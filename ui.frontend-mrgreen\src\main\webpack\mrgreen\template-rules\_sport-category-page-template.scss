.sport-category-page{

    .seo-text-content-component {
        h1 {
            font-weight: 600;
            font-family: Montserrat, verdana, sans-serif;
            font-size: 32px;
            border-bottom: 1px solid #353535;
            padding-bottom: 15px;
            margin: .67em 0;
            @media screen and (max-width: 840px) {
                font-size: 23px;
            }
        }
        h2{
            font-family: Montserrat, verdana, sans-serif;
            font-weight: 700;
            font-size: 14px;
            padding-top: 20px;
            padding-bottom: 10px;
            text-transform: uppercase;
            background: transparent;
        }
        ul li {
            color: #fff;
            font-size: 12px;
            font-weight: 500;
            line-height: 1.5;
            list-style-type: disc;
        }
        .grid-component .column-container .column {
            padding: 0 2vw;
            @media screen and (max-width: 840px) {
                padding: 0 1vw;
            }
        }
    }
}
