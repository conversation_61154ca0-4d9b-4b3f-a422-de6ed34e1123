.grid-slider-component {
    background-color: $black;
    padding: 6vw 10%;
    overflow: hidden;

    @media (max-width: 640px) {
        padding: 0 10%;
    }

    @media only screen and (min-width: 481px) {
        .swiper:has(.twoZigzag) .arrows {
            display: none;
        }
    }

    .swiper {
        position: relative;
        box-sizing: content-box;
        height: 100%;
        overflow: visible;

        .arrows {
            align-self: center;
            width: 9rem;
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            margin-top: 30px;
            margin-left: calc(100% - 9rem);

            @media (max-width: 640px) {
                width: 11rem;
            }

            .swiper-button-prev,
            .swiper-button-next {
                color: white;
                background-color: rgba(255, 255, 255, 0.5);
                padding: 0;
                border-radius: 50%;
                cursor: pointer;
                font-size: 7px;
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border: none;
                text-align: center;
                margin-left: 15px;
                transition: 0.6s ease;
                position: unset;
                margin-top: 0;

                @media (max-width: 640px) {
                    width: 38px;
                    height: 38px;
                }
            }

            .swiper-button-prev:after,
            .swiper-button-next:after {
                display: none;
            }

            .swiper-button-next {
                svg {
                    transform: rotate(180deg);
                }
            }

            @media screen and (min-width: 502px) {
                display: none;
            }
        }

        .swiper-wrapper {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;

            .swiper-slide {
                max-width: 380px;
                flex: 0 0 auto;
                padding: 12px 12px;
                color: $white;
                position: relative;
                z-index: 1;

                @media (max-width: 1440px) {
                    max-width: 290px;
                }

                @media (max-width: $md) {
                    max-width: 240px;
                }

                .rich-text-component .text {
                    h3 {
                        font-family: $font-family-4;
                    }

                    h4 {
                        font-family: $font-family-8;
                    }
                }

                .cta {
                    .cta-template {
                        .label {
                            font-family: $font-family-2;
                        }

                        @media screen and (min-width: 481px) and (max-width: 1185px) {
                            a {
                                line-height: 1;
                            }
                        }
                        @media screen and (max-width: 480px) {
                            a {
                                line-height: 1.5;
                            }
                        }
                    }
                }

                .cta-component {
                    .cta-template {
                        &.cta-wide-size {

                            a {
                                min-width: 230px;
                                padding: 0.5vw 2.5vw;
                                font-size: 1.1vw;

                                @media (max-width: 1480px) {
                                    min-width: 200px;
                                }

                                @media (max-width: $md) {
                                    min-width: 145px;
                                }

                                @media (max-width: 820px) {
                                    min-width: 110px;
                                }

                                @media (max-width: 640px) {
                                    min-width: 222px;
                                    font-size: 16px;
                                    padding: 10px 5px;
                                }

                                @media (max-width: 520px) and (orientation: landscape) {
                                    min-width: 80px;
                                    padding: 0.5vw 2.5vw;
                                    font-size: 1.1vw;
                                }
                            }
                        }

                        &.cta-outline-white {
                            a {
                                border: 3px solid $white;
                            }
                        }
                    }
                }

                @media screen and (min-width: 481px) and (max-width: 767px) {
                    max-width: unset;
                    width: 50%;
                }

                @media screen and (max-width: 480px) {
                    max-width: unset;
                }
            }
            .swiper-slide.with-background::before {
                content: '';
                display: block;
                position: absolute;
                z-index: 0;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='544' height='544' viewBox='0 0 544 544'%3E%3Cdefs%3E%3CradialGradient id='radial-gradient' cx='0.5' cy='0.5' r='0.5' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23366147'/%3E%3Cstop offset='0.251' stop-color='%23366147' stop-opacity='0.792'/%3E%3Cstop offset='1' stop-color='%23366147' stop-opacity='0'/%3E%3C/radialGradient%3E%3C/defs%3E%3Ccircle id='Ellipse_1' data-name='Ellipse 1' cx='272' cy='272' r='272' fill='url(%23radial-gradient)'/%3E%3C/svg%3E");
                pointer-events: none;
                content: '';
                width: 232%;
                height: 232%;
                background-position: center;
                background-size: contain;
                background-repeat: no-repeat;
                left: -66%;
                top: -66%;

                @media screen and (max-width: $md) {
                    width: 182%;
                    height: 182%;
                    left: -45%;
                    top: -45%;
                }

                @media screen and (max-width: $sm) {
                    width: 142%;
                    height: 142%;
                    left: -25%;
                    top: -25%;
                }
            }
        }

        .swiper-wrapper.twoZigzag {
            padding: 35px 0;
            .rich-text-component .text {
                h3 {
                    margin-bottom: 15px;
                    color: $gold;
                    font-family: $font-family-12;
                }

                p {
                    line-height: 150%;
                    color: $gold;
                    font-family: $font-family-12;
                }
            }
            @media only screen and (min-width: 481px) {
                display: block;
                padding: 50px 0;

                .swiper-slide.zigzag-slide {
                    max-width: 100%;
                    width: 100% !important;
                    .grid-component {
                        display: block;
                        .column {
                            display: grid;
                            grid-template-columns: 47.5% 47.5%;
                            grid-column-gap: 5%;
                            grid-row-gap: 3%;

                            .zigzag-grid {
                                &__first {
                                }
                                &__second {
                                    align-content: center;
                                }
                            }
                        }
                    }
                    &:nth-child(2n) {
                        .zigzag-grid {
                            &__first {
                                order: 2;
                            }
                        }
                    }
                }
            }
            @media only screen and (max-width: 480px) {
                .swiper-slide.zigzag-slide {
                    .grid-component {
                        justify-content: center;
                        .column {
                            max-width: 300px;
                            .two-buttons-component .two-buttons__left a,
                            .two-buttons-component .two-buttons__right a {
                                width: 110px;
                                min-width: 110px;
                                height: auto;
                            }

                            .zigzag-grid {
                                &__first {
                                }
                                &__second {
                                }
                            }
                        }
                    }
                }
            }
            .two-buttons-component .two-buttons__left a,
            .two-buttons-component .two-buttons__right a {
                width: 110px;
                min-width: 110px;
                height: auto;
            }
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
            position: relative;

            &.swiper-pagination-disabled {
                display: none;
            }

            .swiper-pagination-bullet {
                display: inline-block;
                width: 40px;
                height: 4px;
                border-radius: 5px;
                background-color: #555;
                margin: 0 5px;
                cursor: pointer;
                opacity: unset;

                &.swiper-pagination-bullet-active {
                    background-color: #fff;
                }
            }
        }
    }
}
