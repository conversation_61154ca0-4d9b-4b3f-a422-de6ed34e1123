.general-page {
    .divider .default {
        margin: 1.5rem 0 0;
    }
    .title h1 {
        font-size: 32px;
        @media screen and (max-width: 840px) {
             h1 {
                font-size: 23px;
                margin-bottom: 15px;
            }
        }
    }
    .rich-text-component{
        h2 {
            color: #05372c;
            font-size: 16px;
            text-transform: uppercase;
            padding: 20px 0 10px;
            margin: 0;
        }
        h2, h3{
            font-family: $font-family;
        }
        p {
            font-family: $font-family-4;
            font-size: 15px;
            line-height: 1.6;
            padding-bottom: 1rem;
            margin-bottom: 0;
            text-rendering: optimizeLegibility;
        }
        .text .TableList tbody tr td:nth-of-type(2n),  .text .table-wrapper tbody tr td:nth-of-type(2n) {
            color: #202020;
        }
        tr td, tbody th, ul li, ul ol{
            font-family: $font-family-4;
        }
        ˙ul li, ol li {
            font-weight:$font-weight-semi-bold;
        }
        a{
            color: #05372c;
        }
    }
}