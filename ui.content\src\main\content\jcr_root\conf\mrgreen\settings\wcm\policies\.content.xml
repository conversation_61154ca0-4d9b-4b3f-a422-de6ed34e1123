<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    xmlns:rep="internal" xmlns:cq="http://www.day.com/jcr/cq/1.0"
    xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:mixinTypes="[rep:AccessControllable]"
    jcr:primaryType="cq:Page">
    <rep:policy />
    <holdings888 jcr:primaryType="nt:unstructured">
        <components jcr:primaryType="nt:unstructured">
            <mrgreen jcr:primaryType="nt:unstructured">
                <editorial jcr:primaryType="nt:unstructured">
                </editorial>
                <structure jcr:primaryType="nt:unstructured">
                    <mrgreen-page jcr:primaryType="nt:unstructured">
                        <policy_1700643170245
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Generic Mr Green Page"
                            sling:resourceType="wcm/core/components/policy/policy">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                        </policy_1700643170245>
                    </mrgreen-page>
                    <mrgreen-xfpage jcr:primaryType="nt:unstructured">
                        <policy
                            jcr:description="Includes the required client libraries."
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Generic Page"
                            sling:resourceType="wcm/core/components/policy/policy">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                        </policy>
                    </mrgreen-xfpage>
                    <mrgreen-client-only-page jcr:primaryType="nt:unstructured">
                        <policy
                            jcr:description=""
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Generic Client-only Page"
                            sling:resourceType="wcm/core/components/policy/policy">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                        </policy>
                    </mrgreen-client-only-page>
                    <mrgreen-client-page jcr:primaryType="nt:unstructured">
                        <policy
                            jcr:description=""
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Generic Client Page"
                            sling:resourceType="wcm/core/components/policy/policy">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                        </policy>
                    </mrgreen-client-page>
                </structure>
                <layout jcr:primaryType="nt:unstructured">
                    <container jcr:primaryType="nt:unstructured">
                        <policy_basic_container
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Mr Green - Basic Container"
                            cq:styleDefaultClasses="basic-container"
                            sling:resourceType="wcm/core/components/policy/policy"
                            components="[group:Mr Green - Editorial,group:Mr Green - Layout,group:Mr Green - Structure]"
                            containerId=""
                            layoutDisabled="false">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                            <cq:authoring jcr:primaryType="nt:unstructured">
                                <assetToComponentMapping jcr:primaryType="nt:unstructured">
                                </assetToComponentMapping>
                            </cq:authoring>
                        </policy_basic_container>
                        <policy_client_page
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Mr Green - Client Page Root Container"
                            sling:resourceType="wcm/core/components/policy/policy"
                            components="[group:Mr Green - Editorial,group:Mr Green - Layout,group:Mr Green - Structure]"
                            containerId="client-page-root"
                            layoutDisabled="false">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                            <cq:authoring jcr:primaryType="nt:unstructured">
                                <assetToComponentMapping jcr:primaryType="nt:unstructured">
                                </assetToComponentMapping>
                            </cq:authoring>
                        </policy_client_page>
                        <policy_30980620151833
                            jcr:primaryType="nt:unstructured"
                            jcr:title="XF Root"
                            sling:resourceType="wcm/core/components/policy/policy"
                            components="[group:Mr Green - Editorial,group:Mr Green - Fragment,group:Mr Green - Layout,group:Mr Green - Structure]"
                            layoutDisabled="false">
                            <jcr:content jcr:primaryType="nt:unstructured" />
                        </policy_30980620151833>
                        <policy_1749022092040
                            cq:styleDefaultClasses="basic-container"
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Mr Green - Sport Category Container"
                            sling:resourceType="wcm/core/components/policy/policy"
                            components="[group:Mr Green - Editorial,/apps/holdings888/components/mrgreen/fragments/seo-content-container,group:Mr Green - Layout,group:Mr Green - Structure]"
                            layoutDisabled="false">
                            <jcr:content jcr:primaryType="nt:unstructured"/>
                            <cq:authoring jcr:primaryType="nt:unstructured">
                                <assetToComponentMapping jcr:primaryType="nt:unstructured"/>
                            </cq:authoring>
                        </policy_1749022092040>
                    </container>
                    <grid jcr:primaryType="nt:unstructured">
                    </grid>
                </layout>
            </mrgreen>
        </components>
    </holdings888>
</jcr:root>
