@mixin cta-color-variant($color-par, $bg-color-par, $border-color-par, $hover-color-par, $hover-bg-par, $font-size-par, $padding-par) {
  a {
    background: $bg-color-par;
    color: $color-par;
    text-transform: uppercase;
    border: 2px solid $border-color-par;
    font-size: $font-size-par;
    text-decoration: none;
    border-radius: 4px;

    @extend #{$padding-par};

    // this rule override default .root a:hover 021 specificity with 031 specificity
    &:hover, &:focus {
      background: $hover-bg-par;
      color: $hover-color-par;
    }

    @media (hover: hover) {
      &:hover, &:focus {
        background: $hover-bg-par;
        color: $hover-color-par;
      }
    }
  }
}

.cta-template {
  a {
    display: flex;
    justify-content: center;
    position: relative;
    text-align: center;
    word-wrap: break-word;
    word-break: break-word;
    transition: all;
    transition-duration: .3s;
    transition-timing-function: ease-out;
    border-radius: $border-radius;
    font-weight: $font-weight-bold;
    cursor: pointer;
    line-height: $line-height-normal;
    min-width: 160px;
    overflow: hidden;
  }

  // CTA PRIMARY
  &.cta-primary {
    @include cta-color-variant($cta-primary-text, $cta-primary-bg, $cta-primary-border-color, $cta-primary-hover-text, $cta-primary-bg-hover, $font-size-16, '.cta-padding-default');
  }

  &.cta-primary-v2 {
    @include cta-color-variant($cta-primary-v2-text, $cta-primary-v2-bg, $cta-primary-v2-border-color, $cta-primary-v2-hover-text, $cta-primary-v2-bg-hover, $font-size-16, '.cta-padding-default');
  }

  // CTA SECONDARY
  &.cta-secondary {
    @include cta-color-variant($cta-secondary-text, $cta-secondary-bg, $cta-secondary-border-color, $cta-secondary-hover-text, $cta-secondary-bg-hover, $font-size-16, '.cta-padding-default');
  }

  // CTA SECONDARY v1
  &.cta-secondary-variant-1 {
    @include cta-color-variant($cta-secondary-variant-1-text, $cta-secondary-variant-1-bg, $cta-secondary-variant-1-border-color, $cta-secondary-variant-1-hover-text, $cta-secondary-variant-1-bg-hover, $font-size-16, '.cta-padding-default');
  }

  // CTA NO Glow
  &.cta-no-glow {
   @include cta-color-variant($cta-noglow-text, $cta-noglow-bg, $cta-noglow-border-color, $cta-noglow-hover-text, $cta-noglow-bg-hover, $font-size-16, '.cta-padding-default');
  }
  
  // CTA GLOW
  &.cta-glow {
    @include cta-color-variant($cta-glow-text, $cta-glow-bg, $cta-glow-border-color, $cta-glow-hover-text, $cta-glow-bg-hover, $font-size-7, '.cta-padding-default');
    a {
      animation: glowing .9s infinite alternate;
    }
  }
}

//CTA UTILITIES
.cta-padding-small {
  padding: .4rem 1.6rem;
}

.cta-padding-default {
  padding: .8rem 4rem;
}

@keyframes glowing {
  100% {
    box-shadow: 0 0 20px 5px $glow-1;
  }
}

@keyframes color-rotation {
  0% {
    filter: hue-rotate(0deg);
  }

  50% {
    filter: hue-rotate(25deg);
  }
}

.cta-template{
  &.cta-wide-size a{
    min-width: 290px;
    line-height: $line-height;
  }

  &.cta-medium-size a{
    line-height: $line-height;
    padding: 0.9rem 0;
    max-width: 290px;
    min-width: 160px;
  }
  &.cta-large-size a{
    line-height: 2;
    padding: 0.75rem 2.25rem;
    font-family: $font-family-6;
    font-size: 19px;
    max-width: 290px;
    min-width: 160px;
    @media screen and (max-width: 1024px) {
      min-width: 215px;
    }
    @media screen and (max-width: 414px) {
      min-width: 170px;
      padding: 0.75rem 0;
    }
  }

  &.cta-fullwidth-size, &.cta-fullwidth-size a {
    width: 100%;
    line-height: $line-height-double;
    font-size: $font-size-17;
  }
}
