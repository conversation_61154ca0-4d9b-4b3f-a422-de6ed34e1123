<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"/>
<sly data-sly-use.logoItems="${'holdings888/utils/multifield.js' @ multifieldName='listItems'}"/>
<sly data-sly-test.hasContent="${logoItems}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<sly data-sly-include="${'clientlibs.html'}"/>

<div data-sly-test="${hasContent}" class="header-bar ${wcmmode.edit? 'edit-mode' : ''}">
    <div class="header-bar-light-lp-components">
        <div class="header-bar-light-lp-components__img" data-sly-list.logo="${logoItems}">
            <sly data-sly-set.linkClasses="${logo.properties.regulationLogo ? 'regulation-logo' : ''} ${logo.properties.mediumLogo ? 'medium-logo' : ''}"/>
            <sly data-sly-call="${link.default @
                                properties=logo.properties,
                                linkClasses=linkClasses,
                                linkUrlName='mainLogoLink',
                                imagePath=logo.properties.mainLogoImageReference,
                                imageAlt=logo.properties.imageAlt,
                                imageLoading=logo.properties.isFetchPriorityHigh,
                                scriptName='linkScript'
                                }"/>
        </div>
        <div class="header-bar-light-lp-components__cta" data-sly-test.hasCta="${properties.label && properties.url}">
            <div class="cta-component d-flex ${properties.flexAlignment} ${properties.paddingTop} ${properties.paddingBottom}"
                data-mbox-id="${properties.mboxId}">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.url}" />
                <sly
                    data-sly-call="${cta.default @
                        label       = properties.label,
                        ctaScript   = properties.secondScript,
                        fontWeight  = properties.fontSize,
                        ariaLabel   = properties.ariaLabel,
                        url         = ctaLink.relativePublishLink,
                        newWindow   = properties.newWindow,
                        ctaType     = properties.type,
                        ctaSize     = properties.ctaSize,
                        ctaAddsCut  = ctaLink.addsCut}" />
            </div>
        </div>
    </div>
</div>
