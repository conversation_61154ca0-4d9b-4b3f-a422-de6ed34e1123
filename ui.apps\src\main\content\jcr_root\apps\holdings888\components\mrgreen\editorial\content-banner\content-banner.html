<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}" />

<div
    class="content-banner ${properties.paddingTop} ${properties.paddingBottom} ${properties.splitContentMobile ? 'mobile-split-content' : ''}"
    data-mbox-id="${properties.mboxId}">
    <div
        class="image-pc"
        style="background-image: url('${properties.imageDesktopPathReference @ context='unsafe'}')"></div>
    <div
        class="image-tablet"
        style="background-image: url('${properties.imageTabletPathReference @ context='unsafe'}')"></div>
    <div
        class="image-mobile"
        style="background-image: url('${properties.imageMobilePathReference @ context='unsafe'}')"></div>
    <div class="content-banner-wrapper">
        <div class="text-container">
            <div
                class="content-info"
                data-contentbannertitle="${properties.bannerTitle}">
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text=properties.textForDesktop}" />
                <div class="rich-text">${richText.text @ context='html'}</div>
            </div>
            <div
                onmousedown="return false;"
                onselectstart="return false;"
                data-sly-test="${properties.url && properties.label}"
                class="cta-component d-flex ${properties.flexAlignment} ${properties.marginTopCTA} ${properties.marginBottomCTA} ${properties.enableMobileButton ? 'cta-display-mobile' : ''}"
                data-sly-test="${properties.enableButton}">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess= properties.url}" />
                <sly
                    data-sly-call="${cta.default @
                                    label       = properties.label,
                                    fontWeight  = properties.fontSize,
                                    ariaLabel   = properties.ariaLabel,
                                    url         = ctaLink.relativePublishLink,
                                    newWindow   = properties.newWindow,
                                    ctaType     = properties.type,
                                    ctaScript   = properties.ctaScript,
                                    ctaSize    = properties.ctaSize,
                                    ctaAddsCut  = ctaLink.addsCut}" />

            </div>
            <sly data-sly-resource="${'two-cta-apps' @ resourceType='holdings888/components/mrgreen/editorial/two-buttons', decorationTagName='div'}"/>
        </div>
    </div>
</div>

