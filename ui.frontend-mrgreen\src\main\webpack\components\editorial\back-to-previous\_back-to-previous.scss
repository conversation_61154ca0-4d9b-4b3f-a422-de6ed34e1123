.back-to-previous-component {
    font-size: $font-size;

    .promotion-page__back-to-btn {
        position: absolute;
        left: 4em;
        top: 6em;
        z-index: 10;
        opacity: 0.9;
        border-radius: 2em;
        transition: all 0.25s ease-out;
        background: transparent;
        padding: 0;
        border: 0;
        line-height: 1;
        margin-top: 0em;

        @media (max-width: calc($md-grid - 1px)) {
            top: 14em;
            left: 2em;
        }

        @media (max-width: 500px) {
            top: 10em;
        }

        &:hover {
            transform: scale(1.025);
        }

        &:active {
            transform: scale(0.95);
        }

        a {
            width: 4rem;
            height: 4rem;
            border-radius: 2rem;
            border: .1rem solid $anchor-menu-divider;
            background: $black-1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        svg {
            width: 2.4rem;
            height: 2.4rem;
            color: gray;
        }
        svg g {
            width: 2.4rem;
            height: 2.4rem;
            color: gray;
        }
    }
}

.container:has(.same-img-desk-mob) {
    .back-to-previous-component .promotion-page__back-to-btn {
        @media (max-width: $sm-grid) {
            margin-top: 0;
        }
    }
}