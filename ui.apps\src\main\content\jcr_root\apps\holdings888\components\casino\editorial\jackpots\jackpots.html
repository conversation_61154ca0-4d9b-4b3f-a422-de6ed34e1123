<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='listItems'}" />
<sly data-sly-use.additionalListItemsJp="${'holdings888/utils/multifield.js' @ multifieldName='additionalListItemsJp'}" />
<sly data-sly-test.hasContent="${properties.ctaLabel}" />
<sly data-sly-test.notShowTitle="${properties.notShowTitle}" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}" />


<div class="jackpot-component-container" data-jp-currency="${properties.apiCurrencyJp}" data-jp-brand="${properties.apiBrandJp}"
     data-jp-currency-sign="${properties.currencySignJp}" data-jp-currency-after-amount="${properties.currencyAfterAmount}">
    <div class="title" data-sly-test="${notShowTitle}">
        <div class="jackpot-container-title">
            <sly data-sly-resource="${'title' @ resourceType='holdings888/components/casino/editorial/title'}"/>
        </div>
    </div>
    <div class="jackpot-jp-games ${properties.hideGameNames ? 'hide-name' : ' '} ${properties.gameNameAbove ? 'game-name-above' : ' '} ${properties.sameFont ? 'same-font' : ' '} img-${properties.imageSize} ${properties.alignLeft ? 'games-align-left' : ' '}" >
        <div class="container-wrapper">
            <sly data-sly-list="${additionalListItemsJp}">
                <sly data-sly-use.gameLink="${'holdings888.core.models.LinkModel' @ urlToProcess=item.properties.additionalDestinationUrlJp}" />
                <div class="jackpot-container additional-games" data-jp-redirection="${gameLink.relativePublishLink @ context='unsafe'}">
                    <sly data-sly-use.additionalGameIcon="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=item.properties.additionalGameIconJp}"/>
                    <sly data-sly-set.additionalGameIconJp="${additionalGameIcon.renditions['webp'].path || additionalGameIcon.renditions['original'].path}"/>
                    <img
                        class="jackpot-card"
                        src="${additionalGameIconJp}"
                        loading="lazy"
                        alt="${item.properties.additionalGameIconAltJp}" />
                    <div class="game-jp-info">
                        <sly data-sly-test="${item.properties.additionalGameNameJp}">
                            <span class="game-jp-name">${item.properties.additionalGameNameJp}</span>
                        </sly>
                    </div>
                </div>
            </sly>
        </div>
    </div>
    <div
        class="cta-container"
        data-sly-test="${hasContent}">
        <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.ctaUrl}" />

        <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScriptExtra}" />

        <div class="cta-template ${properties.typeExtra ? properties.typeExtra : 'cta-glow'}">
            <a
                href="${ctaLink.relativePublishLink @ context='unsafe'}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}"
                data-sly-attribute.aria-label="${properties.ctaAriaLabel}"
                data-sly-attribute.target="${properties.ctaNewWindow ? '_blank':''}"
                data-sly-attribute="${attributes ? attributes : autoList.getAttributes}">
                <span>${properties.ctaLabel}</span>
                <sly data-sly-test="${properties.typeExtra == 'cta-blog-grid'}">
                    <span class="blog-arrow right"></span>
                </sly>
            </a>
        </div>
    </div>
</div>

<div
    class="game-jp-configurations"
    data-sly-list="${listItems}"
    style="display: none">
    <sly data-sly-use.gameApiLink="${'holdings888.core.models.LinkModel' @ urlToProcess=item.properties.destinationUrlJp}" />
    <sly data-sly-use.gameIcon="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=item.properties.gameIconJp}"/>
    <sly data-sly-set.gameIconJp="${gameIcon.renditions['webp'].path || gameIcon.renditions['original'].path}"/>
    <div
        class="game-jp-configuration"
        data-jp-game-id="${item.properties.gameIdJp}"
        data-jp-icon="${gameIconJp}"
        data-jp-icon-alt="${item.properties.gameIconAltJp}"
        data-jp-destination-url="${gameApiLink.relativePublishLink @ context='unsafe'}"></div>
</div>
