<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html" />
<sly data-sly-call="${clientlibs.fe @ locations='mrgreen-sidenav-css'}"/>
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />

<sly data-sly-use.subSidenav="${'holdings888/utils/multifield.js' @ multifieldName='subSidenavItem'}" />
<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html" />
<sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
<sly data-sly-test.hasContent="${properties.sidenavLogoImageReference}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<sly data-sly-test="${hasContent}">
    <div class="header-bar-light-lp-components__sidenav-menu">
         <sly
                        data-sly-call="${link.default @
                                properties=properties,
                                linkClasses='sidenav-image',
                                imagePath=properties.sidenavLogoImageReference,
                                imageAlt=properties.sidenavimageAlt,
                                imageLoading=properties.isFetchPriorityHigh
                                }" />
        </div>
    </sly>
      <sly
                        data-sly-call="${link.default @
                                properties=properties,
                                linkClasses='logo-image',
                                linkUrlName='logoUrl',
                                imagePath=properties.mainLogoImageReference,
                                imageAlt=properties.imageAlt,
                                imageLoading=properties.isFetchPriorityHigh,
                                scriptName='linkScript'
                                }" />
      
    <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.ctaUrl}" />
    <sly
        data-sly-call="${cta.default @
                        label       = properties.label,
                        ctaScript   = properties.secondScript,
                        fontWeight  = properties.fontSize,
                        ariaLabel   = properties.ariaLabel,
                        url         = ctaLink.relativePublishLink,
                        newWindow   = properties.newWindow,
                        ctaType     = properties.type,
                        ctaSize     = properties.ctaSize,
                        ctaAddsCut  = ctaLink.addsCut}" />
<div class="sidenav-wrapper">
    <a class="closebtn">×</a>
    <div class="sidenav-popup">
        <ul>
            <sly data-sly-list.item="${subSidenav}">
                <li>

                    <sly data-sly-use.ctaLink2="${'holdings888.core.models.LinkModel' @ urlToProcess=item.properties.subSidenavItemLink}" />
                    <sly
                        data-sly-call="${link.imageWithText @
                                properties=item.properties,
                                linkClasses=linkClasses,
                                linkUrlName='subSidenavItemLink',
                                imagePath=item.properties.iconImageReference,
                                imageAlt=item.properties.imageAlt,
                                imageLoading=item.properties.isFetchPriorityHigh,
                                scriptName='linkScript',
                                linkLabelName='subSidenavItemText',
                                labelClasses='sidenav-item-label'
                                }" />
                </li>
            </sly>
        </ul>
    </div>
</div>
<sly data-sly-call="${clientlibs.fe @ locations='mrgreen-sidenav-js'}"/>
</sly>