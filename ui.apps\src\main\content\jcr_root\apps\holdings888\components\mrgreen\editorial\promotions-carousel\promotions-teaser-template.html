<template
    data-sly-template.default="${@ TopText, topLinkText, topLink, imagePath, imageAlt, bottomText,topCtaText, topCtaLink, topCtaScript, bottomCtaGlowing, bottomCtaText, bottomCtaLink, bottomCtaScript, link, category, mboxId, bottomLinkText, disclaimerLink, extraText, additionalTextOptions, freeRichText}">
    <sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
    <sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
    <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess = properties.topLinkScript}" />
    <sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
    <sly data-sly-set.ctaTypeBottom="${bottomCtaGlowing ? 'cta-glow' : 'cta-primary'}" />

    <div
        data-mbox-id="${properties.mboxId}"
        style="display: contents">
        <div
            class="promotions-teaser-component ${wcmmode.edit? 'edit-mode' : ''}"
            id="promotions-teaser-component-${category}">
            <div class="promotions-teaser-wrapper ${wcmmode.edit ? 'edit-mode' : ''}">
                <div class="promotions-teaser__top">
                    <div class="promotions-teaser__top__title">${TopText}</div>
                    <a
                        class="promotions-teaser__top__link"
                        href="${topLink @ context='unsafe'}"
                        onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                        ${topLinkText}
                    </a>
                </div>
                <div class="promotions-teaser__img">
                    <sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html" />
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=imageAlt}" />
                </div>
                <div class="promotions-teaser__bot">
                    <sly data-sly-use.bottomrichtexteditor="${'holdings888.core.models.RichTextImp' @ text=bottomText}"/>
                    <div class="promotions-teaser__bot__text">
                        ${bottomrichtexteditor.text @ context='html'}
                    </div>
                    <div class="promotions-teaser__bot__cta-section">
                        <div
                            onmousedown="return false;"
                            onselectstart="return false;"
                            class="cta-component d-flex ${properties.flexAlignment} ${properties.paddingTop} ${properties.paddingBottom}"
                            data-mbox-id="${properties.mboxId}">
                            <sly data-sly-use.ctaLinkTop="${'holdings888.core.models.LinkModel' @ urlToProcess=topCtaLink}" />
                            <sly
                                data-sly-call="${cta.default @
                label       = topCtaText,
                fontWeight  = properties.fontSize,
                ariaLabel   = properties.ariaLabelTop,
                url         = ctaLinkTop.relativePublishLink,
                newWindow   = '',
                ctaType     = 'cta-secondary',
                ctaScript   = topCtaScript,
                ctaSize    = '',
                ctaAddsCut  = ctaLinkTop.addsCut}" />
                            <sly data-sly-test="${bottomCtaText}">
                                <sly data-sly-use.ctaLinkBottom="${'holdings888.core.models.LinkModel' @ urlToProcess=bottomCtaLink}" />
                                <sly
                                    data-sly-call="${cta.default @
                label       = bottomCtaText,
                fontWeight  = properties.fontSize,
                ariaLabel   = properties.ariaLabelBottom,
                url         = ctaLinkBottom.relativePublishLink,
                newWindow   = '',
                ctaType     = ctaTypeBottom,
                ctaScript   = bottomCtaScript,
                ctaSize    = '',
                ctaAddsCut  = ctaLinkBottom.addsCut}" />
                            </sly>
                        </div>
                    </div>
                </div>
                <sly data-sly-test="${additionalTextOptions == 'AdditionalTextAfterLink'}">
                    <div class="disclaimer">
                        <a
                            class="promotions-teaser__disclaimer__link"
                            href="${disclaimerLink @ context='unsafe'}"
                            onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                            ${bottomLinkText}
                        </a>
                        ${extraText}&nbsp
                    </div> </sly
                ><sly data-sly-test="${additionalTextOptions == 'AdditionalTextBeforeLink'}">
                    <div class="disclaimer">
                        &nbsp${extraText}
                        <a
                            class="promotions-teaser__disclaimer__link"
                            href="${disclaimerLink @ context='unsafe'}"
                            onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                            ${bottomLinkText}
                        </a>
                    </div>
                </sly>   
                <sly data-sly-test="${additionalTextOptions == 'FreeRichText'}">
                    <sly data-sly-use.richtexteditor="${'holdings888.core.models.RichTextImp' @ text=freeRichText}"/>
                    <div class="disclaimer">${richtexteditor.text @ context='html'}</div>
                </sly>               
            </div>
        </div>
    </div>
</template>
