<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='games'}" />
<sly data-sly-test.hasContent="${listItems}" />

<!--/* JSON output for games */-->
<sly data-sly-test="${hasContent}">
[
<sly data-sly-list.game="${listItems}">
  {
    "game_id": ${game.properties.gameId @ context='number'},
    "game_name": "${game.properties.gameName @ context='json'}",
    "image_path": "${game.properties.imagePath @ context='json'}"
  }<sly data-sly-test="${!gameLast}">,</sly>
</sly>
]
</sly>

<!--/* Placeholder for edit mode */-->
<sly data-sly-test="${!hasContent && wcmmode.edit}">
    <div class="cq-placeholder" data-emptytext="Configure games in the dialog"></div>
</sly>
