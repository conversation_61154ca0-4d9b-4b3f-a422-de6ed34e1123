document.addEventListener("DOMContentLoaded", function() {
    var lazyBackgrounds = [].slice.call(document.querySelectorAll(".custom-banner"));

    if (lazyBackgrounds) {
        let hasOffer = false;
        lazyBackgrounds.forEach($el => {
            let ctaContaner = $el.querySelector('.cta-button-container');
            if (ctaContaner.classList.contains("has-offer")) {
                hasOffer = true;
            }

            if (hasOffer) {
                ctaContaner.classList.add("has-offer");
            }

            removeEmptyElement($el);
            getMobileOperatingSystem($el);
        });
    }

    function getMobileOperatingSystem($el) {
        const androidImg = $el.querySelector('.additional-image-android');
        const iosImg = $el.querySelector('.additional-image-ios');
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;

        // Android detection
        if (/android/i.test(userAgent)) {
            if (androidImg) {
                androidImg.classList.add("active");
            }
        }

        // iOS detection
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            if (iosImg) {
                iosImg.classList.add("active");
            }
        }
    }

    function updateBackground(lazyBackground) {
        let bgImageDesktop = lazyBackground.getAttribute('data-background-desktop');
        let bgImageMobile = lazyBackground.getAttribute('data-background-mobile');
        let isLandscape = window.matchMedia("(orientation: landscape)").matches;
        let isDesktopMode = window.matchMedia('screen and (orientation: landscape) and (min-width: 500px) and (max-width: 1080px) and (hover: none)').matches;
        let isSmallPortrait = window.matchMedia('screen and (orientation:landscape) and (min-width: 500px) and (max-width: 1080px) and (max-height: 400px)').matches;
        let bgImage = isDesktopMode || isSmallPortrait || window.innerWidth >= 1081 ? bgImageDesktop : bgImageMobile;
        if (bgImage) {
            lazyBackground.style.backgroundImage = "url(" + bgImage + ")";
        }
    }

    function observeBackgrounds() {
        let observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    updateBackground(entry.target);
                }
            });
        });

        lazyBackgrounds.forEach(function(lazyBackground) {
            observer.observe(lazyBackground);
        });
    }

    if ("IntersectionObserver" in window) {
        observeBackgrounds();
    } else {
        lazyBackgrounds.forEach(updateBackground);
    }

    function handleResizeAndOrientationChange() {
        lazyBackgrounds.forEach(updateBackground);
    }

    window.addEventListener('resize', handleResizeAndOrientationChange);
    window.addEventListener('orientationchange', handleResizeAndOrientationChange);
    
    var clickableImages = document.querySelectorAll('.clickable-image');
    clickableImages.forEach(function(image) {
        image.addEventListener('click', function() {
            var url = image.getAttribute('data-redirect-url');
            if (url) {
                window.open(url, '_blank');
            }
        });
    });

    let threeElementsContainer = document.querySelector('.three-elements-container');
    let ctaButtonContainer = document.querySelector('.cta-button-container');
    let originalParent = threeElementsContainer ? threeElementsContainer.parentElement : null;
    let hasMoved = false;

    function moveThreeElementsContainer() {
        if (threeElementsContainer && ctaButtonContainer && !hasMoved && !window.matchMedia("screen and (orientation: landscape) and (min-width: 500px) and (max-width: 1080px) and (hover: none)").matches) {
            ctaButtonContainer.insertBefore(threeElementsContainer, ctaButtonContainer.firstChild);
            hasMoved = true;
        }
    }

    function returnThreeElementsContainer() {
        if (threeElementsContainer && originalParent && hasMoved) {
            originalParent.insertBefore(threeElementsContainer, originalParent.firstChild);
            hasMoved = false;
        }
    }

    function handleResize() {
        if (window.matchMedia("screen and (orientation: landscape) and (min-width: 500px) and (max-width: 1080px) and (hover: none)").matches) {
            returnThreeElementsContainer();
        } else if (window.innerWidth <= 1080) {
            moveThreeElementsContainer();
        } else {
            returnThreeElementsContainer();
        }
    }

    function removeEmptyElement($banner) {
        $banner.querySelectorAll(".cmp-three-elm__step-text").forEach($text => {
            for (const child of $text.children) {
                if (child.innerHTML === "&nbsp;") {
                    child.style.display = "none";
                }
              }
        });
    }

    window.addEventListener('resize', handleResize);
    handleResize();
});
