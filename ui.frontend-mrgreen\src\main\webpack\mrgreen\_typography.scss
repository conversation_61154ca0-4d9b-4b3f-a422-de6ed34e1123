// Typography
.root {
    -moz-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;

    // Titles
    h1, .h1 {
        font-size: $font-size-h1-mobile;
        line-height: $line-height-h1;
        font-weight: $font-weight-h1;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h1;
        }
    }

    h2, .h2 {
        font-size: $font-size-h2-mobile;
        line-height: $line-height-h2;
        font-weight: $font-weight-h2;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h2;
        }
    }

    h3, .h3 {
        font-size: $font-size-h3-mobile;
        line-height: $line-height-h3;
        font-weight: $font-weight-h3;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h3;
        }
    }

    h4, .h4 {
        font-size: $font-size-h4-mobile;
        line-height: $line-height-h4;
        font-weight: $font-weight-h4;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h4;
        }
    }

    h5, .h5 {
        font-size: $font-size-h5-mobile;
        line-height: $line-height-h5;
        font-weight: $font-weight-h5;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h5;
        }
    }

    h6, .h6 {
        font-size: $font-size-h6-mobile;
        line-height: $line-height-h6;
        font-weight: $font-weight-h6;
        margin-bottom: 0;

        @media (min-width: $md) {
            font-size: $font-size-h6;
        }
    }

    // Paragraph
    p {
        font-size: $font-size-mobile;
        line-height: $line-height-custom;

        @media (min-width: $md) {
            font-size: $font-size-root;
        }
    }

    .thin {
        font-weight: $font-weight-thin;
    }
    .light {
        font-weight: $font-weight-light;
    }
    .normal {
        font-weight: $font-weight-normal;
    }
    .regular {
        font-weight: $font-weight-regular;
    }
    .demibold {
        font-weight: $font-weight-semi-bold;
    }
    .bold {
        font-weight: $font-weight-bold;
    }
    .extraBold {
        font-weight: $font-weight-medium-bold;
    }
    .black {
        font-weight: $font-weight-extra-bold;
    }
     b, strong {
        font-weight: $font-weight-bold;
    }
}