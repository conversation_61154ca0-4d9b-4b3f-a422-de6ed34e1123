<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${properties.fileReference}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>
<sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html"/>
<sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.clickScript}" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}"/>

<div data-sly-test="${hasContent}" class="live-game-tile-container">

     <div class="live-game-tile-item"
          data-sly-attribute="${attributes ? attributes : autoList.getAttributes}" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
          <sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"/>
          <sly data-sly-call="${link.default @
             properties=properties,
             linkUrlName='linkImage',
             imagePath=properties.fileReference,
             imageAlt=properties.alt
             }"/>
     </div>

     <div data-sly-test="${properties.text}" class="live-game-tile-teaser">
          <span>${properties.text}</span>
     </div>
    
</div>
