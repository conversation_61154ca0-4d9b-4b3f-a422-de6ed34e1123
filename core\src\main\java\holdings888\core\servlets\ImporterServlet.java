package holdings888.core.servlets;

import com.day.cq.wcm.api.Page;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import holdings888.core.servlets.infrastracture.Const;
import holdings888.core.servlets.infrastracture.PageHandler;
import holdings888.core.servlets.infrastracture.Utils;
import holdings888.core.servlets.pageTemplateMap.CasinoPageTempateDeduction;
import lombok.extern.slf4j.Slf4j;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.ModifiableValueMap;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ResourceResolverFactory;
import org.apache.sling.api.servlets.SlingAllMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jcr.Session;
import javax.servlet.Servlet;
import javax.servlet.ServletException;
import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Servlet to save data in AEM",
        "sling.servlet.paths=" + "/bin/pages" })

public class ImporterServlet extends SlingAllMethodsServlet {

    private static final long serialVersionUID = 1L;

    @Reference
    transient private ResourceResolverFactory resolverFactory;
    // private ResourceResolverFactory resolverFactory;

    @Override
    protected void doGet(SlingHttpServletRequest request, SlingHttpServletResponse response) {
        return;
    }

    @Override
    protected void doPost(SlingHttpServletRequest request, SlingHttpServletResponse response) throws ServletException,
            IOException {
        /*
         * add logs/importServlet.log in http://localhost:4502/system/console/slinglog
         */
        log.info("[ImporterServlet] - request from - user {}", request.getResourceResolver().getUserID());

        HashMap<String, Object> param = new HashMap<>();
        // added map to content-reader-service ->
        // holdings888.core:importservice=content-writer-service
        param.put(ResourceResolverFactory.SUBSERVICE, "account-service-888"); // importservice is my System User.

        // object for response
        JsonArray outputArray = new JsonArray();
        JsonObject outputObject = new JsonObject();
        Gson gson = new Gson();
        try (ResourceResolver resolver = resolverFactory.getServiceResourceResolver(param)) {
            Session session = resolver.adaptTo(Session.class);
            outputObject = new JsonObject();

            try {
                JsonObject json = gson.fromJson(request.getReader(), JsonObject.class);
                JsonElement articalData = json.get(Const.JSON.articalData);

                String parentPath = Utils.getParentPathFromAttributeUrl(json);
                String category = Utils.getAttrValue(articalData.getAsJsonObject(),
                        Const.JSON.categoryName);
                if ("".equals(category)) {
                    category = parentPath;
                } else if (parentPath.contains(Const.JSON.magazine)) {
                    category = Const.JSON.magazine + "/" + category + "/";
                } else if (parentPath.contains(Const.JSON.blog)) {
                    category = Const.JSON.blog + "/" + category + "/";
                } else if (parentPath.contains(Const.JSON.blogg)) {
                    category = Const.JSON.blogg + "/" + category + "/";
                }

                Page page = PageHandler.CreatePage(
                        category,
                        Utils.getAttrValue(json, Const.JSON.pageTemplateKey),
                        Utils.getAttrValue(json, Const.JSON.pageNameKey),
                        resolver,
                        outputObject,
                        null);

                if (page != null) {
                    // get page components list from JSON
                    JsonElement componentList = json.get(Const.JSON.pageComponentListKey);
                    JsonElement metaData = json.get(Const.JSON.metaData);
                    // JsonElement articalData = json.get(Const.JSON.articalData);

                    JsonObject jsonObj = componentList.getAsJsonObject();
                    JsonObject jsonMetaObj = metaData.getAsJsonObject();
                    JsonObject articalDataObj = articalData.getAsJsonObject();

                    Set<Map.Entry<String, JsonElement>> allEntries = jsonObj.entrySet();

                    for (Map.Entry<String, JsonElement> entry : articalDataObj.entrySet()) {
                        jsonMetaObj.add(entry.getKey(), entry.getValue());
                    }

                    Set<Map.Entry<String, JsonElement>> allMetaEntries = jsonMetaObj.entrySet();

                    // add page components base on page tamplate
                    CasinoPageTempateDeduction.deduct(Utils.getAttrValue(json, Const.JSON.pageTemplateKey),
                            page,
                            allEntries, session, resolver);
                    Resource pagResource = page.getContentResource();
                    if (Objects.nonNull(pagResource)) {
                        ModifiableValueMap map = pagResource.adaptTo(ModifiableValueMap.class);

                        // add metadata entries to map
                        PageHandler.addPageProperty(allMetaEntries, map);
                        String navTitle = Utils.getAttrValue(json, Const.JSON.navTitle);
                        String pageTitle = Utils.getAttrValue(json, Const.JSON.pageTitle);
                        String pageCanonicalUrl = Utils.getAttrValue(json, Const.JSON.canonicalUrl);
                        String pageRobotTag[] = Utils.getAttrValue(json, Const.JSON.robotTag).split(", ");

                        map.put(Const.JSON.navtitle, navTitle);
                        map.put(Const.JSON.pageTitleKey, pageTitle);
                        map.put(Const.JSON.canonicalUrlKey, pageCanonicalUrl);
                        map.put(Const.JSON.robotTagKey, pageRobotTag);

                        resolver.commit();
                    }

                } else {
                    // set output data for exists page
                    outputObject.addProperty(Utils.getAttrValue(json, Const.JSON.pageNameKey),
                            Const.Instance.rootResource
                                    + new URL(Utils.getAttrValue(json, Const.JSON.pageUrlKey)).getPath());
                    outputObject.addProperty(Const.Status.status, Const.Status.exist);
                    outputObject.addProperty(Const.Message.statusMessage, Const.Message.pageEsixts);
                }
                outputArray.add(outputObject);

            } catch (Exception e) {
                Utils.errorHandler(outputObject, outputArray, e, e.getMessage());
            }

        } catch (Exception e) {

            Utils.errorHandler(outputObject, outputArray, e, e.getMessage());
        }
        response.setContentType("application/json");
        response.getWriter().write(outputArray.toString());
    }

}
