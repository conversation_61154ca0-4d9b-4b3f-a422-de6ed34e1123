.article-slider-component-xf:not(.main-container .article-slider-component-xf) {
  padding: 2rem 1rem 1rem 4rem;
}


.article-slider-component-xf {
   padding: 1rem 0 0 0;
  .internalLinksStyle {
    .boxTitle {
      display: none;
    }

    .article-slider-container-xf {
      position: relative;
      margin: 2rem auto 3.5rem;
      padding-top: 3rem;

      @media (max-width: $lg) {
        border-top: 0.1rem solid $black;
      }

    }

    .article-slider-container-xf::before {
      content: "";
      background: url("../resources/images/icons/shadow-bg-carousel.png") no-repeat;
      width: 100%;
      height: 35rem;
      bottom: -14.1rem;
      left: 0;
      position: absolute;
      background-position: top;
      background-size: contain;

      @media (max-width: $lg) {
        top: 0.1rem;
        border-top: 0.1rem solid $article-slider-border;
      }

    }


    .swiper-button-prev-xf {
      margin-top: 3rem;
    }

    .swiper-button-next-xf {
      margin-top: 3rem;
    }
  }

  .relatedArticlesStyle {
    .boxTitle {
      background: linear-gradient(90deg, $black, hsla(0,0%,100%,0) 75%);
      padding: 0.3em calc(1em - 0.2rem);
      border-left: 0.4rem solid $green;
      margin-top: 1.5em;
      margin-bottom: 1em;
      font-size: 1.6rem;
      font-weight: 600;
      color: $green-2;
    }

    .article-slider-container-xf {
      position: relative;
      margin: 2rem auto 3.5rem;
      padding-top: 3rem;
      border-top: 0.1rem solid $article-slider-border;
    }
  }

  .article-slider-container-xf {
    a {
      text-decoration: none;
      color: $white;

      &:hover {
        color: $white;
      }
    }

    .slide-img-xf, .slide-text-xf {
      width: 20.25rem;
    }

    .slide-img-xf {
      height: auto;
      display: inline-block;

      img {
        width: 100%;
      }
    }

    .slide-text-xf {
      padding: .8rem 0;
      display: block;
      text-align: center;
    }

  }

  .swiper-xf {
    width: 100%;
    max-width: 114rem;
    margin: 1.25rem auto;
    overflow-x: hidden;

    @media (max-width: 1200px) {
      max-width: 90.5rem;
    }

    @media (max-width: $sm-air) {
      max-width: 67rem;
    }

    @media (max-width: $sm) {
      max-width: 21rem;
    }

    .wrapper-xf {
      width: 100%;

      .swiper-slide {
        display: flex;
        justify-content: center;
        align-items: center;
        height: auto;
        width: auto;

        align-items: baseline;
        justify-content: left;

        @media (min-width: $md) {
          justify-content: center;
        }
      }
    }
  }

  .swiper-button-prev-xf, .swiper-button-next-xf {
    display: block;
    position: absolute;
    width: auto;
    top: 33%;
  }

  .swiper-button-prev-xf {
    content: url("../resources/images/icons/double-arrow-left.png");

    left: 3.5rem;
    height: 1.5rem;

    @media (max-width: $md) {
      left: -0.5rem;
    }

    @media (max-width: $sm-air) {
      left: -1.5rem;
    }
  }

  .swiper-button-next-xf {
    content: url("../resources/images/icons/double-arrow-right.png");
    right: 3.5rem;
    height: 1.5rem;

    @media (max-width: $md) {
      right: -0.5rem;
    }

    @media (max-width: $sm-air) {
      right: -1.5rem;
    }
  }
}