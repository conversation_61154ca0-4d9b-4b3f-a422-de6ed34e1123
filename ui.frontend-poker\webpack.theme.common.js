'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TSConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const resolve = {
    extensions: ['.js', '.ts'],
    plugins: [new TSConfigPathsPlugin({
        configFile: './tsconfig.json'
    })]
};

module.exports = {
    context: path.resolve(__dirname, './src/main/webpack'),
    resolve: resolve,
    entry: {
        'poker' : './poker/main.ts',
        'poker-blog' : './poker/main-blog.ts',
        'poker-top-section' : './poker/main-top-section.ts',
        'poker-amp' : './poker/amp/main.ts',
        'poker-blog-amp' : './poker/amp/main-blog.ts',
        'promotion-poker' : '/components/fragment/promotion-poker/promotion-poker.ts',
				'poker-claim' : '/components/claim-poker/claim-poker.ts',

    },
    optimization: {
        minimize:false,
        splitChunks: {
            chunks: 'all', // The option could be set to 'async', so all vendors libs will be included in EACH component
            // file, but it's better to have them in separate files
            cacheGroups: {
                // "This chunk contains all the external libraries used in the project"
                defaultVendors: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'poker-vendors', // The chunk should be the same as location name in HtmlPageItemsConfig
                    chunks: 'all',
                }
            }
        }
     },
    output: {
       filename: 'aem-static/js/[name].js',
       path: path.resolve(__dirname, 'dist')
   },
   module: {
       rules: [
           {
               test: /\.tsx?$/,
               exclude: /node_modules/,
               use: [
                   {
                       loader: 'ts-loader'
                   },
                   {
                       loader: 'glob-import-loader',
                       options: {
                           resolve: resolve
                       }
                   }
               ]
           },
           {
               test: /\.scss$/,
               use: [
                   MiniCssExtractPlugin.loader,
                   {
                       loader: 'css-loader',
                       options: {
                           url: false
                       }
                   },
                   {
                       loader: 'sass-loader',
                   },
                   {
                       loader: 'glob-import-loader',
                       options: {
                           resolve: resolve
                       }
                   }
               ]
           },
           {
               test: /\.(ico|jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)(\?.*)?$/,
               use: {
                 loader: 'file-loader',
                 options: {
                   name: '[path][name].[ext]'
                 }
               }
           },
       ]
   },
   plugins: [
       new CleanWebpackPlugin(),
       new ESLintPlugin({
           extensions: ['js', 'ts', 'tsx']
       }),
       new MiniCssExtractPlugin({
           filename: 'aem-static/css/[name].css'
       }),
       new CopyWebpackPlugin({
           patterns: [
               { from: './resources', to: './aem-static/resources' },
           ]
       })
   ],
   stats: {
       assetsSort: 'chunks',
       builtAt: true,
       children: false,
       chunkGroups: true,
       chunkOrigins: true,
       colors: false,
       errors: true,
       errorDetails: true,
       env: true,
       modules: false,
       performance: true,
       providedExports: false,
       source: false,
       warnings: true
   }
};
