
// defaultThin
@font-face {
    font-family: "defaultThin";
    src: url("https://webassets.images4us.com/fonts/888-thin-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-thin-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.svg") format("svg");
    font-weight: 100;
    font-display: swap;
}

// defaultLight
@font-face {
    font-family: "defaultLight";
    src: url("https://webassets.images4us.com/fonts/888-light-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-light-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.svg") format("svg");
    font-weight: 200;
    font-display: swap;
}

// defaultRegular
@font-face {
    font-family: "defaultRegular";
    src: url("https://webassets.images4us.com/fonts/888-regular-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-regular-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.svg") format("svg");
    font-weight: 400;
    font-display: swap;
}

// defaultMedium
@font-face {
    font-family: "defaultMedium";
    src: url("https://webassets.images4us.com/fonts/888-medium-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-medium-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.svg") format("svg");
    font-weight: 500;
    font-display: swap;
}

// defaultDemibold
@font-face {
    font-family: "defaultDemibold";
    src: url("https://webassets.images4us.com/fonts/888-demibold-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.svg") format("svg");
    font-weight: 600;
    font-display: swap;
}

// defaultBold
@font-face {
    font-family: "defaultBold";
    src: url("https://webassets.images4us.com/fonts/888-bold-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-bold-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.svg") format("svg");
    font-weight: bold;
    font-display: swap;
}

// defaultBlack
@font-face {
    font-family: "defaultBlack";
    src: url("https://webassets.images4us.com/fonts/888-black-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-black-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.svg") format("svg");
    font-weight: 800;
    font-display: swap;
}

// defaultUltra
@font-face {
    font-family: "defaultUltra";
    src: url("https://webassets.images4us.com/fonts/888-ultra-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-ultra-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.svg") format("svg");
    font-weight: 900;
    font-display: swap;
}

// defaultCondMedium
@font-face {
    font-family: "defaultCondMedium";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

// defaultCondDemibold
@font-face {
    font-family: "defaultCondDemibold";
    src: url("https://webassets.images4us.com/fonts/888-CondensedDemiBold.otf") format("opentype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

// defaultCondBold
@font-face {
    font-family: "defaultCondBold";
    src: url("https://webassets.images4us.com/fonts/888-CondensedBold.otf") format("opentype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* Backwards Compatibility (to be deleted once everyone replaces font-weight property with the corresponding font-family above) */

@font-face {
    font-family: "defaultCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "defaultCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedDemiBold.otf") format("opentype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "defaultCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedBold.otf") format("opentype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* B2C fonts */

@font-face {
    font-family: "defaultExtraCondensed";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.svg") format("svg");
    font-weight: 400;
    font-display: swap;
}

@font-face {
    font-family: "defaultExtraCondensedMedium";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.svg") format("svg");
    font-weight: 500;
    font-display: swap;
}

@font-face {
    font-family: "defaultExtraCondensedDemibold";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.svg") format("svg");
    font-weight: 600;
    font-display: swap;
}

@font-face {
    font-family: "defaultExtraCondensedBold";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.svg") format("svg");
    font-weight: 700;
    font-display: swap;
}

@font-face {
    font-family: "888thin";
    src: url("https://webassets.images4us.com/fonts/888-thin-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-thin-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-thin-webfont.svg") format("svg");
    font-weight: 100;
    font-display: swap;
}

@font-face {
    font-family: "888light";
    src: url("https://webassets.images4us.com/fonts/888-light-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-light-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-light-webfont.svg") format("svg");
    font-weight: 200;
    font-display: swap;
}

@font-face {
    font-family: "888regular";
    src: url("https://webassets.images4us.com/fonts/888-regular-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-regular-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-regular-webfont.svg") format("svg");
    font-weight: 400;
    font-display: swap;
}

@font-face {
    font-family: "888medium";
    src: url("https://webassets.images4us.com/fonts/888-medium-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-medium-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-medium-webfont.svg") format("svg");
    font-weight: 500;
    font-display: swap;
}

@font-face {
    font-family: "888demibold";
    src: url("https://webassets.images4us.com/fonts/888-demibold-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-demibold-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-demibold-webfont.svg") format("svg");
    font-weight: 600;
    font-display: swap;
}

@font-face {
    font-family: "888bold";
    src: url("https://webassets.images4us.com/fonts/888-bold-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-bold-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-bold-webfont.svg") format("svg");
    font-weight: bold;
    font-display: swap;
}

@font-face {
    font-family: "888black";
    src: url("https://webassets.images4us.com/fonts/888-black-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-black-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-black-webfont.svg") format("svg");
    font-weight: 800;
    font-display: swap;
}

@font-face {
    font-family: "888ultra";
    src: url("https://webassets.images4us.com/fonts/888-ultra-webfont.eot");
    src: url("https://webassets.images4us.com/fonts/888-ultra-webfont.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888-ultra-webfont.svg") format("svg");
    font-weight: 900;
    font-display: swap;
}

@font-face {
    font-family: "888cond-medium";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888cond-medium";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888cond-medium";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888cond-demibold";
    src: url("https://webassets.images4us.com/fonts/888-CondensedDemiBold.otf") format("opentype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888cond-bold";
    src: url("https://webassets.images4us.com/fonts/888-CondensedBold.otf") format("opentype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* Backwards Compatibility (to be deleted once everyone replaces font-weight property with the corresponding font-family above) */

@font-face {
    font-family: "888SportCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedMedium.otf") format("opentype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888SportCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedDemiBold.otf") format("opentype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "888SportCond";
    src: url("https://webassets.images4us.com/fonts/888-CondensedBold.otf") format("opentype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* B2C fonts */

@font-face {
    font-family: "888ExtraCondensed";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensed.svg") format("svg");
    font-weight: 400;
    font-display: swap;
}

@font-face {
    font-family: "888ExtraCondensedMedium";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.svg") format("svg");
    font-weight: 400;
    font-display: swap;
}

@font-face {
    font-family: "888ExtraCondensedMedium";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedMedium.svg") format("svg");
    font-weight: 500;
    font-display: swap;
}

@font-face {
    font-family: "888ExtraCondensedDemiBold";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedDemiBold.svg") format("svg");
    font-weight: 600;
    font-display: swap;
}

@font-face {
    font-family: "888ExtraCondensedBold";
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.eot");
    src: url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/888-ExtraCondensedBold.svg") format("svg");
    font-weight: 700;
    font-display: swap;
}

@font-face {
    font-family: "Chronicle";
    src: url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.eot");
    src: url("https://webassets.images4us.com/fonts/SI/ChronicleTextG1-Roman.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/SI/Chronicle-TextG1-Roman.svg") format("svg");
    font-weight: 325;
    font-display: swap;
}

@font-face {
    font-family: "BrixtonWood";
    src: url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.eot");
    src: url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.otf") format("opentype"),
    url("https://webassets.images4us.com/fonts/888/Brixton_Wood-Vector.svg") format("svg");
    font-weight: 700;
    font-display: swap;
}

@font-face {
    font-family: "IndustryBook";
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Book.eot");
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Book.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Book.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Book.ttf") format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "IndustryDemi";
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Demi.eot");
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Demi.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Demi.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Demi.ttf") format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "IndustryMedium";
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Medium.eot");
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Medium.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Medium.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Medium.ttf") format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "IndustryBold";
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Bold.eot");
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Bold.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Bold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Bold.ttf") format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "IndustryBlack";
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Black.eot");
    src: url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Black.eot?#iefix") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Black.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/SI/Industry/Industry-Black.ttf") format("truetype");
    font-display: swap;
}

/*Montserrat font for MrGreen*/
@font-face {
    font-family: "MontserratBold";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBold.svg#MontserratBold") format("svg");
}

@font-face {
    font-family: "MontserratBlack";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratBlack.svg#MontserratBlack") format("svg");
}

@font-face {
    font-family: "MontserratSemiBold";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratSemiBold.svg#MontserratSemiBold") format("svg");
}

@font-face {
    font-family: "MontserratExtraBold";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratExtraBold.svg#MontserratExtraBold") format("svg");
}

@font-face {
    font-family: "MontserratRegular";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratRegular.svg#MontserratRegular") format("svg");
}

@font-face {
    font-family: "MontserratLight";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratLight.svg#MontserratLight") format("svg");
}

@font-face {
    font-family: "MontserratThin";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratThin.svg#MontserratThin") format("svg");
}

@font-face {
    font-family: "MontserratMedium";
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.eot");
    src: url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.eot") format("embedded-opentype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.woff2") format("woff2"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.woff") format("woff"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.ttf") format("truetype"),
    url("https://webassets.images4us.com/fonts/mrgreen/MontserratMedium.svg#MontserratMedium") format("svg");
}

