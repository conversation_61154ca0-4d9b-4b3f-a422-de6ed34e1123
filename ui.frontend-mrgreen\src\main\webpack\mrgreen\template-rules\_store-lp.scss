.store-lp {

    .hero-banner-light-lp {
        .custom-banner {
            @media (min-width: $sm-grid) {
                background-size: contain;
                padding-top: 23%;
            }
            
            @media (min-width: $md) {
                background-size: contain;
                padding-top: 23%;
            }

            @media (max-width: 910px) and (min-width: 500px) {
                background-size: contain !important;
            }

        
            .above-cta-rich-text {
                @media screen and (max-width: $sm-grid) and (orientation: landscape) and (min-width: 500px) {
                    width: 55%;
                    bottom: 75%;
                }
                @media screen and (max-width: 499px) {
                    max-width: 90%;
                    width: 90%;
                }
                @media (max-width: 330px) {
                    bottom: 50%;
                }
            }

            .cta-button-container {
                bottom: 15% !important;
                @media (max-width: 1480px) {
                    bottom: 13% !important;
                }
                @media screen and (max-width: $sm-grid) and (orientation: landscape) and (min-width: 500px) {
                    bottom: 58% !important;
                }
                @media (max-width: 330px) {
                    bottom: 5% !important;
                }
            }
        }
    }
    
}