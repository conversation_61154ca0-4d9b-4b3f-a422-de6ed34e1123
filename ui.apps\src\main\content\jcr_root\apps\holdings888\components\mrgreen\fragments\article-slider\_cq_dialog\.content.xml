<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
          xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
          jcr:primaryType="nt:unstructured"
          jcr:title="Article Slider Properties"
          extraClientlibs="[888casino.components.author.editor,holdings888.dialog.altText]"
          sling:resourceType="cq/gui/components/authoring/dialog">
    <content
            jcr:primaryType="nt:unstructured"
            granite:class="cmp-image__editor"
            sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="granite/ui/components/coral/foundation/tabs"
                    maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <main
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Main"
                            sling:resourceType="granite/ui/components/coral/foundation/container"
                            margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                    margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <seoTag
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/include"
                                                    path="holdings888/components/dialog-include/seo-tag" />
                                            <boxTitle
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="Box Title"
                                                    name="./boxTitle"/>
                                            <textAlignment
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/include"
                                                    path="holdings888/components/dialog-include/text-alignment" />
                                            <type
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                granite:class="cq-dialog-dropdown-showhide"
                                                fieldLabel="Style Option"
                                                emptyOption="{Boolean}false"
                                                multiple="{Boolean}false"
                                                name="./type">
                                                <items
                                                    jcr:primaryType="nt:unstructured">
                                                    <relatedArticles
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Related Articles"
                                                        selected="{Boolean}true"
                                                        value="relatedArticlesStyle" />
                                                    <internalLinks
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Internal Links"
                                                        value="internalLinksStyle" />
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".articles-showhide-target"/>
                                            </type>
                                            <dynamic-article-style-options
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="articles-showhide-target"
                                                cq-dialog-dropdown-showhide-value="relatedArticlesStyle"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <style-options
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                        fieldLabel="Article Title Style Option"
                                                        emptyOption="{Boolean}false"
                                                        multiple="{Boolean}false"
                                                        name="./articleStyleType">
                                                            <items
                                                            jcr:primaryType="nt:unstructured">
                                                            <defaultStyle
                                                                jcr:primaryType="nt:unstructured"
                                                                text="Green Color"
                                                                selected="{Boolean}true"
                                                                value=" " />
                                                            <greenGradientStyle
                                                                jcr:primaryType="nt:unstructured"
                                                                text="Green Grandient Color"
                                                                value="article-green-gradient" />
                                                        </items>
                                                    </style-options>
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="relatedArticlesStyle"/>
                                            </dynamic-article-style-options>
                                            <id
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="ID"
                                                name="./id"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </main>
                    <articles
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Articles"
                            sling:resourceType="granite/ui/components/coral/foundation/container"
                            margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                    margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <articleItems
                                                    jcr:primaryType="nt:unstructured"
                                                    granite:class="cmp-image__editor-image-with-alt-multifield"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                    composite="{Boolean}true"
                                                    validation="minmax-multifield"
                                                    fieldLabel="Articles Items">
                                                <granite:data
                                                        jcr:primaryType="nt:unstructured"
                                                        min-items="1"
                                                        name="articles"/>
                                                <field
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container"
                                                        name="./articles">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <articleImage
                                                                        jcr:primaryType="nt:unstructured"
                                                                        path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                                    <parameters
                                                                            jcr:primaryType="nt:unstructured"
                                                                            altName="articleAltImage"
                                                                            hideIsFetchPriorityHigh="{Boolean}true"
                                                                            imageIsRequired="{Boolean}true"/>
                                                                </articleImage>
                                                                <articleLink
                                                                        jcr:primaryType="nt:unstructured"
                                                                        path="/apps/holdings888/components/common888/dialog-include/link"
                                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                                    <parameters
                                                                            jcr:primaryType="nt:unstructured"
                                                                            fieldsetTitle="Link"
                                                                            linkUrlName="articleLink"
                                                                            urlIsRequired="{Boolean}true"
                                                                            linkLabelName="articleText"
                                                                            labelIsRequired="{Boolean}true"
                                                                            hideScript="{Boolean}false"
                                                                    />
                                                                </articleLink>
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </articleItems>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </articles>
                    <targetConfig
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/include"
                            path="holdings888/components/dialog-include/target-config"/>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>