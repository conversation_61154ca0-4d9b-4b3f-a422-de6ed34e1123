.promotions-category-icons-anchor {
  height: 94px;
  background-color: #f9f9f9;
  .title-component{
    padding: 7.5px 0;
    @media only screen and (max-width: 768px) {
      padding: 15px 0;
    }
  }
  .container-background-color{
    background-color: $dark-2;

    .container-wrapper {
      padding: 0 4.5rem;
      @media only screen and (max-width: 768px) {
        padding: 0 1.5rem;
      }
      @media only screen and (min-width: 769px) and (max-width: 1024px) {
        padding: 0 3rem;
      }
    }
  }
}
.promotions-category-icons-anchor-cmp {
  height: 94px;
  &.fixedCategories{
    position: fixed;
    z-index: 99;
    width: 100%
  }
  width: 100%;
  overflow: hidden;
  background-color: $dark-2;

  .categories-container{
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    overflow: auto;
    @media (max-width: $sm){
      justify-content: space-between;;
    }
    .category-element{
      border-right: 1px solid #222;
      flex: 0 0 auto;
      min-width: 115px;
      &__innerLink{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 0.6rem;
        text-decoration: none;
        min-width: 80px;
        height: 90px;
        &:hover {
          background-color: $color-foreground;
        }
        .anchor-icon{
          height: 28px;
          margin-bottom: 0.6rem;
        }
        .anchor-title{
          font-weight: $font-weight-light;
          font-size: 14px;
          text-align: center;
          color: $white;
        }
      }
      &.active {
        a {
          background-color: $color-foreground;
        }
      }
      &:last-child {
        border-right: none;
      }
    }
  }
}