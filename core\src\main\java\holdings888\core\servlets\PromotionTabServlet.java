package holdings888.core.servlets;

import com.day.cq.commons.jcr.JcrConstants;
import com.day.cq.workflow.WorkflowException;
import com.day.cq.workflow.WorkflowService;
import com.day.cq.workflow.WorkflowSession;
import com.day.cq.workflow.exec.Workflow;
import com.day.cq.workflow.exec.WorkflowData;
import com.day.cq.workflow.model.WorkflowModel;
import com.drew.lang.annotations.NotNull;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.ModifiableValueMap;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ResourceUtil;
import org.apache.sling.api.servlets.ServletResolverConstants;
import org.apache.sling.api.servlets.SlingAllMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;

import javax.jcr.Session;
import javax.servlet.Servlet;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

import static holdings888.core.utils.Constants.SLASH;

/**
 * Java Servlet to populate 'Activity' dropdown in editor dialog of Poker Blog Article Page Properties
 */
@Slf4j
@Component( service = Servlet.class,
        property = {
                Constants.SERVICE_DESCRIPTION + "= Start workflow Export to Target scheduled Servlet",
                ServletResolverConstants.SLING_SERVLET_NAME + "=" + "PromotionTabServlet",
                ServletResolverConstants.SLING_SERVLET_PATHS + "=" + "/apps/startWorkflow",
                ServletResolverConstants.SLING_SERVLET_EXTENSIONS + "=" + "json"
        })
public class PromotionTabServlet extends SlingAllMethodsServlet {

    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String WORKFLOW_MODEL_PATH = "/var/workflow/models/scheduled-export-to-target-xf";
    private static final String PAGE_PATH = "pagePath";
    private static final String MESSAGE = "message";
    private static final String ERROR = "error";
    private static final String START_DATE_WORKFLOW_ID = "startDateWorkflowId";
    private static final String END_DATE_WORKFLOW_ID = "endDateWorkflowId";
    private static final TimeZone UTC = TimeZone.getTimeZone("UTC");
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss Z");
    private static final String OLD_START_DATE = "oldStartDate";
    private static final String OLD_END_DATE = "oldEndDate";

    @Reference
    private transient WorkflowService workflowService;

    @Override
    protected void doPost(@NotNull SlingHttpServletRequest request, @NotNull SlingHttpServletResponse response) throws IOException {
        log.debug("[888] - [PromotionTabServlet] - doPost");
        request.setCharacterEncoding("UTF-8");
        JsonObject jsonBody = readJsonBody(request);
        String startDate = getString(jsonBody, START_DATE);
        String endDate = getString(jsonBody, END_DATE);
        String oldStartDate = getString(jsonBody, OLD_START_DATE);
        String oldEndDate = getString(jsonBody, OLD_END_DATE);
        String payloadPath = getString(jsonBody, PAGE_PATH);

        ResourceResolver resourceResolver = request.getResourceResolver();
        Gson gson = new Gson();
        JsonObject jsonObject = new JsonObject();
        PrintWriter responseWriter = response.getWriter();
        response.setStatus(HttpStatus.SC_OK);

        if (StringUtils.isNotBlank(payloadPath) && StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            Resource contentResource = null;
            Calendar oldStartDateCalendar = null;
            Calendar oldEndDateCalendar = null;

            try {
                Calendar startDateCalendar = convertToCalendar(startDate);
                Calendar endDateCalendar = convertToCalendar(endDate);
                oldStartDateCalendar = convertToCalendar(oldStartDate);
                oldEndDateCalendar = convertToCalendar(oldEndDate);

                contentResource = resourceResolver.getResource(payloadPath + SLASH + JcrConstants.JCR_CONTENT);
                if (contentResource == null) {
                    jsonObject.addProperty(ERROR, "Page at path: " + payloadPath + " is broken");
                    responseWriter.write(gson.toJson(jsonObject));
                    return;
                }
                String pageType;
                if(contentResource.getResourceType().contains("content-banner-page")){
                    pageType = "content-banner-page";
                }else{
                    pageType ="promotion";
                }

                if (!startDateCalendar.before(endDateCalendar)) {
                    rollbackDates(contentResource, resourceResolver, oldStartDateCalendar, oldEndDateCalendar);
                    jsonObject.addProperty(ERROR, "Start date should be before end date");
                    responseWriter.write(gson.toJson(jsonObject));
                    return;
                }

                String oldStartDateWorkflowId = ResourceUtil.getValueMap(contentResource).get(START_DATE_WORKFLOW_ID, String.class);
                String oldEndDateWorkflowId = ResourceUtil.getValueMap(contentResource).get(END_DATE_WORKFLOW_ID, String.class);
                Calendar currentTime = Calendar.getInstance(UTC);
                Workflow startDateWorkflow = null;
                Calendar startDateWFCalendar = startDateCalendar;
                Calendar endDateWFCalendar = endDateCalendar;
                if (endDateCalendar.before(currentTime)) {
                    endDateWFCalendar = currentTime;
                } else {
                    if (startDateCalendar.before(currentTime)) {
                        startDateWFCalendar = currentTime;
                    }
                   startDateWorkflow = startWorkflow(payloadPath, oldStartDateWorkflowId, resourceResolver, startDateWFCalendar, pageType);
                }
                Workflow endDateWorkflow = startWorkflow(payloadPath, oldEndDateWorkflowId, resourceResolver, endDateWFCalendar,pageType);

                saveDate(contentResource, resourceResolver, startDateCalendar, endDateCalendar);

                jsonObject.addProperty(MESSAGE, "Workflows started successfully for: " + payloadPath);
                String startDateWorkflowId = null;
                if (startDateWorkflow != null) {
                    startDateWorkflowId = startDateWorkflow.getId();
                    jsonObject.addProperty(START_DATE_WORKFLOW_ID, startDateWorkflowId);
                    jsonObject.addProperty("wfStartDate", formatCalendar(startDateWFCalendar));
                }
                String endDateWorkflowId = endDateWorkflow.getId();
                saveWFIds(contentResource, resourceResolver, startDateWorkflowId, endDateWorkflowId);

                jsonObject.addProperty(END_DATE_WORKFLOW_ID, endDateWorkflowId);
                jsonObject.addProperty("wfEndDate", formatCalendar(endDateWFCalendar));
                responseWriter.write(gson.toJson(jsonObject));
            } catch (WorkflowException wfException) {
                log.error("Error starting workflows", wfException);
                rollbackDates(contentResource, resourceResolver, oldStartDateCalendar, oldEndDateCalendar);
                jsonObject.addProperty(ERROR, "Error starting workflow: " + wfException.getMessage());
                responseWriter.write(gson.toJson(jsonObject));
            } catch (IOException ioException) {
                log.error("Error updating page property", ioException);
                rollbackDates(contentResource, resourceResolver, oldStartDateCalendar, oldEndDateCalendar);
                jsonObject.addProperty(ERROR, "Error updating page property: " + ioException.getMessage());
                responseWriter.write(gson.toJson(jsonObject));
            } catch (Exception e) {
                log.error("Error [PromotionTabServlet] ", e);
                rollbackDates(contentResource, resourceResolver, oldStartDateCalendar, oldEndDateCalendar);
                jsonObject.addProperty(ERROR, "Export Error: " + e.getMessage());
                responseWriter.write(gson.toJson(jsonObject));
            } finally {
                if (resourceResolver != null && resourceResolver.isLive()) {
                    resourceResolver.commit();
                    resourceResolver.close();
                }
            }
        } else {
            // validated at frontend side
            log.warn("No all parameters are provided. Path: {}, Start Date: {}, End Date: {}", payloadPath, startDate, endDate);
            jsonObject.addProperty(ERROR, "No path, start or endDate parameters are provided");
            responseWriter.write(gson.toJson(jsonObject));
        }
    }

    private static String getString(JsonObject jsonBody, String key) {
        JsonElement jsonElement = jsonBody.get(key);
        return Objects.nonNull(jsonElement) ? jsonElement.getAsString() : null;
    }

    private Workflow startWorkflow(String payloadPath, String workflowId, ResourceResolver resourceResolver, Calendar absoluteTime,String pageType) throws Exception {
        WorkflowSession workflowSession = workflowService.getWorkflowSession(resourceResolver.adaptTo(Session.class));
        terminateOldWorkflow(workflowId, workflowSession);

        WorkflowModel workflowModel = workflowSession.getModel(WORKFLOW_MODEL_PATH);
        WorkflowData workflowData = workflowSession.newWorkflowData("JCR_PATH", payloadPath);
        workflowData.getMetaDataMap().put("absoluteTime",absoluteTime.getTimeInMillis());
        Calendar scheduledTime = Calendar.getInstance(UTC);
        scheduledTime = scheduledTime.after(absoluteTime) ? scheduledTime : absoluteTime;
        workflowData.getMetaDataMap().put("workflowTitle", "Export to Target scheduled on " + formatCalendar(scheduledTime));
        workflowData.getMetaDataMap().put("type", pageType);
        return workflowSession.startWorkflow(workflowModel, workflowData);
    }

    private void terminateOldWorkflow(String workflowId, WorkflowSession workflowSession) throws WorkflowException {
        if (workflowId != null) {
            Workflow oldWorkflow = workflowSession.getWorkflow(workflowId);
            if (oldWorkflow != null && oldWorkflow.isActive()) {
                workflowSession.terminateWorkflow(oldWorkflow);
            }
        }
    }

    private void saveDate(Resource contentNode, ResourceResolver resourceResolver, Calendar startDate, Calendar endDate) throws IOException {
        try {
                ModifiableValueMap valueMap = contentNode.adaptTo(ModifiableValueMap.class);
                if (valueMap != null) {
                    if (startDate != null) {
                        valueMap.put(START_DATE, startDate);
                    } else {
                        valueMap.remove(START_DATE);
                    }
                    if (endDate != null) {
                        valueMap.put(END_DATE, endDate);
                    } else {
                        valueMap.remove(END_DATE);
                    }
                    resourceResolver.refresh();
                }
                log.debug("Date properties updated successfully");
        } catch (Exception e) {
            log.error("Error updating page date properties for {} : {}", contentNode.getPath(), e.getMessage());
            throw new IOException("Error updating page date properties", e);
        }
    }

    private void rollbackDates(Resource contentResource, ResourceResolver resourceResolver, Calendar oldStartDateCalendar, Calendar oldEndDateCalendar) {
        try {
            saveDate(contentResource, resourceResolver, oldStartDateCalendar, oldEndDateCalendar);
        } catch (IOException ioException) {
            log.error("Error updating page property", ioException);
        }
    }

    private void saveWFIds(Resource contentNode, ResourceResolver resourceResolver, String startDateWorkflowId, String endDateWorkflowId) throws IOException {
        try {
            ModifiableValueMap valueMap = contentNode.adaptTo(ModifiableValueMap.class);
            if (valueMap != null) {
                if (startDateWorkflowId != null) {
                    valueMap.put(START_DATE_WORKFLOW_ID, startDateWorkflowId);
                } else {
                    valueMap.remove(START_DATE_WORKFLOW_ID);
                }
                valueMap.put(END_DATE_WORKFLOW_ID, endDateWorkflowId);
                resourceResolver.refresh();
            }
            log.debug("Property updated successfully");
        } catch (Exception e) {
            log.error("Error updating page workflowIds property for {} : {}", contentNode.getPath(), e.getMessage());
            throw new IOException("Error updating page workflowIds property", e);
        }
    }


    private static Calendar convertToCalendar(String absoluteTime) {
        if (absoluteTime == null) {
            return null;
        }
        OffsetDateTime dateTime = OffsetDateTime.parse(absoluteTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        Date date = Date.from(dateTime.toInstant());
        Calendar calendar = Calendar.getInstance(UTC);
        calendar.setTime(date);
        return calendar;
    }

    private String formatCalendar(Calendar calendar) {
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(calendar.toInstant(), calendar.getTimeZone().toZoneId());
        return FORMATTER.format(zonedDateTime);
    }

    private JsonObject readJsonBody(SlingHttpServletRequest request) throws IOException {
        String body = IOUtils.toString(request.getReader());
        return new Gson().fromJson(body, JsonObject.class);
    }
}
