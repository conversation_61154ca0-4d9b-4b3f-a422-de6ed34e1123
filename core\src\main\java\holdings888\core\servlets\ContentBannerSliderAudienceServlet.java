package holdings888.core.servlets;

import com.day.cq.wcm.api.Page;
import com.drew.lang.annotations.NotNull;
import holdings888.core.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.apache.sling.models.annotations.injectorspecific.ScriptVariable;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;
import java.util.Enumeration;

import static holdings888.core.servlets.DamUtilsServlet.populateCasinoDropDownListWithAudiences;

/**
 * Java Servlet to populate 'Audience' dropdown in editor dialog Carousel Component
 *
 */
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Get Categories Servlet",
        "sling.servlet.resourceTypes=" + "/apps/contentBannerSliderAudienceServlet"
})
public class ContentBannerSliderAudienceServlet extends SlingSafeMethodsServlet {
    private final transient Logger logger = LoggerFactory.getLogger(ContentBannerSliderAudienceServlet.class);

    @ScriptVariable
    transient private Page currentPage;

    @Override
    protected void doGet(@NotNull SlingHttpServletRequest request,
            @NotNull SlingHttpServletResponse response) {
        logger.info("[888] - [ContentBannerSliderAudienceServlet] - doGet");
        Enumeration<String> values = request.getHeaders("Referer");
        StringBuilder url = new StringBuilder();
        if (values != null) {
            while (values.hasMoreElements()) {
                String firstElement = values.nextElement();
                ResourceResolver resourceResolver = request.getResourceResolver();
                String path = getPagePath(firstElement);
                Resource resource = resourceResolver.getResource(path);
                String pathContentBannerSliderPage;

                if (resource != null) {
                    Page page = resource.adaptTo(Page.class);
                    if (firstElement.contains("experience-fragments")) {
                        pathContentBannerSliderPage = page.getProperties().get("pathContentBannerSliderPage", String.class);
                    } else {
                        pathContentBannerSliderPage = PageUtils.findContentBannerSliderPagePath(page);
                    }
                    if (StringUtils.isNotBlank(pathContentBannerSliderPage)) {
                        url.append(pathContentBannerSliderPage);
                        url.append("/jcr:content/audiences");
                    }
                }
            }
        }
        populateCasinoDropDownListWithAudiences(request, url.toString());
    }

    private String getPagePath(String firstElement) {
        int startPath= firstElement.indexOf("/content/");
        String path = firstElement.substring(startPath);
        if(path.contains(".html")){
            int endPath= path.indexOf(".html");
            path = path.substring(0,endPath);

        }
        return path;
    }
}

