<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
          xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
          xmlns:jcr="http://www.jcp.org/jcr/1.0"
          xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
          jcr:primaryType="nt:unstructured"
          jcr:title="Explorer Banner"
          sling:resourceType="cq/gui/components/authoring/dialog"
          extraClientlibs="[core.wcm.components.image.v3.editor,core.wcm.components.commons.editor.dialog.pageimagethumbnail.v1,holdings888.dialog.altText, holdings888.explorerBanner]"
          width="{Long}500">
        <content jcr:primaryType="nt:unstructured"
                 granite:class="cmp-image__editor"
                 sling:resourceType="granite/ui/components/coral/foundation/container">
                <items jcr:primaryType="nt:unstructured">
                        <tabs jcr:primaryType="nt:unstructured">
                                <items jcr:primaryType="nt:unstructured">
                                        <tab1 jcr:primaryType="nt:unstructured" jcr:title="General Settings">
                                                <items jcr:primaryType="nt:unstructured">
                                                        <columns jcr:primaryType="nt:unstructured">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                        <column jcr:primaryType="nt:unstructured">
                                                                                <items jcr:primaryType="nt:unstructured">

                                                                                        <file
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                                                                <parameters
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        imagePrefixName="defaultImage"
                                                                                                        imageIsRequired="{Boolean}true"
                                                                                                        imageLabel="Default Image"
                                                                                                        altName="imageAlt"
                                                                                                        imageDescription="This is the default image that will be loaded when a date is not set." />
                                                                                        </file>
                                                                                
                                                                                        <enableRoundImage
                                                                                                granite:class="cq-dialog-dropdown-showhide"
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                                                fieldLabel="Round Image Options"
                                                                                                name="./roundImage">
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        cq-dialog-dropdown-showhide-target=".imageround-showhide-target"/>
                                                                                                <items jcr:primaryType="nt:unstructured">
                                                                                                        <no-round-image
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                text="No round image"
                                                                                                                value=" "/>
                                                                                                        <imageround
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                text="Round image"
                                                                                                                value="image-round"/>
                                                                                                </items>
                                                                                        </enableRoundImage>
                                                                                        <dynamic-imageround
                                                                                                granite:class="hide imageround-showhide-target"
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                                                                text="With border"
                                                                                                name="./roundImageBorder"
                                                                                                checked="{Boolean}true"
                                                                                                uncheckedValue="false"
                                                                                                value="{Boolean}true">
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        showhidetargetvalue="image-round"/>
                                                                                        </dynamic-imageround>
                                                                                        <textLink
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                path="/apps/holdings888/components/common888/dialog-include/link"
                                                                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                                                                <parameters
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        fieldsetTitle="Link (Optional, when filled, disabled Modal.)"
                                                                                                        linkUrlName="imageTextLink"
                                                                                                        urlIsRequired="{Boolean}false"
                                                                                                        hideLabel="{Boolean}true"
                                                                                                        hideScript="{Boolean}true"
                                                                                                />
                                                                                        </textLink>
                                                                                        <defaultBannerScript
                                                                                                jcr:description="The JS Script that will be execute on click"
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                                                                fieldLabel="Custom Script"
                                                                                                name="./defaultBannerScript"
                                                                                                resize="vertical"/>
                                                                                </items>
                                                                        </column>
                                                                </items>
                                                        </columns>
                                                </items>
                                        </tab1>
                                        <bannerImage
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="holdings888/components/dialog-include/banner-image" />
                                </items>
                        </tabs>

                </items>
        </content>
</jcr:root>
