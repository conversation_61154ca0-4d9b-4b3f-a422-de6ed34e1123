<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        jcr:primaryType="nt:unstructured"
        jcr:title="Properties content banner"
        sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
        cq:showOnCreate="{Boolean}false">
        <items jcr:primaryType="nt:unstructured">
                <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                                <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items
                                                jcr:primaryType="nt:unstructured">
                                                <audiences
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                        fieldDescription="Select the Audiences"
                                                        composite="{Boolean}true"
                                                        fieldLabel="Audiences">
                                                        <field
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                                name="./audiences">
                                                                <items
                                                                        jcr:primaryType="nt:unstructured">
                                                                        <audience
                                                                                jcr:primaryType="nt:unstructured"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                                emptyOption="{Boolean}true"
                                                                                ordered="{Boolean}true"
                                                                                fieldLabel="Audience"
                                                                                name="./audience"
                                                                                fieldDescription="It will display the teaser in the correct XF linked to the selected audiences.">
                                                                                <datasource
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="/apps/contentBannerSliderAudienceServlet" />
                                                                        </audience>
                                                                        <orderXF
                                                                                jcr:primaryType="nt:unstructured"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                                                                fieldDescription="Determines the render order of the teaser in the XF carousel."
                                                                                fieldLabel="Teaser Order In Carousel XF"
                                                                                name="./orderXf"
                                                                                renderReadOnly="{Boolean}true">
                                                                                <granite:data
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        allowBulkEdit="{Boolean}true"
                                                                                        cq-msm-lockable="order" />
                                                                        </orderXF>
                                                                </items>
                                                        </field>
                                                </audiences>
                                                <explanation
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                        level="{Long}4"
                                                        text="Dates usage: To activate the teaser - set any valid dates range for now.
                                                        To de-activate the teaser - set both dates in the past.
                                                        If you want to schedule the teaser activation - set the future dates range.
                                                        NOTE: DONT change teaser's category without de-activating it with previous category! " />
                                                <startDate
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                                        displayTimezoneMessage="{Boolean}true"
                                                        displayedFormat="DD-MM-YYYY HH:mm Z"
                                                        valueformat="DD/MM/YYYY HH:mm Z"
                                                        fieldLabel="Start Date"
                                                        name="./startDate"
                                                        type="datetime"
                                                        validation="dates.notchanged"
                                                        granite:id="startDateExportToTarget">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="startDate" />
                                                </startDate>
                                                <endDate
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                                        displayTimezoneMessage="{Boolean}true"
                                                        displayedFormat="DD-MM-YYYY HH:mm Z"
                                                        valueformat="DD/MM/YYYY HH:mm Z"
                                                        fieldLabel="End Date"
                                                        name="./endDate"
                                                        type="datetime"
                                                        validation="dates.notchanged"
                                                        granite:id="endDateExportToTarget">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="endDate" />
                                                </endDate>
                                                <exportToTargetWrapper
                                                        jcr:primaryType="nt:unstructured"
                                                        granite:id="exportToTargetWrapper"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items
                                                                jcr:primaryType="nt:unstructured">
                                                                <exportToTarget
                                                                        granite:id="exportToTargetScheduled"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/anchorbutton"
                                                                        text="Export to Target scheduled"
                                                                        variant="primary"
                                                                        size="L"
                                                                        icon="arrowUp"
                                                                        x-cq-linkchecker="skip"/>
                                                        </items>
                                                </exportToTargetWrapper>
                                        </items>
                                </column>
                        </items>
                </column>
        </items>
</jcr:root>
