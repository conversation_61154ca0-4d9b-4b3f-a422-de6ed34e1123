class Accordion {
    constructor(element) {
        this.element = element;
        this.tabs = element.querySelectorAll(".cmp-accordion-lp__tab");
        this.init();
    }

    init() {
        this.tabs.forEach(tab => {
            const expandIconURL = tab.getAttribute('data-expand-icon');
            const icon = tab.querySelector('.cmp-accordion-lp__icon');

            const panel = tab.nextElementSibling;
            const footerSection = panel.querySelector('.section-footer');

            const variationV1 = document.querySelector('.accordion-lp-v1');
            const variationV2 = document.querySelector('.accordion-lp-v2');
            if (variationV2) {
                const title = tab.querySelector('.cmp-accordion-lp__title'); 
                title.classList.add('cmp-accordion-lp__title-expand');
            }

            // Set initial icon
            icon.classList.add('expand-icon');
            if(expandIconURL){
                icon.style.setProperty('--expand-icon-url', `url(${window.location.origin}${expandIconURL})`);
            }

            tab.addEventListener("click", () => this.toggleTab(tab));

            if(footerSection){
                footerSection.addEventListener("click", () => {
                    if (variationV1) {
                        moveToAccordion();
                    }
                    this.toggleTab(tab);
                });
            }
        });
        setTimeout(function(){
            checkIdUrl();
        },250);
    }

    toggleTab(tab) {
        const panel = tab.nextElementSibling;
        const icon = tab.querySelector('.cmp-accordion-lp__icon');
        const isExpanded = tab.classList.contains("active");

        const variationV1 = document.querySelector('.accordion-lp-v1');                
        if (variationV1) {
            const panelTotalHeight = tab.scrollHeight + panel.scrollHeight + 400;
            panel.style.maxHeight = isExpanded ? 0 : panelTotalHeight + "px";
            panel.style.opacity = isExpanded ? 0 : 1;
        } else {
            panel.style.display = isExpanded ? "none" : "block";
        }
        tab.setAttribute('aria-expanded', !isExpanded);
        tab.classList.toggle("active");
        icon.classList.toggle('expand-icon');
        icon.classList.toggle('collapse-icon');

        const variationV2 = document.querySelector('.accordion-lp-v2');
        if (variationV2) {
            const title = tab.querySelector('.cmp-accordion-lp__title'); 
            title.classList.toggle('cmp-accordion-lp__title-expand');
            title.classList.toggle('cmp-accordion-lp__title-collapse');
        }

        const collapseIconURL = tab.getAttribute('data-collapse-icon');
        if (collapseIconURL) {
            icon.style.setProperty(`--${isExpanded ? 'expand' : 'collapse'}-icon-url`,
                `url(${window.location.origin}${tab.getAttribute(`data-${isExpanded ? 'expand' : 'collapse'}-icon`)})`);
        }
    }
}

function accordionLpInit(){
    const accordionElement = document.querySelectorAll(".cmp-accordion-lp");
    accordionElement.forEach((tab) =>     new Accordion(tab));   
}

function setupLinkListeners() {
    const links = document.querySelectorAll('a[href*="#tc"]');
    if (links) {
        links.forEach(link => {
            if(checkCurrentPage(link)) {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    moveToAccordion();
                });
            }
        });
    }
}

function checkCurrentPage(link) {

    const currentPagePathname = window.location.pathname;
    const url = new URL(link.href);
    const linkPathname = url.pathname;
    return currentPagePathname === linkPathname;
}

function checkIdUrl() {
    let idFromURL = getIDFromURL();
    if (idFromURL == "tc"){
        moveToAccordion();
    }
}
function getIDFromURL() {

    let url = window.location.href;
    let hashIndex = url.indexOf("#");
    if (hashIndex !== -1) {
        let id = url.substring(hashIndex + 1);
        return id;
    } else {
        return null;
    }
}

function moveToAccordion(){
    const accordionDiv = document.querySelector('.accordion-lp');
    let navbarHeight = 0;
    const cyHeader = document.querySelector('.cy-header-regulation-data');
    const cyNavbar = document.querySelector('.cy-navbar-container');
    const accordionActive = document.querySelectorAll(".cmp-accordion-lp__tab.active");
    const offsetTop = accordionDiv.offsetTop;
    
    if (cyHeader) {
        navbarHeight += cyHeader.clientHeight;
    }
    if (cyNavbar) {
        navbarHeight += cyNavbar.clientHeight;
    }

    if (accordionActive.length == 0) {
        let firstButton = accordionDiv.querySelector('button');
        if (firstButton) {
            firstButton.click();
        }
    }
    
    window.scrollTo({
        top: offsetTop - navbarHeight,
        behavior: 'smooth'
    });
}

accordionLpInit();
setupLinkListeners();
window.onload = checkIdUrl;
