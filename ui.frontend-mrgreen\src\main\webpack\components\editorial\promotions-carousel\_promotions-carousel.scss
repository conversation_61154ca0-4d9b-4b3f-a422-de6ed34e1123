.promotions-carousel {
    .promotion-carousel-component {
        color: $white;
        box-sizing: inherit;
        font-size: 15px;
        padding: 0 3em 2em;
        flex-direction: row;
        display: flex;
        transition: all 0.4s ease;
        margin-top: 2.75em;
        @media only screen and (max-width: 768px) {
            padding: 1.5rem;
        }
        @media only screen and (min-width: 769px) and (max-width: 1024px) {
            padding: 3rem;
        }

        .promotion-carousel-container {
            width: 100%;
            color: $black;
            font-family: $font-family-10;
            position: relative;
            .promotions-carousel-top {
                background: inherit;
                border-left: none;
                color: $dark-2;
                padding: 0;
                display: flex;
                justify-content: space-between;
                margin-bottom: 1.5em;
                .promotions-carousel-title {
                    background: inherit;
                    border-left: none;
                    color: $dark-green;
                    font-family: $font-family-4;
                    padding: 0;
                    h2 {
                        text-transform: uppercase;
                        background: transparent;
                        line-height: inherit;
                        padding-bottom: 1rem;
                        margin: 0;
                        font-size: 24px;
                        color: $dark-2;
                        font-weight: $font-weight-bold;
                        font-family: $font-family;
                        @media only screen and (max-width: 460px) {
                            font-size: 16px;
                        }
                    }
                }
                .arrows {
                    position: relative;
                    top: 0.3em;
                    display: none;
                    font-family: $font-family-7;

                    @media (min-width: $sm-grid) {
                        display: flex;
                    }
                    .arrow-right {
                        background: inherit;
                        border: 0.2rem solid $dark-green-2;
                        color: $dark-green-2;
                        width: 3.8rem;
                        height: 3.8rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 100%;
                        margin: 0 0.2rem;
                        cursor: pointer;
                    }
                    .arrow-left {
                        background: inherit;
                        border: 0.2rem solid $dark-green-2;
                        color: $dark-green-2;
                        width: 3.8rem;
                        height: 3.8rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 100%;
                        margin: 0 0.2rem;
                        cursor: pointer;
                        transform: rotate(180deg);
                    }
                }
            }

            .swiper {
                position: relative;
                max-width: 100%;
                box-sizing: content-box;
                overflow-x: hidden;
                height: 100%;
                .swiper-wrapper {
                    width: auto;
                    display: flex;
                    transition: all 0.4s ease;
                    margin: 0 -0.5rem;
                    gap: 1rem;
                    flex-direction: column;
                    height: fit-content;
                    @media (min-width: $sm-grid) {
                        flex-direction: row;
                    }
                }
            }
        }
    }
}
