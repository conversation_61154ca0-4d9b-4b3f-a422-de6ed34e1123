
$(function () {

    $(document).on("dialog-ready", function(e) {

        const exportToTargetScheduledAnchorButton = document.querySelector("#explorerBannerExportToTargetWrapper");
        
        var formElement = $(".coral-Form--vertical.cq-dialog");
        if (formElement.length) {
            formElement.attr("id", "explorer-banner-form");
        }

        if (exportToTargetScheduledAnchorButton) {
            // Get current URL.
            const currentURL = window.location.href;

            // This won't work anymore as we need to get the path
            // of the page from a component where we're on.
            // const urlParams = new URLSearchParams(window.location.search);
            // const pagePath = urlParams.get('item');

            // There's no more item in the query parameter so we'd
            // proceed in cleaning the URL of the current page we're on.
            const pagePath = cleanAEMUrl(currentURL);
            const validatorSelector = '[data-foundation-validation~="dates.notchanged"]';
            let validator = getValidator()

            exportToTargetScheduledAnchorButton.on("click", function (e) {

                const startDatePicker = document.querySelector("#startDateExportToTarget");
                const endDatePicker = document.querySelector("#endDateExportToTarget");
                const startDate = startDatePicker?.value;
                const endDate = endDatePicker?.value;
                let oldStartDate = getJCRDate(startDatePicker);
                let oldEndDate = getJCRDate(endDatePicker);
                const servletUrl = '/apps/startWorkflow.json';

                if ((startDate != null && startDate.trim() !== '') && (endDate != null && endDate.trim() !== '')
                    && (pagePath != null && pagePath.trim() !== '')) {

                    let invalidTabs = getInvalidTabs();

                    console.log("Invalid tabs: ", invalidTabs);

                    if (invalidTabs.length === 0) {
                        submitPagePropertiesAndExport();
                    } else {
                        showAlertMessage("error", "Check warnings in " + invalidTabs + " tab(s)")
                    }

                } else if (startDate == null || startDate.trim() === '') {
                    showAlertMessage("error", "ERROR - Start Date is required");
                } else if (endDate == null || endDate.trim() === '') {
                    showAlertMessage("error", "ERROR - End Date is required");
                }

                function submitPagePropertiesAndExport() {
                    exportToTargetScheduledAnchorButton.disabled = true;

                    let $pageForm = $('form#explorer-banner-form');

                    $.post( $pageForm.attr("action"), $pageForm.serialize(), function(data) {
                        let status = $(data).find('#Status')?.text();

                        if (status && status === '200') {
                            console.debug("Page properties saved. Start export to Target")
                            exportToTarget();
                        } else {
                            showAlertMessage("error", "ERROR - Unable to save page properties");
                            exportToTargetScheduledAnchorButton.disabled = false;
                        }
                    }, 'html' )
                }

                function exportToTarget() {
                    const requestData = {
                        startDate: startDate,
                        endDate: endDate,
                        oldStartDate: oldStartDate,
                        oldEndDate: oldEndDate,
                        pagePath: pagePath
                    };

                    $.ajax({
                        url: servletUrl,
                        type: "POST",
                        data: JSON.stringify(requestData),
                        dataType: "json",
                        contentType: "application/json",
                        success: function (response, message) {
                            if (response.error) {
                                showAlertMessage("error", "Server ERROR - " + response.error);
                                exportToTargetScheduledAnchorButton.disabled = false;
                                validateDates();
                            } else {
                                let startDateWorkflowId = response.startDateWorkflowId;
                                showAlertMessage("success", response.message + " <br> " +
                                    (startDateWorkflowId ? "Start date workflow ID: " + startDateWorkflowId +
                                        " scheduled on " + response.wfStartDate + "<br>" : "")
                                    + "End date workflow ID: " + response.endDateWorkflowId
                                    + " scheduled on " + response.wfEndDate);
                                exportToTargetScheduledAnchorButton.disabled = false;
                                validateDates();
                            }
                        },
                        error: function (data, message) {
                            showAlertMessage("error", "Server ERROR - " + message + " - " + data.status);
                            exportToTargetScheduledAnchorButton.disabled = false;
                            validateDates();
                        }
                    });
                }

                function showAlertMessage(variant, message) {
                    const exportToTargetWrapper = document.querySelector("#explorerBannerExportToTargetWrapper");
                    let alert = exportToTargetWrapper.querySelector("coral-alert");
                    const info = alert ? alert : new Coral.Alert();
                    info.variant = variant;
                    info.content.innerHTML = message;
                    exportToTargetWrapper.appendChild(info);
                }

                function validateDates() {
                    if (validator) {
                        document.querySelectorAll(validatorSelector).forEach(function (el) {
                            let result = validator.validate(el);
                            if (result) {
                                console.log("Validation error: " + result);
                            } else {
                                console.log("Validation success for element: " + el);
                                if (el.invalid) {
                                    el.invalid = false;
                                    let parentElement = el.parentElement;
                                    parentElement.querySelector("[icon=alert]").remove();
                                    parentElement.querySelector(".coral-Form-errorlabel").remove();
                                    let panelId = parentElement.closest("coral-panel").id;
                                    let promoTab = document.querySelector(`coral-tab[aria-controls=${panelId}]`);
                                    if (promoTab) {
                                        promoTab.invalid = false
                                        promoTab.removeAttribute("aria-invalid");
                                    }
                                }
                            }
                        });
                    } else {
                        console.warn("Validator not found");
                    }
                }

                function getInvalidTabs() {
                    return $("coral-tab.is-invalid:not(.is-selected)").find("coral-tab-label")
                        .map(function() {
                        return $(this).text();
                    }).get()
                }
            });

            function getValidator() {
                let registry = $(window).adaptTo("foundation-registry");
                return registry.get("foundation.validation.validator").find(v => validatorSelector === v.selector)
            }


            function getJCRDate(field) {
                return field.querySelector("[name=jcrValue]")?.value;
            }

            function cleanAEMUrl(url) {
                try {
                    const parsedUrl = new URL(url); // Parse the URL
                    let path = parsedUrl.pathname; // Get the path (without domain)
            
                    // Remove "/editor.html" or "/sites.html" if present at the beginning
                    path = path.replace(/^\/(editor|sites)\.html/, "");
            
                    // Remove ".html" at the end if present
                    path = path.replace(/\.html$/, "");
            
                    return path;
                } catch (error) {
                    console.error("Invalid URL:", error);
                    return null;
                }
            }

        }

    });

});
