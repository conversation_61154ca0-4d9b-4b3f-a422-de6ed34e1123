.basicpage {
    .xf-content-height {
        min-height: 0 !important;
    }
}

.header-bar {
    width: 100%;
    padding-bottom: 1%;
    position: absolute;
    z-index: 99;
    padding-top: 0.5rem;
    a,
    img {
        margin-top: 1%;
        height: 100%;
        width: auto;
        max-height: 100%;
        text-decoration: none;
    }

    .header-bar-light-lp-components {
        width: 100%;
        max-width: 70%;
        margin: 0 auto;
        display: flex;
        flex-wrap: nowrap;
        padding-bottom: 1%;
        position: relative;
        z-index: 99;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        &__img {
            height: auto;
            max-width: 22%;
            cursor: pointer;
            margin-top: 1%;
            display: flex;
            img {
                width: 100%;
                height: 100%;
                padding-right: 0.2rem;
            }
            .regulation-logo {
                margin-top: -1rem;
                img {
                    width: 4.5vw;
                    padding-top: 1rem;
                }
            }
            .medium-logo {
                margin-top: -1rem;
                img {
                    width: 7vw;
                    padding-top: 1rem;
                }
            }
        }

        &__cta {
            margin-top: 1%;
            margin-left: auto;
            .cta-template a {
                padding: 6% 4%;
                min-width: 12vw;
                align-items: center;
                font-size: 1.1vw;
            }
        }
    }

    &.cq-Editable-dom {
        position: relative;
        min-height: 10em;
    }

    @media (max-width: 1080px) {
        padding-top: 0.5rem;
        padding: 12px;

        .header-bar-light-lp-components {
            max-width: 100%;

            &__img {
                max-width: 34%;
                img {
                    max-height: 12vw;
                }
                .regulation-logo img {
                    width: 12vw;
                }
                .medium-logo img {
                    @media (max-width: $md) and (min-width: 768px) and (orientation: portrait) {
                        width: 100%;
                    }
                }
            }
            &__cta .cta-template {
                min-width: 12vw;

                @media (max-width: $md) and (min-width:768px) and (orientation: portrait) {
                    min-width: 22vw;
                }

                a {
                    min-width: 13rem;
                    font-size: 2.6vw;
                }
            }
        }
    }

    @media (max-width: 460px) {
        padding-top: 1.2rem;

        .header-bar-light-lp-components {
            &__img {
                .regulation-logo img {
                    width: 8vw;
                }
                .medium-logo img {
                    width: 12vw;
                }
            }

            &__cta .cta-template a {
                min-width: 26vw;
                border-radius: 4.4vw;
                padding: 0.8rem 0.4rem;
                font-size: 2.6vw;
            }
        }
    }

    @media (max-width: 390px) {
        .header-bar-light-lp-components {
            &__img {
                .regulation-logo img {
                    width: 13vw;
                }
                .medium-logo img {
                    max-height: 12vw;
                    width: 100%;
                }
            }
        }
    }

    @media (max-width: 330px) {
        .header-bar-light-lp-components {
            &__img {
                .medium-logo img {
                    max-height: 14vw;
                    width: 100%;
                }
            }
        }
    }

    @media screen and (orientation: landscape) and (min-width: 500px) and (max-width: 1080px) and (hover: none) {
        width: 100%;
        padding-bottom: 1%;
        position: absolute;
        z-index: 99;
        padding-top: 0.5rem;
        a,
        img {
            margin-top: 1%;
            height: 100%;
            width: auto;
            max-height: 100%;
            text-decoration: none;
        }

        .header-bar-light-lp-components {
            width: 100%;
            max-width: 70%;
            margin: 0 auto;
            display: flex;
            flex-wrap: nowrap;
            padding-bottom: 1%;
            position: relative;
            z-index: 99;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            &__img {
                height: auto;
                max-width: 22%;
                cursor: pointer;
                margin-top: 1%;
                display: flex;
                img {
                    width: 100%;
                    height: 100%;
                    padding-right: 0.2rem;
                }
                .regulation-logo {
                    margin-top: -1rem;
                    img {
                        width: 5vw;
                        padding-top: 1rem;
                    }
                }
            }

            &__cta {
                margin-top: 0%;
                margin-left: auto;
                .cta-template a {
                    padding: 6% 4%;
                    min-width: 12vw;
                    font-size: 1.1vw;
                }
            }
        }
    }

    @media screen and (orientation: landscape) and (min-width: 500px) and (max-width: 1080px) and (max-height: 400px) {
        width: 100%;
        padding-bottom: 1%;
        position: absolute;
        z-index: 99;
        padding-top: 0.5rem;
        a,
        img {
            margin-top: 1%;
            height: 100%;
            width: auto;
            max-height: 100%;
            text-decoration: none;
        }

        .header-bar-light-lp-components {
            width: 100%;
            max-width: 70%;
            margin: 0 auto;
            display: flex;
            flex-wrap: nowrap;
            padding-bottom: 1%;
            position: relative;
            z-index: 99;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            &__img {
                height: auto;
                max-width: 22%;
                cursor: pointer;
                margin-top: 1%;
                display: flex;
                img {
                    width: 100%;
                    height: 100%;
                    padding-right: 0.2rem;
                }
                .regulation-logo {
                    margin-top: -1rem;
                    img {
                        width: 8vw;
                        padding-top: 1rem;
                    }
                }
            }

            &__cta {
                margin-top: 0%;
                margin-left: auto;
                .cta-template a {
                    padding: 6% 4%;
                    min-width: 12vw;
                    font-size: 1.1vw;
                }
            }
        }
    }
}

.one-col-desktop-50 {
    .experiencefragment {
        width: 100%;
    }

    .header-bar {
        padding: 0%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }

    .header-bar-light-lp-components {
        max-width: 100%;
        height: auto;

        &__img {
            height: 100%;
            display: flex;
            align-items: center;
        }

        @media only screen and (min-width: 10rem) {
            &__img {
                a,
                img {
                    width: 10rem;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
            }
        }
    }
}

.root.container:has(.hero-banner-light-lp) {
    .header-bar .header-bar-light-lp-components {
        @media (max-width: 1240px) {
            max-width: 98%;
        }

        @media (max-width: $md) and (min-height: 1024px) and (orientation: portrait) {
            max-width: 90%;
        }

        @media (max-width: 1180px) and (min-height: 820px) and (orientation: landscape) {
            max-width: 96%;
        }
    }
}

.root.container:has(.hero-banner-light-lp .cta-offer) {
    .header-bar .header-bar-light-lp-components {
        @media (max-width: 1240px) and (orientation: landscape) {
            max-width: 74%;
        }
    }
}
