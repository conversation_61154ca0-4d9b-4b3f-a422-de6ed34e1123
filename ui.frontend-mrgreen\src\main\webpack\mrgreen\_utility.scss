// off-canvas-wrap hides excessively long elements in overflow that split the page to the right
// - created for the wide header, but also for other elements
.off-canvas-wrap{
    -webkit-backface-visibility: hidden;
    overflow: hidden;
}

.clearfix::after {
    display: block;
    clear: both;
    content: "";
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030;
}

.sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 998;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-twoline-truncate {
    display: block;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: $sm) {
    .sticky-sm-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020;
    }
}

@media (min-width: $md) {
    .sticky-md-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020;
    }
}

@media (min-width: $lg) {
    .sticky-lg-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020;
    }
}

.hstack {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-self: stretch;
}

.vstack {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    content: "";
}

.floating-pseudo {
    content: '';
    position: absolute;
}

.overlay-transition {
    transition: all;
    transition-duration: .3s;
    transition-timing-function: ease-out;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vr {
    display: inline-block;
    align-self: stretch;
    width: 1px;
    min-height: 1em;
    background-color: currentColor;
    opacity: 0.25;
}

.align-baseline {
    vertical-align: baseline;
}

.align-top {
    vertical-align: top;
}

.align-middle {
    vertical-align: middle;
}

.align-bottom {
    vertical-align: bottom;
}

.align-text-bottom {
    vertical-align: text-bottom;
}

.align-text-top {
    vertical-align: text-top;
}

.float-start {
    float: left;
}

.float-end {
    float: right;
}

.float-none {
    float: none;
}

.opacity-0 {
    opacity: 0;
}

.opacity-25 {
    opacity: 0.25;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-75 {
    opacity: 0.75;
}

.opacity-100 {
    opacity: 1;
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-visible {
    overflow: visible;
}

.overflow-scroll {
    overflow: scroll;
}

.d-inline {
    display: inline;
}

.d-inline-block {
    display: inline-block;
}

.d-block {
    display: block;
}

.d-grid {
    display: grid;
}

.d-table {
    display: table;
}

.d-table-row {
    display: table-row;
}

.d-table-cell {
    display: table-cell;
}

.d-flex {
    display: flex;
}

.d-inline-flex {
    display: inline-flex;
}

.d-none {
    display: none;
}

.shadow {
    box-shadow: 0 0.8rem 1.6rem rgba(0, 0, 0, 0.15);
}

.shadow-sm {
    box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.075);
}

.shadow-lg {
    box-shadow: 0 1.6rem 4.8rem rgba(0, 0, 0, 0.175);
}

.shadow-none {
    box-shadow: none;
}

.position-static {
    position: static;
}

.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;
}

.position-fixed {
    position: fixed;
}

.position-sticky {
    position: -webkit-sticky;
    position: sticky;
}

.top-0 {
    top: 0;
}

.top-50 {
    top: 50%;
}

.top-100 {
    top: 100%;
}

.bottom-0 {
    bottom: 0;
}

.bottom-50 {
    bottom: 50%;
}

.bottom-100 {
    bottom: 100%;
}

.start-0 {
    left: 0;
}

.start-50 {
    left: 50%;
}

.start-100 {
    left: 100%;
}

.end-0 {
    right: 0;
}

.end-50 {
    right: 50%;
}

.end-100 {
    right: 100%;
}

.translate-middle {
    transform: translate(-50%, -50%);
}

.translate-middle-x {
    transform: translateX(-50%);
}

.translate-middle-y {
    transform: translateY(-50%);
}

.border {
    border: 1px solid #dee2e6;
}

.border-0 {
    border: 0;
}

.border-top {
    border-top: 1px solid #dee2e6;
}

.border-top-0 {
    border-top: 0;
}

.border-end {
    border-right: 1px solid #dee2e6;
}

.border-end-0 {
    border-right: 0;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6;
}

.border-bottom-0 {
    border-bottom: 0;
}

.border-start {
    border-left: 1px solid #dee2e6;
}

.border-start-0 {
    border-left: 0;
}


.border-dark {
    border-color: $black;
}

.border-white {
    border-color: $white;
}

.border-1 {
    border-width: 1px;
}

.border-2 {
    border-width: 2px;
}

.border-3 {
    border-width: 3px;
}

.border-4 {
    border-width: 4px;
}

.border-5 {
    border-width: 5px;
}

.w-25 {
    width: 25%;
}

.w-30 {
    width: 30%;
}

.w-50 {
    width: 50%;
}

.w-75 {
    width: 75%;
}

.w-70 {
    width: 70%;
}

.w-100 {
    width: 100%;
}

.w-auto {
    width: auto;
}

.mw-100 {
    max-width: 100%;
}

.vw-100 {
    width: 100vw;
}

.min-vw-100 {
    min-width: 100vw;
}

.h-0 {
    height: 0;
}

.h-25 {
    height: 25%;
}

.h-50 {
    height: 50%;
}

.h-75 {
    height: 75%;
}

.h-100 {
    height: 100%;
}

.h-auto {
    height: auto;
}

.mh-100 {
    max-height: 100%;
}

.vh-100 {
    height: 100vh;
}

.min-vh-100 {
    min-height: 100vh;
}

.flex-fill {
    flex: 1 1 auto;
}

.flex-row {
    flex-direction: row;
}

.flex-column {
    flex-direction: column;
}

.flex-row-reverse {
    flex-direction: row-reverse;
}

.flex-column-reverse {
    flex-direction: column-reverse;
}

.flex-grow-0 {
    flex-grow: 0;
}

.flex-grow-1 {
    flex-grow: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.flex-shrink-1 {
    flex-shrink: 1;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse;
}

.gap-0 {
    gap: 0;
}

.gap-1 {
    gap: 0.4rem;
}

.gap-2 {
    gap: 0.8rem;
}

.gap-3 {
    gap: 1.6rem;
}

.gap-4 {
    gap: 2.4rem;
}

.gap-5 {
    gap: 4.8rem;
}

.justify-content-start {
    justify-content: flex-start;
}

.justify-content-end {
    justify-content: flex-end;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-around {
    justify-content: space-around;
}

.justify-content-evenly {
    justify-content: space-evenly;
}

.align-items-start {
    align-items: flex-start;
}

.align-items-end {
    align-items: flex-end;
}

.align-items-center {
    align-items: center;
}

.align-items-baseline {
    align-items: baseline;
}

.align-items-stretch {
    align-items: stretch;
}

.align-content-start {
    align-content: flex-start;
}

.align-content-end {
    align-content: flex-end;
}

.align-content-center {
    align-content: center;
}

.align-content-between {
    align-content: space-between;
}

.align-content-around {
    align-content: space-around;
}

.align-content-stretch {
    align-content: stretch;
}

.align-self-auto {
    align-self: auto;
}

.align-self-start {
    align-self: flex-start;
}

.align-self-end {
    align-self: flex-end;
}

.align-self-center {
    align-self: center;
}

.align-self-baseline {
    align-self: baseline;
}

.align-self-stretch {
    align-self: stretch;
}

.flex-centered {
    display: flex;
    justify-content: center;
    align-items: center;
}

.order-first {
    order: -1;
}

.order-0 {
    order: 0;
}

.order-1 {
    order: 1;
}

.order-2 {
    order: 2;
}

.order-3 {
    order: 3;
}

.order-4 {
    order: 4;
}

.order-5 {
    order: 5;
}


.order-last {
    order: 6;
}

// margin dedicate for template
.mt-small {
    margin-top: 0;
    @media (min-width: $md) {
        margin-top: 1.5rem; // 15px;
    }
}

.mt-medium {
    margin-top: 0;
    @media (min-width: $md) {
        margin-top: 3.2rem; // 32px;
    }
}

.mt-large {
    margin-top: 0;
    @media (min-width: $md) {
        margin-top: 4.5rem; // 45px;
    }
}

// padding for components - from padding tab
.pt-small {
    padding-top: .8rem; // 8px;
    @media (min-width: $sm) {
        padding-top: 1.6rem; // 16px;
    }
}

.pt-medium {
    padding-top: 1.6rem; // 16px;
    @media (min-width: $sm) {
        padding-top: 3.2rem; // 32px;
    }
}

.pt-large {
    padding-top: 2.4rem; // 24px;
    @media (min-width: $sm) {
        padding-top: 4.8rem; // 48px;
    }
}

.pt-xlarge {
    padding-top: 9.6rem; // 96px;
    @media (min-width: $sm) {
        padding-top: 6.4rem; // 64px;
    }
}

.pb-small {
    padding-bottom: .8rem; // 8px;
    @media (min-width: $sm) {
        padding-bottom: 1.6rem; // 16px;
    }
}

.pb-medium {
    padding-bottom: 1.6rem; // 16px;
    @media (min-width: $sm) {
        padding-bottom: 3.2rem; // 32px;
    }
}

.pb-large {
    padding-bottom: 2.4rem; // 24px;
    @media (min-width: $sm) {
        padding-bottom: 4.8rem; // 48px;
    }
}

.pb-xlarge {
    padding-bottom: 4rem; // 40px;
    @media (min-width: $sm) {
        padding-bottom: 6.4rem; // 64px;
    }
}


.m-0 {
    margin: 0;
}

.m-1 {
    margin: 0.4rem;
}

.m-2 {
    margin: 0.8rem;
}

.m-3 {
    margin: 1.6rem;
}

.m-4 {
    margin: 2.4rem;
}

.m-5 {
    margin: 4.8rem;
}

.m-auto {
    margin: auto;
}

.m-0-auto {
    margin: 0 auto;
}

.mx-0 {
    margin-right: 0;
    margin-left: 0;
}

.mx-1 {
    margin-right: 0.4rem;
    margin-left: 0.4rem;
}

.mx-2 {
    margin-right: 0.8rem;
    margin-left: 0.8rem;
}

.mx-3 {
    margin-right: 1.6rem;
    margin-left: 1.6rem;
}

.mx-4 {
    margin-right: 2.4rem;
    margin-left: 2.4rem;
}

.mx-5 {
    margin-right: 4.8rem;
    margin-left: 4.8rem;
}

.mx-auto {
    margin-right: auto;
    margin-left: auto;
}

.my-0 {
    margin-top: 0;
    margin-bottom: 0;
}

.my-1 {
    margin-top: 0.4rem;
    margin-bottom: 0.4rem;
}

.my-2 {
    margin-top: 0.8rem;
    margin-bottom: 0.8rem;
}

.my-3 {
    margin-top: 1.6rem;
    margin-bottom: 1.6rem;
}

.my-4 {
    margin-top: 2.4rem;
    margin-bottom: 2.4rem;
}

.my-5 {
    margin-top: 4.8rem;
    margin-bottom: 4.8rem;
}

.my-auto {
    margin-top: auto;
    margin-bottom: auto;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 0.4rem;
}

.mt-2 {
    margin-top: 0.8rem;
}

.mt-3 {
    margin-top: 1.6rem;
}

.mt-4 {
    margin-top: 2.4rem;
}

.mt-5 {
    margin-top: 4.8rem;
}

.mt-auto {
    margin-top: auto;
}

.me-0 {
    margin-right: 0;
}

.me-1 {
    margin-right: 0.4rem;
}

.me-2 {
    margin-right: 0.8rem;
}

.me-3 {
    margin-right: 1.6rem;
}

.me-4 {
    margin-right: 2.4rem;
}

.me-5 {
    margin-right: 4.8rem;
}

.me-auto {
    margin-right: auto;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.4rem;
}

.mb-2 {
    margin-bottom: 0.8rem;
}

.mb-3 {
    margin-bottom: 1.6rem;
}

.mb-4 {
    margin-bottom: 2.4rem;
}

.mb-5 {
    margin-bottom: 4.8rem;
}

.mb-auto {
    margin-bottom: auto;
}

.ms-0 {
    margin-left: 0;
}

.ms-1 {
    margin-left: 0.4rem;
}

.ms-2 {
    margin-left: 0.8rem;
}

.ms-3 {
    margin-left: 1.6rem;
}

.ms-4 {
    margin-left: 2.4rem;
}

.ms-5 {
    margin-left: 4.8em;
}

.ms-auto {
    margin-left: auto;
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: 0.4rem;
}

.p-2 {
    padding: 0.8rem;
}

.p-3 {
    padding: 1.6rem;
}

.p-4 {
    padding: 2.4rem;
}

.p-5 {
    padding: 4.8rem;
}

.px-0 {
    padding-right: 0;
    padding-left: 0;
}

.px-1 {
    padding-right: 0.4em;
    padding-left: 0.4rem;
}

.px-2 {
    padding-right: 0.8rem;
    padding-left: 0.8rem;
}

.px-3 {
    padding-right: 1.6rem;
    padding-left: 1.6rem;
}

.px-4 {
    padding-right: 2.4rem;
    padding-left: 2.4rem;
}

.px-5 {
    padding-right: 4.8rem;
    padding-left: 4.8rem;
}

.py-0 {
    padding-top: 0;
    padding-bottom: 0;
}

.py-1 {
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
}

.py-2 {
    padding-top: 0.8rem;
    padding-bottom: 0.8rem;
}

.py-3 {
    padding-top: 1.6rem;
    padding-bottom: 1.6rem;
}

.py-4 {
    padding-top: 2.4rem;
    padding-bottom: 2.4rem;
}

.py-5 {
    padding-top: 4.8rem;
    padding-bottom: 4.8rem;
}

.pt-0 {
    padding-top: 0;
}

.pt-1 {
    padding-top: 0.4rem;
}

.pt-2 {
    padding-top: 0.8rem;
}

.pt-3 {
    padding-top: 1.6rem;
}

.pt-4 {
    padding-top: 2.4rem;
}

.pt-5 {
    padding-top: 4.8rem;
}

.pe-0 {
    padding-right: 0;
}

.pe-1 {
    padding-right: 0.4rem;
}

.pe-2 {
    padding-right: 0.8rem;
}

.pe-3 {
    padding-right: 1.6rem;
}

.pe-4 {
    padding-right: 2.4rem;
}

.pe-5 {
    padding-right: 4.8rem;
}

.pb-0 {
    padding-bottom: 0;
}

.pb-1 {
    padding-bottom: 0.4rem;
}

.pb-2 {
    padding-bottom: 0.8rem;
}

.pb-3 {
    padding-bottom: 1.6rem;
}

.pb-4 {
    padding-bottom: 2.4rem;
}

.pb-5 {
    padding-bottom: 4.8rem;
}

.ps-0 {
    padding-left: 0;
}

.ps-1 {
    padding-left: 0.4rem;
}

.ps-2 {
    padding-left: 0.8rem;
}

.ps-3 {
    padding-left: 1.6rem;
}

.ps-4 {
    padding-left: 2.4rem;
}

.ps-5 {
    padding-left: 4.8rem;
}

// Font Family
.jost-light-family {
    font-family: $font-family-13;
}

.default-extra-condensed-family {
    font-family: $font-family-14;
}

.extra-888-condensed-family {
    font-family: $font-family-19;
}

.jost-regular-family {
    font-family: $font-family-12;
}
.BookmanJFProItalic-family{
    font-family: $font-family-21;
}

.demi-bold-888-family {
    font-family: $font-family-15;
}

.ultra-888-family {
    font-family: $font-family-16;
}

.bold-888 {
    font-family: $font-family-18;
}

// Font Size
.fs-1 {
    font-size: $font-size-1;
}

.fs-2 {
    font-size: $font-size-2;
}

.fs-3 {
    font-size: $font-size-3;
}

.fs-4 {
    font-size: $font-size-4;
}

.fs-5 {
    font-size: $font-size-5;
}

.fs-6 {
    font-size: $font-size-6;
}

.smaller-text-rich-text {
    font-size: 1.2rem;
}

.font-size-10 {
    font-size: 10px;
  }
.font-size-12 {
    font-size: 12px;
  }
  .font-size-14 {
    font-size: 14px;
  }
  .font-size-15 {
    font-size: 15px;
  }
  .font-size-16 {
    font-size: 16px;
  }
  .font-size-18 {
    font-size: 18px;
  }
  .font-size-20 {
    font-size: 20px;
  }
  .font-size-22 {
    font-size: 22px;
  }
  .font-size-24 {
    font-size: 24px;
  }
  .font-size-24-half {
    font-size: 24.5px;
    font-weight: 600;
  }
  .font-size-26 {
    font-size: 26px;
  }
  .font-size-28 {
    font-size: 28px;
  }
  .font-size-30 {
    font-size: 30px;
  }
  .font-size-32 {
    font-size: 32px;
  }
  .font-size-34 {
    font-size: 34px;
  }
  .font-size-36 {
    font-size: 36px;
  }
  .font-size-38 {
    font-size: 38px;
  }
  .font-size-40 {
    font-size: 40px;
  }
  .font-size-404-v1 {
    font-size: 24.5px;
    font-weight: 600;
    font-family: "888-Medium";
  }
  .font-size-404-v2 {
    font-size: 24.5px;
    font-weight: 700;
    font-family: '888', "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  }

  .font-size-404-responsive {
    font-family: '888';
    font-weight: 600;
    font-size: 24.5px;
    @media (max-width: 475px){
        font-size: 17.5px;
    }
  }

.font-size-3vw {
    font-size: 3vw;

    @media (max-width: $md) and (orientation: portrait) {
        font-size: 3.8rem;
    }

    @media (max-width: 500px) {
        font-size: 3rem;
    }
}

.font-size-2vw {
    font-size: 2.2vw;
    display: inline-block;
    line-height: 1.2;

    @media (max-width: 1240px) {
        font-size: 2.4vw;
    }

    @media (max-width: $md) and (orientation: portrait) {
        font-size: 2.8rem;
    }
    
    @media (max-width: 500px) {
        font-size: 1.9rem;
    }
}

.font-size-065 {
    font-size: 0.65vw;

    @media (max-width: $md) and (orientation: portrait) {
        font-size: 2.1vw;
    }

    @media (max-width: 460px) {
        font-size: 1.1rem;
    }
}

.font-size-12vw {
    font-size: 18px;

    @media only screen and (min-width: 1400px) and (orientation: landscape) {
        font-size: 1.286vw;
    }

    @media only screen and (max-width: 1240px) and (orientation: landscape) {
        font-size: 1.4516vw;
    }

    @media only screen and (max-width: 500px) {
        font-size: 15px;
    }
}

.font-size-32vw {
    font-size: 44px;

    @media only screen and (min-width: 1400px) and (orientation: landscape) {
        font-size: 3.2vw;
    }

    @media only screen and (max-width: 1240px) and (orientation: landscape) {
        font-size: 3vw;
    }

    @media only screen and (max-width: 500px) {
        font-size: 30px;
    }
}
  
.bigger-text-rich-text {
    font-size: 2.24rem;
}

.text-xs {
    font-size: 1rem; //10
    line-height: 1.3;
}

.fst-italic {
    font-style: italic;
}

.fst-normal {
    font-style: normal;
}


.lh-1 {
    line-height: 1;
}

.lh-sm {
    line-height: 1.25;
}

.lh-base {
    line-height: 1.5;
}

.lh-lg {
    line-height: 2;
}

.text-start, .text-left {
    text-align: left;
}

.text-end, .text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-decoration-none {
    text-decoration: none;
}

.text-decoration-underline {
    text-decoration: underline;
}

.text-decoration-line-through {
    text-decoration: line-through;
}

.text-lowercase {
    text-transform: lowercase;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-capitalize {
    text-transform: capitalize;
}

.text-wrap {
    white-space: normal;
}

.text-nowrap {
    white-space: nowrap;
}

.text-break {
    word-wrap: break-word;
    word-break: break-word;
}

.text-light {
    color: $light
}

.text-dark {
    color: $black
}

.text-black {
    color: $black
}

.text-white {
    color: $white
}

.text-body {
    color: $color-font
}

.text-muted {
    color: #6c757d;
}

.text-black-50 {
    color: rgba(0, 0, 0, 0.5);
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5);
}

.text-reset {
    color: inherit;
}

.opacity-10 {
    opacity: 0.10;
}

.opacity-20 {
    opacity: 0.2;
}

.opacity-25 {
    opacity: 0.25;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-75 {
    opacity: 0.75;
}

.opacity-100 {
    opacity: 1;
}

.bg-light {
    background-color: $light
}

.bg-dark {
    background-color: $black
}

.bg-black {
    background-color: $black
}

.bg-white {
    background-color: $white
}

.bg-body {
    background-color: $color-background
}

.bg-transparent {
    background-color: transparent;
}

.bg-gradient {
    background-image: $linear-gradient;
}

.bglg-gradient {
    background-image: $linear-gradient-light-grey;
}

.user-select-all {
    -webkit-user-select: all;
    -moz-user-select: all;
    user-select: all;
}

.user-select-auto {
    -webkit-user-select: auto;
    -moz-user-select: auto;
    user-select: auto;
}

.user-select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.pe-none {
    pointer-events: none;
}

.pe-auto {
    pointer-events: auto;
}

.rounded {
    border-radius: 0.4rem;
}

.rounded-0 {
    border-radius: 0;
}

.rounded-1 {
    border-radius: 0.2rem;
}

.rounded-2 {
    border-radius: 0.4rem;
}

.rounded-3 {
    border-radius: 0.4.8rem;
}

.rounded-circle {
    border-radius: 50%;
}

.rounded-pill {
    border-radius: $border-radius;
}

.rounded-top {
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
}

.rounded-end {
    border-top-right-radius: 0.4rem;
    border-bottom-right-radius: 0.4rem;
}

.rounded-bottom {
    border-bottom-right-radius: 0.4rem;
    border-bottom-left-radius: 0.4rem;
}

.rounded-start {
    border-bottom-left-radius: 0.4rem;
    border-top-left-radius: 0.4rem;
}

.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden;
}

// font-weight
.fw-regular{ font-weight: $font-weight-regular;} //400
.fw-normal{ font-weight: $font-weight-normal;} //400
.fw-medium{ font-weight: $font-weight-medium;} //500
.fw-semi-bold{ font-weight: $font-weight-semi-bold;} //600
.fw-bold{ font-weight: $font-weight-bold;} //700
.fw-medium-bold{ font-weight: $font-weight-medium-bold;} //800
.fw-extra-bold{ font-weight: $font-weight-extra-bold;} //900
.fw-black{ font-weight: $font-weight-black;} //900

@media (min-width: $sm) {
    .float-sm-start {
        float: left;
    }

    .float-sm-end {
        float: right;
    }

    .float-sm-none {
        float: none;
    }

    .d-sm-inline {
        display: inline;
    }

    .d-sm-inline-block {
        display: inline-block;
    }

    .d-sm-block {
        display: block;
    }

    .d-sm-grid {
        display: grid;
    }

    .d-sm-table {
        display: table;
    }

    .d-sm-table-row {
        display: table-row;
    }

    .d-sm-table-cell {
        display: table-cell;
    }

    .d-sm-flex {
        display: flex;
    }

    .d-sm-inline-flex {
        display: inline-flex;
    }

    .d-sm-none {
        display: none;
    }

    .flex-sm-fill {
        flex: 1 1 auto;
    }

    .flex-sm-row {
        flex-direction: row;
    }

    .flex-sm-column {
        flex-direction: column;
    }

    .flex-sm-row-reverse {
        flex-direction: row-reverse;
    }

    .flex-sm-column-reverse {
        flex-direction: column-reverse;
    }

    .flex-sm-grow-0 {
        flex-grow: 0;
    }

    .flex-sm-grow-1 {
        flex-grow: 1;
    }

    .flex-sm-shrink-0 {
        flex-shrink: 0;
    }

    .flex-sm-shrink-1 {
        flex-shrink: 1;
    }

    .flex-sm-wrap {
        flex-wrap: wrap;
    }

    .flex-sm-nowrap {
        flex-wrap: nowrap;
    }

    .flex-sm-wrap-reverse {
        flex-wrap: wrap-reverse;
    }

    .gap-sm-0 {
        gap: 0;
    }

    .gap-sm-1 {
        gap: 0.4rem;
    }

    .gap-sm-2 {
        gap: 0.8rem;
    }

    .gap-sm-3 {
        gap: 1.6rem;
    }

    .gap-sm-4 {
        gap: 2.4rem;
    }

    .gap-sm-5 {
        gap: 4.8rem;
    }

    .justify-content-sm-start {
        justify-content: flex-start;
    }

    .justify-content-sm-end {
        justify-content: flex-end;
    }

    .justify-content-sm-center {
        justify-content: center;
    }

    .justify-content-sm-between {
        justify-content: space-between;
    }

    .justify-content-sm-around {
        justify-content: space-around;
    }

    .justify-content-sm-evenly {
        justify-content: space-evenly;
    }

    .align-items-sm-start {
        align-items: flex-start;
    }

    .align-items-sm-end {
        align-items: flex-end;
    }

    .align-items-sm-center {
        align-items: center;
    }

    .align-items-sm-baseline {
        align-items: baseline;
    }

    .align-items-sm-stretch {
        align-items: stretch;
    }

    .align-content-sm-start {
        align-content: flex-start;
    }

    .align-content-sm-end {
        align-content: flex-end;
    }

    .align-content-sm-center {
        align-content: center;
    }

    .align-content-sm-between {
        align-content: space-between;
    }

    .align-content-sm-around {
        align-content: space-around;
    }

    .align-content-sm-stretch {
        align-content: stretch;
    }

    .align-self-sm-auto {
        align-self: auto;
    }

    .align-self-sm-start {
        align-self: flex-start;
    }

    .align-self-sm-end {
        align-self: flex-end;
    }

    .align-self-sm-center {
        align-self: center;
    }

    .align-self-sm-baseline {
        align-self: baseline;
    }

    .align-self-sm-stretch {
        align-self: stretch;
    }

    .order-sm-first {
        order: -1;
    }

    .order-sm-0 {
        order: 0;
    }

    .order-sm-1 {
        order: 1;
    }

    .order-sm-2 {
        order: 2;
    }

    .order-sm-3 {
        order: 3;
    }

    .order-sm-4 {
        order: 4;
    }

    .order-sm-5 {
        order: 5;
    }

    .order-sm-last {
        order: 6;
    }

    .m-sm-0 {
        margin: 0;
    }

    .m-sm-1 {
        margin: 0.4rem;
    }

    .m-sm-2 {
        margin: 0.8rem;
    }

    .m-sm-3 {
        margin: 1.6rem;
    }

    .m-sm-4 {
        margin: 2.4rem;
    }

    .m-sm-5 {
        margin: 4.8rem;
    }

    .m-sm-auto {
        margin: auto;
    }

    .mx-sm-0 {
        margin-right: 0;
        margin-left: 0;
    }

    .mx-sm-1 {
        margin-right: 0.4rem;
        margin-left: 0.4rem;
    }

    .mx-sm-2 {
        margin-right: 0.8rem;
        margin-left: 0.8rem;
    }

    .mx-sm-3 {
        margin-right: 1.6rem;
        margin-left: 1.6rem;
    }

    .mx-sm-4 {
        margin-right: 2.4rem;
        margin-left: 2.4rem;
    }

    .mx-sm-5 {
        margin-right: 4.8rem;
        margin-left: 4.8rem;
    }

    .mx-sm-auto {
        margin-right: auto;
        margin-left: auto;
    }

    .my-sm-0 {
        margin-top: 0;
        margin-bottom: 0;
    }

    .my-sm-1 {
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
    }

    .my-sm-2 {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .my-sm-3 {
        margin-top: 1.6rem;
        margin-bottom: 1.6rem;
    }

    .my-sm-4 {
        margin-top: 2.4rem;
        margin-bottom: 2.4rem;
    }

    .my-sm-5 {
        margin-top: 4.8rem;
        margin-bottom: 4.8rem;
    }

    .my-sm-auto {
        margin-top: auto;
        margin-bottom: auto;
    }

    .mt-sm-0 {
        margin-top: 0;
    }

    .mt-sm-1 {
        margin-top: 0.4rem;
    }

    .mt-sm-2 {
        margin-top: 0.8rem;
    }

    .mt-sm-3 {
        margin-top: 1.6rem;
    }

    .mt-sm-4 {
        margin-top: 2.4rem;
    }

    .mt-sm-5 {
        margin-top: 4.8rem;
    }

    .mt-sm-auto {
        margin-top: auto;
    }

    .me-sm-0 {
        margin-right: 0;
    }

    .me-sm-1 {
        margin-right: 0.4rem;
    }

    .me-sm-2 {
        margin-right: 0.8rem;
    }

    .me-sm-3 {
        margin-right: 1.6rem;
    }

    .me-sm-4 {
        margin-right: 2.4rem;
    }

    .me-sm-5 {
        margin-right: 4.8rem;
    }

    .me-sm-auto {
        margin-right: auto;
    }

    .mb-sm-0 {
        margin-bottom: 0;
    }

    .mb-sm-1 {
        margin-bottom: 0.4rem;
    }

    .mb-sm-2 {
        margin-bottom: 0.8rem;
    }

    .mb-sm-3 {
        margin-bottom: 1.6rem;
    }

    .mb-sm-4 {
        margin-bottom: 2.4rem;
    }

    .mb-sm-5 {
        margin-bottom: 4.8rem;
    }

    .mb-sm-auto {
        margin-bottom: auto;
    }

    .ms-sm-0 {
        margin-left: 0;
    }

    .ms-sm-1 {
        margin-left: 0.4rem;
    }

    .ms-sm-2 {
        margin-left: 0.8rem;
    }

    .ms-sm-3 {
        margin-left: 1.6rem;
    }

    .ms-sm-4 {
        margin-left: 2.4rem;
    }

    .ms-sm-5 {
        margin-left: 4.8rem;
    }

    .ms-sm-auto {
        margin-left: auto;
    }

    .p-sm-0 {
        padding: 0;
    }

    .p-sm-1 {
        padding: 0.4rem;
    }

    .p-sm-2 {
        padding: 0.8rem;
    }

    .p-sm-3 {
        padding: 1.6rem;
    }

    .p-sm-4 {
        padding: 2.4rem;
    }

    .p-sm-5 {
        padding: 4.8rem;
    }

    .px-sm-0 {
        padding-right: 0;
        padding-left: 0;
    }

    .px-sm-1 {
        padding-right: 0.4rem;
        padding-left: 0.4rem;
    }

    .px-sm-2 {
        padding-right: 0.8rem;
        padding-left: 0.8rem;
    }

    .px-sm-3 {
        padding-right: 1.6rem;
        padding-left: 1.6rem;
    }

    .px-sm-4 {
        padding-right: 2.4rem;
        padding-left: 2.4rem;
    }

    .px-sm-5 {
        padding-right: 4.8rem;
        padding-left: 4.8rem;
    }

    .py-sm-0 {
        padding-top: 0;
        padding-bottom: 0;
    }

    .py-sm-1 {
        padding-top: 0.4rem;
        padding-bottom: 0.4rem;
    }

    .py-sm-2 {
        padding-top: 0.8rem;
        padding-bottom: 0.8rem;
    }

    .py-sm-3 {
        padding-top: 1.6rem;
        padding-bottom: 1.6rem;
    }

    .py-sm-4 {
        padding-top: 2.4rem;
        padding-bottom: 2.4rem;
    }

    .py-sm-5 {
        padding-top: 4.8rem;
        padding-bottom: 4.8rem;
    }

    .pt-sm-0 {
        padding-top: 0;
    }

    .pt-sm-1 {
        padding-top: 0.4rem;
    }

    .pt-sm-2 {
        padding-top: 0.8rem;
    }

    .pt-sm-3 {
        padding-top: 1.6rem;
    }

    .pt-sm-4 {
        padding-top: 2.4rem;
    }

    .pt-sm-5 {
        padding-top: 4.8rem;
    }

    .pe-sm-0 {
        padding-right: 0;
    }

    .pe-sm-1 {
        padding-right: 0.4rem;
    }

    .pe-sm-2 {
        padding-right: 0.8rem;
    }

    .pe-sm-3 {
        padding-right: 1.6rem;
    }

    .pe-sm-4 {
        padding-right: 2.4rem;
    }

    .pe-sm-5 {
        padding-right: 4.8rem;
    }

    .pb-sm-0 {
        padding-bottom: 0;
    }

    .pb-sm-1 {
        padding-bottom: 0.4rem;
    }

    .pb-sm-2 {
        padding-bottom: 0.8rem;
    }

    .pb-sm-3 {
        padding-bottom: 1.6rem;
    }

    .pb-sm-4 {
        padding-bottom: 2.4rem;
    }

    .pb-sm-5 {
        padding-bottom: 4.8rem;
    }

    .ps-sm-0 {
        padding-left: 0;
    }

    .ps-sm-1 {
        padding-left: 0.4rem;
    }

    .ps-sm-2 {
        padding-left: 0.8rem;
    }

    .ps-sm-3 {
        padding-left: 1.6rem;
    }

    .ps-sm-4 {
        padding-left: 2.4rem;
    }

    .ps-sm-5 {
        padding-left: 4.8rem;
    }

    .text-sm-start {
        text-align: left;
    }

    .text-sm-end {
        text-align: right;
    }

    .text-sm-center {
        text-align: center;
    }
}

@media (min-width: $md) {
    .float-md-start {
        float: left;
    }

    .float-md-end {
        float: right;
    }

    .float-md-none {
        float: none;
    }

    .d-md-inline {
        display: inline;
    }

    .d-md-inline-block {
        display: inline-block;
    }

    .d-md-block {
        display: block;
    }

    .d-md-grid {
        display: grid;
    }

    .d-md-table {
        display: table;
    }

    .d-md-table-row {
        display: table-row;
    }

    .d-md-table-cell {
        display: table-cell;
    }

    .d-md-flex {
        display: flex;
    }

    .d-md-inline-flex {
        display: inline-flex;
    }

    .d-md-none {
        display: none;
    }

    .flex-md-fill {
        flex: 1 1 auto;
    }

    .flex-md-row {
        flex-direction: row;
    }

    .flex-md-column {
        flex-direction: column;
    }

    .flex-md-row-reverse {
        flex-direction: row-reverse;
    }

    .flex-md-column-reverse {
        flex-direction: column-reverse;
    }

    .flex-md-grow-0 {
        flex-grow: 0;
    }

    .flex-md-grow-1 {
        flex-grow: 1;
    }

    .flex-md-shrink-0 {
        flex-shrink: 0;
    }

    .flex-md-shrink-1 {
        flex-shrink: 1;
    }

    .flex-md-wrap {
        flex-wrap: wrap;
    }

    .flex-md-nowrap {
        flex-wrap: nowrap;
    }

    .flex-md-wrap-reverse {
        flex-wrap: wrap-reverse;
    }

    .gap-md-0 {
        gap: 0;
    }

    .gap-md-1 {
        gap: 0.4rem;
    }

    .gap-md-2 {
        gap: 0.8rem;
    }

    .gap-md-3 {
        gap: 1.6rem;
    }

    .gap-md-4 {
        gap: 2.4rem;
    }

    .gap-md-5 {
        gap: 4.8rem;
    }

    .justify-content-md-start {
        justify-content: flex-start;
    }

    .justify-content-md-end {
        justify-content: flex-end;
    }

    .justify-content-md-center {
        justify-content: center;
    }

    .justify-content-md-between {
        justify-content: space-between;
    }

    .justify-content-md-around {
        justify-content: space-around;
    }

    .justify-content-md-evenly {
        justify-content: space-evenly;
    }

    .align-items-md-start {
        align-items: flex-start;
    }

    .align-items-md-end {
        align-items: flex-end;
    }

    .align-items-md-center {
        align-items: center;
    }

    .align-items-md-baseline {
        align-items: baseline;
    }

    .align-items-md-stretch {
        align-items: stretch;
    }

    .align-content-md-start {
        align-content: flex-start;
    }

    .align-content-md-end {
        align-content: flex-end;
    }

    .align-content-md-center {
        align-content: center;
    }

    .align-content-md-between {
        align-content: space-between;
    }

    .align-content-md-around {
        align-content: space-around;
    }

    .align-content-md-stretch {
        align-content: stretch;
    }

    .align-self-md-auto {
        align-self: auto;
    }

    .align-self-md-start {
        align-self: flex-start;
    }

    .align-self-md-end {
        align-self: flex-end;
    }

    .align-self-md-center {
        align-self: center;
    }

    .align-self-md-baseline {
        align-self: baseline;
    }

    .align-self-md-stretch {
        align-self: stretch;
    }

    .order-md-first {
        order: -1;
    }

    .order-md-0 {
        order: 0;
    }

    .order-md-1 {
        order: 1;
    }

    .order-md-2 {
        order: 2;
    }

    .order-md-3 {
        order: 3;
    }

    .order-md-4 {
        order: 4;
    }

    .order-md-5 {
        order: 5;
    }

    .order-md-last {
        order: 6;
    }

    .m-md-0 {
        margin: 0;
    }

    .m-md-1 {
        margin: 0.4rem;
    }

    .m-md-2 {
        margin: 0.8rem;
    }

    .m-md-3 {
        margin: 1.6rem;
    }

    .m-md-4 {
        margin: 2.4rem;
    }

    .m-md-5 {
        margin: 4.8rem;
    }

    .m-md-auto {
        margin: auto;
    }

    .mx-md-0 {
        margin-right: 0;
        margin-left: 0;
    }

    .mx-md-1 {
        margin-right: 0.4rem;
        margin-left: 0.4rem;
    }

    .mx-md-2 {
        margin-right: 0.8rem;
        margin-left: 0.8rem;
    }

    .mx-md-3 {
        margin-right: 1.6rem;
        margin-left: 1.6rem;
    }

    .mx-md-4 {
        margin-right: 2.4rem;
        margin-left: 2.4rem;
    }

    .mx-md-5 {
        margin-right: 4.8rem;
        margin-left: 4.8rem;
    }

    .mx-md-auto {
        margin-right: auto;
        margin-left: auto;
    }

    .my-md-0 {
        margin-top: 0;
        margin-bottom: 0;
    }

    .my-md-1 {
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
    }

    .my-md-2 {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .my-md-3 {
        margin-top: 1.6rem;
        margin-bottom: 1.6rem;
    }

    .my-md-4 {
        margin-top: 2.4rem;
        margin-bottom: 2.4rem;
    }

    .my-md-5 {
        margin-top: 4.8rem;
        margin-bottom: 4.8rem;
    }

    .my-md-auto {
        margin-top: auto;
        margin-bottom: auto;
    }

    .mt-md-0 {
        margin-top: 0;
    }

    .mt-md-1 {
        margin-top: 0.4rem;
    }

    .mt-md-2 {
        margin-top: 0.8rem;
    }

    .mt-md-3 {
        margin-top: 1.6rem;
    }

    .mt-md-4 {
        margin-top: 2.4rem;
    }

    .mt-md-5 {
        margin-top: 4.8rem;
    }

    .mt-md-auto {
        margin-top: auto;
    }

    .me-md-0 {
        margin-right: 0;
    }

    .me-md-1 {
        margin-right: 0.4rem;
    }

    .me-md-2 {
        margin-right: 0.8rem;
    }

    .me-md-3 {
        margin-right: 1.6rem;
    }

    .me-md-4 {
        margin-right: 2.4rem;
    }

    .me-md-5 {
        margin-right: 4.8rem;
    }

    .me-md-auto {
        margin-right: auto;
    }

    .mb-md-0 {
        margin-bottom: 0;
    }

    .mb-md-1 {
        margin-bottom: 0.4rem;
    }

    .mb-md-2 {
        margin-bottom: 0.8rem;
    }

    .mb-md-3 {
        margin-bottom: 1.6rem;
    }

    .mb-md-4 {
        margin-bottom: 2.4rem;
    }

    .mb-md-5 {
        margin-bottom: 4.8rem;
    }

    .mb-md-auto {
        margin-bottom: auto;
    }

    .ms-md-0 {
        margin-left: 0;
    }

    .ms-md-1 {
        margin-left: 0.4rem;
    }

    .ms-md-2 {
        margin-left: 0.8rem;
    }

    .ms-md-3 {
        margin-left: 1.6rem;
    }

    .ms-md-4 {
        margin-left: 2.4rem;
    }

    .ms-md-5 {
        margin-left: 4.8rem;
    }

    .ms-md-auto {
        margin-left: auto;
    }

    .p-md-0 {
        padding: 0;
    }

    .p-md-1 {
        padding: 0.4rem;
    }

    .p-md-2 {
        padding: 0.8rem;
    }

    .p-md-3 {
        padding: 1.6rem;
    }

    .p-md-4 {
        padding: 2.4rem;
    }

    .p-md-5 {
        padding: 4.8rem;
    }

    .px-md-0 {
        padding-right: 0;
        padding-left: 0;
    }

    .px-md-1 {
        padding-right: 0.4rem;
        padding-left: 0.4rem;
    }

    .px-md-2 {
        padding-right: 0.8rem;
        padding-left: 0.8rem;
    }

    .px-md-3 {
        padding-right: 1.6rem;
        padding-left: 1.6rem;
    }

    .px-md-4 {
        padding-right: 2.4rem;
        padding-left: 2.4rem;
    }

    .px-md-5 {
        padding-right: 4.8rem;
        padding-left: 4.8rem;
    }

    .py-md-0 {
        padding-top: 0;
        padding-bottom: 0;
    }

    .py-md-1 {
        padding-top: 0.4rem;
        padding-bottom: 0.4rem;
    }

    .py-md-2 {
        padding-top: 0.8rem;
        padding-bottom: 0.8rem;
    }

    .py-md-3 {
        padding-top: 1.6rem;
        padding-bottom: 1.6rem;
    }

    .py-md-4 {
        padding-top: 2.4rem;
        padding-bottom: 2.4rem;
    }

    .py-md-5 {
        padding-top: 4.8rem;
        padding-bottom: 4.8rem;
    }

    .pt-md-0 {
        padding-top: 0;
    }

    .pt-md-1 {
        padding-top: 0.4rem;
    }

    .pt-md-2 {
        padding-top: 0.8rem;
    }

    .pt-md-3 {
        padding-top: 1.6rem;
    }

    .pt-md-4 {
        padding-top: 2.4rem;
    }

    .pt-md-5 {
        padding-top: 4.8rem;
    }

    .pe-md-0 {
        padding-right: 0;
    }

    .pe-md-1 {
        padding-right: 0.4rem;
    }

    .pe-md-2 {
        padding-right: 0.8rem;
    }

    .pe-md-3 {
        padding-right: 1.6rem;
    }

    .pe-md-4 {
        padding-right: 2.4rem;
    }

    .pe-md-5 {
        padding-right: 4.8rem;
    }

    .pb-md-0 {
        padding-bottom: 0;
    }

    .pb-md-1 {
        padding-bottom: 0.4rem;
    }

    .pb-md-2 {
        padding-bottom: 0.8rem;
    }

    .pb-md-3 {
        padding-bottom: 1.6rem;
    }

    .pb-md-4 {
        padding-bottom: 2.4rem;
    }

    .pb-md-5 {
        padding-bottom: 4.8rem;
    }

    .ps-md-0 {
        padding-left: 0;
    }

    .ps-md-1 {
        padding-left: 0.4rem;
    }

    .ps-md-2 {
        padding-left: 0.8rem;
    }

    .ps-md-3 {
        padding-left: 1.6rem;
    }

    .ps-md-4 {
        padding-left: 2.4rem;
    }

    .ps-md-5 {
        padding-left: 4.8rem;
    }

    .text-md-start {
        text-align: left;
    }

    .text-md-end {
        text-align: right;
    }

    .text-md-center {
        text-align: center;
    }
}


@media (min-width: $lg) {
    .float-lg-start {
        float: left;
    }

    .float-lg-end {
        float: right;
    }

    .float-lg-none {
        float: none;
    }

    .d-lg-inline {
        display: inline;
    }

    .d-lg-inline-block {
        display: inline-block;
    }

    .d-lg-block {
        display: block;
    }

    .d-lg-grid {
        display: grid;
    }

    .d-lg-table {
        display: table;
    }

    .d-lg-table-row {
        display: table-row;
    }

    .d-lg-table-cell {
        display: table-cell;
    }

    .d-lg-flex {
        display: flex;
    }

    .d-lg-inline-flex {
        display: inline-flex;
    }

    .d-lg-none {
        display: none;
    }

    .flex-lg-fill {
        flex: 1 1 auto;
    }

    .flex-lg-row {
        flex-direction: row;
    }

    .flex-lg-column {
        flex-direction: column;
    }

    .flex-lg-row-reverse {
        flex-direction: row-reverse;
    }

    .flex-lg-column-reverse {
        flex-direction: column-reverse;
    }

    .flex-lg-grow-0 {
        flex-grow: 0;
    }

    .flex-lg-grow-1 {
        flex-grow: 1;
    }

    .flex-lg-shrink-0 {
        flex-shrink: 0;
    }

    .flex-lg-shrink-1 {
        flex-shrink: 1;
    }

    .flex-lg-wrap {
        flex-wrap: wrap;
    }

    .flex-lg-nowrap {
        flex-wrap: nowrap;
    }

    .flex-lg-wrap-reverse {
        flex-wrap: wrap-reverse;
    }

    .gap-lg-0 {
        gap: 0;
    }

    .gap-lg-1 {
        gap: 0.4rem;
    }

    .gap-lg-2 {
        gap: 0.8rem;
    }

    .gap-lg-3 {
        gap: 1.6rem;
    }

    .gap-lg-4 {
        gap: 2.4rem;
    }

    .gap-lg-5 {
        gap: 4.8rem;
    }

    .justify-content-lg-start {
        justify-content: flex-start;
    }

    .justify-content-lg-end {
        justify-content: flex-end;
    }

    .justify-content-lg-center {
        justify-content: center;
    }

    .justify-content-lg-between {
        justify-content: space-between;
    }

    .justify-content-lg-around {
        justify-content: space-around;
    }

    .justify-content-lg-evenly {
        justify-content: space-evenly;
    }

    .align-items-lg-start {
        align-items: flex-start;
    }

    .align-items-lg-end {
        align-items: flex-end;
    }

    .align-items-lg-center {
        align-items: center;
    }

    .align-items-lg-baseline {
        align-items: baseline;
    }

    .align-items-lg-stretch {
        align-items: stretch;
    }

    .align-content-lg-start {
        align-content: flex-start;
    }

    .align-content-lg-end {
        align-content: flex-end;
    }

    .align-content-lg-center {
        align-content: center;
    }

    .align-content-lg-between {
        align-content: space-between;
    }

    .align-content-lg-around {
        align-content: space-around;
    }

    .align-content-lg-stretch {
        align-content: stretch;
    }

    .align-self-lg-auto {
        align-self: auto;
    }

    .align-self-lg-start {
        align-self: flex-start;
    }

    .align-self-lg-end {
        align-self: flex-end;
    }

    .align-self-lg-center {
        align-self: center;
    }

    .align-self-lg-baseline {
        align-self: baseline;
    }

    .align-self-lg-stretch {
        align-self: stretch;
    }

    .order-lg-first {
        order: -1;
    }

    .order-lg-0 {
        order: 0;
    }

    .order-lg-1 {
        order: 1;
    }

    .order-lg-2 {
        order: 2;
    }

    .order-lg-3 {
        order: 3;
    }

    .order-lg-4 {
        order: 4;
    }

    .order-lg-5 {
        order: 5;
    }

    .order-lg-last {
        order: 6;
    }

    .m-lg-0 {
        margin: 0;
    }

    .m-lg-1 {
        margin: 0.4rem;
    }

    .m-lg-2 {
        margin: 0.8rem;
    }

    .m-lg-3 {
        margin: 1.6rem;
    }

    .m-lg-4 {
        margin: 2.4rem;
    }

    .m-lg-5 {
        margin: 4.8rem;
    }

    .m-lg-auto {
        margin: auto;
    }

    .mx-lg-0 {
        margin-right: 0;
        margin-left: 0;
    }

    .mx-lg-1 {
        margin-right: 0.4rem;
        margin-left: 0.4rem;
    }

    .mx-lg-2 {
        margin-right: 0.8rem;
        margin-left: 0.8rem;
    }

    .mx-lg-3 {
        margin-right: 1.6rem;
        margin-left: 1.6rem;
    }

    .mx-lg-4 {
        margin-right: 2.4rem;
        margin-left: 2.4rem;
    }

    .mx-lg-5 {
        margin-right: 4.8rem;
        margin-left: 4.8rem;
    }

    .mx-lg-auto {
        margin-right: auto;
        margin-left: auto;
    }

    .my-lg-0 {
        margin-top: 0;
        margin-bottom: 0;
    }

    .my-lg-1 {
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
    }

    .my-lg-2 {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .my-lg-3 {
        margin-top: 1.6rem;
        margin-bottom: 1.6rem;
    }

    .my-lg-4 {
        margin-top: 2.4rem;
        margin-bottom: 2.4rem;
    }

    .my-lg-5 {
        margin-top: 4.8rem;
        margin-bottom: 4.8rem;
    }

    .my-lg-auto {
        margin-top: auto;
        margin-bottom: auto;
    }

    .mt-lg-0 {
        margin-top: 0;
    }

    .mt-lg-1 {
        margin-top: 0.4rem;
    }

    .mt-lg-2 {
        margin-top: 0.8rem;
    }

    .mt-lg-3 {
        margin-top: 1.6rem;
    }

    .mt-lg-4 {
        margin-top: 2.4rem;
    }

    .mt-lg-5 {
        margin-top: 4.8rem;
    }

    .mt-lg-auto {
        margin-top: auto;
    }

    .me-lg-0 {
        margin-right: 0;
    }

    .me-lg-1 {
        margin-right: 0.4rem;
    }

    .me-lg-2 {
        margin-right: 0.8rem;
    }

    .me-lg-3 {
        margin-right: 1.6rem;
    }

    .me-lg-4 {
        margin-right: 2.4rem;
    }

    .me-lg-5 {
        margin-right: 4.8rem;
    }

    .me-lg-auto {
        margin-right: auto;
    }

    .mb-lg-0 {
        margin-bottom: 0;
    }

    .mb-lg-1 {
        margin-bottom: 0.4rem;
    }

    .mb-lg-2 {
        margin-bottom: 0.8rem;
    }

    .mb-lg-3 {
        margin-bottom: 1.6rem;
    }

    .mb-lg-4 {
        margin-bottom: 2.4rem;
    }

    .mb-lg-5 {
        margin-bottom: 4.8rem;
    }

    .mb-lg-auto {
        margin-bottom: auto;
    }

    .ms-lg-0 {
        margin-left: 0;
    }

    .ms-lg-1 {
        margin-left: 0.4rem;
    }

    .ms-lg-2 {
        margin-left: 0.8rem;
    }

    .ms-lg-3 {
        margin-left: 1.6rem;
    }

    .ms-lg-4 {
        margin-left: 2.4rem;
    }

    .ms-lg-5 {
        margin-left: 4.8rem;
    }

    .ms-lg-auto {
        margin-left: auto;
    }

    .p-lg-0 {
        padding: 0;
    }

    .p-lg-1 {
        padding: 0.4rem;
    }

    .p-lg-2 {
        padding: 0.8rem;
    }

    .p-lg-3 {
        padding: 1.6rem;
    }

    .p-lg-4 {
        padding: 2.4rem;
    }

    .p-lg-5 {
        padding: 4.8rem;
    }

    .px-lg-0 {
        padding-right: 0;
        padding-left: 0;
    }

    .px-lg-1 {
        padding-right: 0.4rem;
        padding-left: 0.4rem;
    }

    .px-lg-2 {
        padding-right: 0.8rem;
        padding-left: 0.8rem;
    }

    .px-lg-3 {
        padding-right: 1.6rem;
        padding-left: 1.6rem;
    }

    .px-lg-4 {
        padding-right: 2.4rem;
        padding-left: 2.4rem;
    }

    .px-lg-5 {
        padding-right: 4.8rem;
        padding-left: 4.8rem;
    }

    .py-lg-0 {
        padding-top: 0;
        padding-bottom: 0;
    }

    .py-lg-1 {
        padding-top: 0.4rem;
        padding-bottom: 0.4rem;
    }

    .py-lg-2 {
        padding-top: 0.8rem;
        padding-bottom: 0.8rem;
    }

    .py-lg-3 {
        padding-top: 1.6rem;
        padding-bottom: 1.6rem;
    }

    .py-lg-4 {
        padding-top: 2.4rem;
        padding-bottom: 2.4rem;
    }

    .py-lg-5 {
        padding-top: 4.8rem;
        padding-bottom: 4.8rem;
    }

    .pt-lg-0 {
        padding-top: 0;
    }

    .pt-lg-1 {
        padding-top: 0.4rem;
    }

    .pt-lg-2 {
        padding-top: 0.8rem;
    }

    .pt-lg-3 {
        padding-top: 1.6rem;
    }

    .pt-lg-4 {
        padding-top: 2.4rem;
    }

    .pt-lg-5 {
        padding-top: 4.8rem;
    }

    .pe-lg-0 {
        padding-right: 0;
    }

    .pe-lg-1 {
        padding-right: 0.4rem;
    }

    .pe-lg-2 {
        padding-right: 0.8rem;
    }

    .pe-lg-3 {
        padding-right: 1.6rem;
    }

    .pe-lg-4 {
        padding-right: 2.4rem;
    }

    .pe-lg-5 {
        padding-right: 4.8rem;
    }

    .pb-lg-0 {
        padding-bottom: 0;
    }

    .pb-lg-1 {
        padding-bottom: 0.4rem;
    }

    .pb-lg-2 {
        padding-bottom: 0.8rem;
    }

    .pb-lg-3 {
        padding-bottom: 1.6rem;
    }

    .pb-lg-4 {
        padding-bottom: 2.4rem;
    }

    .pb-lg-5 {
        padding-bottom: 4.8rem;
    }

    .ps-lg-0 {
        padding-left: 0;
    }

    .ps-lg-1 {
        padding-left: 0.4rem;
    }

    .ps-lg-2 {
        padding-left: 0.8rem;
    }

    .ps-lg-3 {
        padding-left: 1.6rem;
    }

    .ps-lg-4 {
        padding-left: 2.4rem;
    }

    .ps-lg-5 {
        padding-left: 4.8rem;
    }

    .text-lg-start {
        text-align: left;
    }

    .text-lg-end {
        text-align: right;
    }

    .text-lg-center {
        text-align: center;
    }
}


@media print {
    .d-print-inline {
        display: inline;
    }

    .d-print-inline-block {
        display: inline-block;
    }

    .d-print-block {
        display: block;
    }

    .d-print-grid {
        display: grid;
    }

    .d-print-table {
        display: table;
    }

    .d-print-table-row {
        display: table-row;
    }

    .d-print-table-cell {
        display: table-cell;
    }

    .d-print-flex {
        display: flex;
    }

    .d-print-inline-flex {
        display: inline-flex;
    }

    .d-print-none {
        display: none;
    }
}

