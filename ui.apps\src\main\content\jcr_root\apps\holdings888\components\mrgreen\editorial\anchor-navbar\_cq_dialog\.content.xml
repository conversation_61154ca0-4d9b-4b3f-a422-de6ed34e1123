<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Anchor Menu - Configuration"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <tabTitle
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <styling
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="Styling Variation"
                                required="{Boolean}true"
                                name="./styling">
                                <items jcr:primaryType="nt:unstructured">
                                    <default
                                        sling:hideResource="{Boolean}true"
                                        jcr:primaryType="nt:unstructured"
                                        text="Vip Anchors"
                                        value="vip" />
                                    <variation1
                                        jcr:primaryType="nt:unstructured"
                                        text="Game info Anchors"
                                        value="game-info" />
                                </items>
                            </styling>
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <firstItemHighlighted
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                text="First item highlighted by default"
                                                jcr:description="Once selected the first item in the navbar will be highlighted by default."
                                                name="./firstItemHighlighted"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true" />
                                            <itemNotUppercase
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                text="Make every Navigation Item Title not uppercase"
                                                jcr:description="Selected will render the Navigation Item Title not uppercase."
                                                name="./navigationItemTitleNotUppercase"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true" />
                                            <logoImage
                                                sling:hideResource="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Logo Image Path"
                                                rootPath="/content/dam/mrgreen"
                                                name="./logoImage" />
                                            <logoAlt
                                                sling:hideResource="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Logo Image Alt Text"
                                                name="./logoAlt" />
                                            <logoImageLink
                                                sling:hideResource="{Boolean}true"
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/link"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    fieldsetTitle="Logo Image Link"
                                                    linkUrlName="logoImageLink"
                                                    urlIsRequired="{Boolean}false"
                                                    hideLabel="{Boolean}true"
                                                    linkTitleName="linkTitleAttr"
                                                    hideScript="{Boolean}true" />
                                            </logoImageLink>
                                            <navMenu
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                fieldLabel="Navigation Menu Items">
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./navMenu">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <anchorId
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Anchor ID"
                                                                    name="./anchorId" />
                                                                <anchorTitle
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Navigation Item Title"
                                                                    name="./anchorTitle" />
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </navMenu>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabTitle>

                </items>
            </tabs>
        </items>
    </content>
</jcr:root>