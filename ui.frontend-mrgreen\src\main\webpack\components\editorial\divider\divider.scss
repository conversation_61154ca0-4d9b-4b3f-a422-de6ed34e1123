.divider {
    .divider-editMode {
        min-height: 5rem;
        &.green-line,
        &.grey-line,
        &.grey-line-taboola,
        &.grey-line-med,
        &.black-line,
        &.black-line-med {
            border-radius: 0 !important;
        }
    }
    margin: 0.25rem 0;
    .green-line {
        border-top: 0.5rem solid $divider-color;
    }

    .grey-line {
        border-top: 0.2rem solid $light-gray;
    }
    .thic-grey-line {
        border-top: 5px solid rgba(5, 55, 44, 0.4);
    }
    .mr-green-line{
        border-top: 1px solid $article-slider-border;
    }
    .mr-green-double-line{
        border-top: 1px solid $article-slider-border;
        border-bottom: 1px solid $article-slider-border;
        padding: 0.5px 0;
    }

    .default {
        margin: 2.5rem 0;
    }

    .medium {
        margin: 1rem 0;
    }

    .small {
        margin: 0.5rem 0;
    }
}

.one-col-desktop-50 .divider {
    @media (max-width: $sm-grid) {
        display: contents;
    }
}
