function BackToTop($cmp) {
    let isEditor = window.Granite && window.Granite.author ? true : false;
    if (!isEditor) {
      $cmp.classList.remove("back-to-top-author-display");
    }
    let selectors = {
      circleIcon: '.circle',
      label: '.label',
    };
    let circleIcon = $cmp.querySelector(selectors.circleIcon),
        label = $cmp.querySelector(selectors.label),
        headerHeight = 50,
        opacity = 0;
  
    if (circleIcon && !isEditor) {
      circleIcon.style.opacity = opacity;
    }
    if (label) {
      label.style.opacity = opacity;
    }
    $cmp.style.visibility = opacity > 0 ? "visible" : "hidden";
  
    document.addEventListener("scroll", function () {
      opacity = window.scrollY < headerHeight ? "0" : "1";
      if (circleIcon) {
        circleIcon.style.opacity = opacity;
      }
      if (label) {
        label.style.opacity = opacity;
      }
      $cmp.style.visibility = opacity > 0 ? "visible" : "hidden";
    });
  
    $cmp.addEventListener("click", function () {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });
  }
  
  document.querySelectorAll(".back-to-top-component").forEach(function ($cmp) {
    BackToTop($cmp);
  });