package holdings888.core.models;

import com.day.cq.wcm.api.Page;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.*;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Model(adaptables = { SlingHttpServletRequest.class }, resourceType = {
        "holdings888/components/editorial/live-events" }, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)

public class LiveEventsModel {
    public static final String EVENT_PAGE_RESOURCE_TYPE = "holdings888/components/structure/poker-event-page";

    @Inject
    private ResourceResolver resourceResolver;

    @Inject
    SlingHttpServletRequest slingHttpServletRequest;

    @ScriptVariable
    private Page currentPage;

    @ValueMapValue
    @Getter
    private String eventsRootPagePath;

    @ValueMapValue(name = "sortEventBy")
    @Getter
    private String sortEventBy;

    @ValueMapValue(name = "sortEvent")
    @Getter
    private String sortEvent;



    private List<EventPageModel> allEvents = new ArrayList<>();

    @PostConstruct
    protected void init() {
        Page eventsRootPage = getRootPage();
        if(eventsRootPage != null) {
            allEvents = getAllEvents(eventsRootPage);
        }
    }

    private Page getRootPage() {
        Page page = currentPage;
        if (StringUtils.isNotBlank(eventsRootPagePath)) {
            Resource pageResource = resourceResolver.getResource(eventsRootPagePath);
            if (Objects.nonNull(pageResource)) {
                Page eventsRootPage = pageResource.adaptTo(Page.class);
                if (Objects.nonNull(page)) {
                    page = eventsRootPage;
                }
            }
        }
        return page;
    }

    private List<EventPageModel> getAllEvents(Page eventsRootPage) {
        Iterator<Page> iterator = eventsRootPage.listChildren();

        while (iterator.hasNext()) {
            Page eventPage = iterator.next();
            Resource eventPageResource = eventPage.getContentResource();
            if (Objects.nonNull(eventPageResource)
                    && eventPageResource.getResourceType().equals(EVENT_PAGE_RESOURCE_TYPE)) {
                EventPageModel eventPageModel = eventPageResource.adaptTo(EventPageModel.class);
                allEvents.add(eventPageModel);
            }
        }

        if (allEvents != null) {
            List<EventPageModel> cloneAllEvents =allEvents;
            return cloneAllEvents;
        } else {
            return Collections.emptyList();
        }

    }

    //Method for setting options on the dialog to sort the event on either ASC or DESC
    public Comparator<EventPageModel> eventSortOptions (){
        switch (sortEvent) {
            case "ASC":
                return eventSortWith(sortEventBy);
            case "DESC":
                return eventSortWith(sortEventBy).reversed();
            default:
                throw new IllegalArgumentException("Invalid sorting option: " + sortEvent);
        }
    } 

    //Method for setting options on the dialog to sort the event Based on condition
    public Comparator<EventPageModel> eventSortWith (String sortWith){
        switch (sortWith) {
            case "eventName":
                return Comparator.comparing(EventPageModel::getTitle);
            case "eventDate":
                return Comparator.comparing(EventPageModel::getDate);
            case "eventDesc":
                return Comparator.comparing(EventPageModel::getDescription);
            default:
                throw new IllegalArgumentException("Invalid sorting option: " + sortWith);
        }
    }


    public List<EventPageModel> getLiveEvents() {
        return new ArrayList<>(allEvents.stream()
        .filter(event -> event.isActive() && !event.isHidden())
        .sorted(eventSortOptions())
        .collect(Collectors.toList()));
    }

    public List<EventPageModel> getPastEvents() {
        return new ArrayList<>(allEvents.stream()
        .filter(event -> !event.isActive() && !event.isHidden())
        .sorted(eventSortOptions())
        .collect(Collectors.toList()));
    }

}
