.content-banner {
    display: flex;
    align-items: center;
    width: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    min-height: 35rem;
    position: relative;
    line-height: 1.3;
    z-index: 1;

    @media only screen and (max-width: 1480px) {
        min-height: 250px;
    }

    @media only screen and (max-width: $md) {
        min-height: 205px;
    }

    @media only screen and (max-width: 820px) {
        min-height: 150px;
    }

    @media only screen and (max-width: 513px) {
        min-height: 100px;
    }

    @media only screen and (max-width: 513px) and (orientation: portrait) {
        height: 100vw;
    }

    &.mobile-split-content {
        position: relative;

        @media only screen and (max-width: 513px) and (orientation: portrait) {
            min-height: 455px;
        }

        .image-mobile {
            background-position: center 57%;

            @media only screen and (max-width: 390px) {
                background-position: center 54%;
            }

            @media only screen and (max-width: 330px) {
                background-position: center 50%;
            }
        }

        .content-banner-wrapper {
            .text-container {
                .content-info {
                    @media only screen and (max-width: 513px) and (orientation: portrait) {
                        position: absolute;
                        top: 0;
                    }
                    

                    h2 {
                        @media only screen and (max-width: 513px) and (orientation: portrait) {
                            font-size: 30px;
                            padding: 10px 0 0;
                        }
                        
                    }
                    
                    h3 {
                        @media only screen and (max-width: 513px) and (orientation: portrait) {
                            line-height: 1;
                        }
                        
                    }
                }

                .cta-component {
                    position: absolute;
                    bottom: 16%;
                    left: 16%;

                    @media only screen and (max-width: 390px) {
                        bottom: 19%;
                        left: 12%;
                    }

                    @media only screen and (max-width: 375px) {
                        bottom: 20%;
                        left: 11%;
                    }

                    @media only screen and (max-width: 330px) {
                        bottom: 25%;
                        left: 5%;
                    }
                }

                .two-cta-apps {
                    @media only screen and (max-width: 513px) and (orientation: portrait) {
                        position: absolute;
                        bottom: 2%;
                        left: 18%;
                    }
                    

                    @media only screen and (max-width: 390px) {
                        bottom: 5%;
                        left: 14%;
                    }

                    @media only screen and (max-width: 375px) {
                        bottom: 5%;
                        left: 13%;
                    }

                    @media only screen and (max-width: 330px) {
                        bottom: 10%;
                        left: 5%;
                    }

                    .two-buttons-component {
                        .two-buttons__left,
                        .two-buttons__right {
                            a {
                                img {
                                    @media only screen and (max-width: 513px) and (orientation: portrait) {
                                        max-width: 144px;
                                        margin-top: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .content-info{
        h1 {
            font-size: 2.1vw;
            color: #ffffff;
            margin: 0;
            padding: 0;
            border-bottom: none;
            font-family: $font-family-4;

            @media only screen and (max-width: 600px) and (orientation: portrait) {
                font-size: 30px;
                text-shadow: 2px 2px 2px #000;
            }
        }
        h2 {
            font-size: 1.8vw;
            color: #ffffff;
            line-height: 1;
            font-family: $font-family-4;
            margin-bottom: 0.6vw;
            text-transform: none;
            padding: 20px 0 10px;

            @media only screen and (max-width: 600px) and (orientation: portrait) {
                font-size: 22px;
            }

            @media only screen and (max-width: 513px) {
                padding: 0 0 10px;
            }
        }
        h2.banner-heading {
            font-size: 2.1vw;
            color: #ffffff;
            margin: 0;
            padding: 0;
            border-bottom: none;
            font-family: $font-family-4;
            margin-bottom: 30px;
        }
        h2.heading {
            font-size: 2.2vw;
            color: #ffffff;
            margin: 0;
            padding: 0;
            border-bottom: none;
            font-family: $font-family-4;
            text-shadow: 4px 4px 8px rgba(65, 177, 105, 0.6);
        }
        h3 {
            color: #41B169;
            font-size: 1.65vw;
            margin-top: 0.8vw;
            @media only screen and (max-width: 600px) and (orientation: portrait) {
                font-size: 22px;
                margin-top: 2.6vw;
            }
        }
        @media only screen and (max-width: 600px) and (orientation: portrait) {
            h2.small-font {
                font-size: 27px !important;
            }
        }
        .white {
            color: #fff !important;
        }
        .green {
            color: #41b169;
            padding: 0 0 0 7px;
        }
    }

    .cta-component {
        @media only screen and (max-width: 512px) {
            justify-content: center;
        }

        &.cta-display-mobile {
            @media only screen and (min-width: 513px) {
                display: none;
            }
        }

    }

    .cta-template.cta-wide-size a {
        font-family: $font-family-2;
        line-height: 1;
        font-size: 1.3vw;
        padding: 0.87vw 3.05vw;
        min-width: 137px;
        max-width: 345px;

        @media only screen and (max-width: 513px) and (orientation: landscape) {
            min-width: 90px;
            max-width: 100px;
        }

        @media only screen and (max-width: 600px) and (orientation: portrait) {
                font-size: 16px;
                padding: 8px 16px;
                display: block;
                border-radius: 26px;
                width: fit-content;
                min-width: auto;
        }

        @media only screen and (max-width: 512px) and (orientation: portrait) {
            font-size: 21px;
            min-width: 300px;
            padding: 12px 0px;
        }

        @media only screen and (max-width: 330px) {
            min-width: 288px;
        }
    }

    .two-buttons-component img{
        width: 120px;
        min-width: 120px;
        display: inline-block;
        height: auto;
        text-decoration: none;
        margin-top: 10px;
    }

    .image-pc {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 100% 0;
        display: none;
        @media (min-width: $md) {
            display: flex;
        }
    }
    .image-tablet {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 100% 0;
        display: none;
        @media (min-width: 513px) {
            display: flex;
        }
        @media (min-width: $md) {
            display: none;
        }
    }
    .image-mobile {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: contain;
        background-repeat: no-repeat;
        display: flex;
        @media (min-width: 513px) {
            display: none;
        }
    }
    .content-banner-wrapper {
        z-index: 1;
        display: flex;
        flex-direction: column;
        max-width: 48%;
        margin-left: 10em;
        width: 100%;
        opacity: 1;

        @media only screen and (max-width: $md) {
            max-width: 60%;
            margin-left: 7em;
        }

        @media only screen and (max-width: 820px) {
            margin-left: 4em;
        }

        @media only screen and (max-width: 513px) {
            max-width: 88%;
            margin-left: 2em;
        }

        .two-cta-apps {
            .two-buttons-component {
                gap: 0;

                .two-buttons__left,
                .two-buttons__right {
                    a {
                        width: auto;
                        min-width: auto;

                        img {
                            max-width: 11vw;
                            min-width: auto;
                        }
                    }
                }
            }
        }

    }

}

.content-banner:has(.mobile-split-content) {
    @media only screen and (max-width: 513px) and (orientation: portrait) {
        min-height: 455px;
    }
}
