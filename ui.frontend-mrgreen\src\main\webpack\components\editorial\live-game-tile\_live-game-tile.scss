.live-game-tile-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 21rem;
    width: 100%;
    cursor: pointer;
    height: auto;

    @media (max-width: $sm) {
        margin: 0 0.6rem 3rem;
    }

    .live-game-tile-item {
        max-width: 21rem;
        position: relative;
        width: 100%;
        height: auto;
        text-align: center;
        background-image: url("../resources/images/icons/bg-game-black.png");
        background-size: contain;
        background-repeat: no-repeat;

        a {
            outline: 0;
            box-shadow: none;
        }

        img {
            width: 100%;
            height: auto;

            @media (max-width: $md) {
                width: 90%;
            }

            @media (max-width: $sm-air) {
                width: 100%;
            }
        }
    }

    .live-game-tile-teaser {
        max-width: 21rem;
        position: relative;
        width: 100%;
        height: 4.5rem;
        background: url("../resources/images/icons/border-horizontal.png");
        background-position: center 2rem;
        background-repeat: no-repeat;
        padding-bottom: 1.2rem;
        padding-top: 0.6rem;
        margin: -0.5rem auto 0;
        text-align: center;
        cursor: auto;
        span {
            font-family: $font-family-2;
            font-size: 14px;
            line-height: 1.2;
            font-weight: $font-weight-semi-bold;
        }
    }
}

.live-game-tile{
    display: flex;
    justify-content: center;
}