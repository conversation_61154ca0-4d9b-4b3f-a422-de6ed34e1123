.promotions-teaser-component {
    scroll-snap-align: start;
    border: 0.1rem solid $black;
    display: flex;
    width: 100%;
    flex: 0 0 40%;
    background: $white-1;
    border: none;
    border-radius: 1.3rem;
    @media (max-width: $md-max) {
        flex: 0 0 49%;
        width: 50%;
    }
    @media (max-width: $sm-grid) {
        width: 100%;
    }
    .promotions-teaser-wrapper {
        height: fit-content;
        color: $dark-2;
        font-weight: $font-weight-medium;
        font-size: 1rem;
        margin: 0;
        width: 100%;
        box-sizing: border-box;
        flex-direction: column;
        .promotions-teaser {
            &__top {
                height: 14.3%;
                line-height: 1.5;
                font-size: 1.5rem;
                box-sizing: inherit;
                margin: 0;
                padding: 1em;
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 0.8rem;
                &__link {
                    box-sizing: inherit;
                    line-height: inherit;
                    outline: 0;
                    box-shadow: none;
                    background-color: transparent;
                    cursor: pointer;
                    text-decoration: underline;
                    color: $dark-2;
                    font-size: 1.2rem;
                    font-weight: $font-weight-bold;
                    font-family: $font-family-4;
                }
                &__title {
                    font-family: $font-family-2;
                    font-style: normal;
                    line-height: 1.2em;
                    display: flex;
                    align-items: center;
                    max-width: 80%;
                    min-height: 3rem;
                    margin: 0;
                    padding: 0;
                    font-size: 1.2rem;
                    text-transform: uppercase;
                    border: none;
                    font-weight: $font-weight-medium;
                    color: $dark-green;
                    line-height: 2rem;
                    @media (min-width: $md) {
                        font-size: 1.3rem;
                        font-weight: $font-weight-semi-bold;
                    }
                }
            }
            &__img {
                line-height: 1.5;
                color: $white;
                box-sizing: inherit;
                border: 0;
                max-width: 100%;
                height: auto;
                display: inline-block;
                vertical-align: middle;
                width: 100%;
                img {
                    width: 100%;
                    height: auto;
                }
            }
            &__bot {
                line-height: 1.5;
                color: $white;
                font-weight: $font-weight-semi-bold;
                font-size: 1.5rem;
                box-sizing: inherit;
                margin: 0;
                width: 100%;
                padding: 1em;
                display: flex;
                justify-content: space-between;
                align-items: start;
                &__text {
                    width: 65%;

                    p {
                        color: $dark-2;
                        font-size: 1.4rem;
                        font-weight: $font-weight-bold;
                        line-height: 2rem;
                        box-sizing: inherit;
                        
                        display: block;
                        padding-right: 0.25em;
                        font-family: $font-family-2;
                        @media (max-width: 1024px) {
                            font-size: 1.2rem;
                            line-height: 1.44rem;
                            font-weight: $font-weight-thinner;
                            word-break: break-word;
                        }

                    }
                    
                }
                &__cta-section {
                    line-height: 1.5;
                    color: $white;
                    font-weight: $font-weight-semi-bold;
                    font-size: 1.2rem;
                    box-sizing: inherit;
                    @media (min-width: $sm) {
                        font-size: 1.5rem;
                    }
                    .cta-component {
                        display: flex;
                        flex-direction: column;
                        gap: 0.5em;
                        padding-top: 0;
                        .cta-template {
                            a {
                                width: 160px;
                                height: 35px;
                                align-items: center;
                                letter-spacing: 0.05em;
                                min-width: 10pc;
                                font-size: 10px;
                                text-decoration: none;
                                font-family: $font-family-2;
                                @media (max-width: $md-max) {
                                    font-size: $font-size;
                                    line-height: 10px;
                                    letter-spacing: 0;
                                }
                            }
                        }

                        .cta-primary {
                            a {
                                width: 160px;
                                height: 33.2px;
                                border-color: $dark-green-2;
                                background-color: $dark-green-2;
                                padding: 0.8rem 1rem;

                                @media (max-width: $md) {
                                    padding: 0.8rem 0;
                                }

                                &:hover {
                                    background-color: $white;
                                    
                                    span {
                                        color: $dark-green;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .disclaimer{ 
            padding: 0 $font-size-16 $font-size-18;
                p {
                line-height: $line-height;
                box-sizing: inherit;
                font-size: 1rem;
                background-color: inherit;
                border: none;
                color: $dark-2;
                font-weight: $font-weight-medium;
                font-size: $font-size-18;
                a {
                    box-sizing: inherit;
                    line-height: inherit;
                    outline: 0;
                    box-shadow: none;
                    background-color: transparent;
                    color: $green-3;
                }
            }
        }
    }
}
