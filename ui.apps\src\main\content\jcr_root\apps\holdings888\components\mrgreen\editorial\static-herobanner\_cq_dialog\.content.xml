<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
    xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Static Hero Banner Properties"
    extraClientlibs="[888casino.components.author.editor,holdings888.dialog.altText]"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container"
        granite:class="cmp-container__editor">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                granite:class="cmp-image__editor"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <tabMedia
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Media"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <mediaSelect
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cq-dialog-dropdown-showhide"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                fieldLabel="Select type of media"
                                                fieldDescription="Using this select a different media type will be displayed in the component"
                                                name="./mediaSelect">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <video
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Video"
                                                        value="video" />
                                                    <image
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Image"
                                                        value="image" />
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".mediaSelect-showhide-target" />
                                            </mediaSelect>
                                            <setVideoDesktop
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="mediaSelect-showhide-target"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <videoDesktop
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldLabel="Desktop"
                                                        fieldDescription="Select Desktop Video path"
                                                        name="./videoDesktop"
                                                        rootPath="/content/dam/mrgreen" />
                                                    <videoDesktopPreviewImage
                                                        jcr:primaryType="nt:unstructured"
                                                        path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                        <parameters
                                                            jcr:primaryType="nt:unstructured"
                                                            imageLabel="Desktop"
                                                            imageDescription="Image to be shown until the user presses the play button (in case of autoplay not active)"
                                                            imagePrefixName="videoDesktopPreviewImage"
                                                            altName="desktopAltVideo"
                                                            hideIsFetchPriorityHigh="{Boolean}true"
                                                            imageIsRequired="{Boolean}false" />
                                                    </videoDesktopPreviewImage>
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="video" />
                                            </setVideoDesktop>
                                            <setVideoMobile
                                                granite:class="mediaSelect-showhide-target"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <videoMobile
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldLabel="Mobile"
                                                        fieldDescription="Select Mobile Video path"
                                                        name="./videoMobile"
                                                        rootPath="/content/dam/mrgreen" />
                                                    <videoMobilePreviewImage
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        class="cq-droptarget"
                                                        fileNameParameter="./videoMobilePreviewImageName"
                                                        fileReferenceParameter="./videoMobilePreviewImageReference"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./videoMobilePreviewImage"
                                                        fieldDescription="Image to be shown until the user presses the play button (in case of autoplay not active)"
                                                        fieldLabel="Mobile Preview Image"
                                                        allowUpload="{Boolean}false" />
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="video" />
                                            </setVideoMobile>
                                            <setImageLinkDesktop
                                                granite:class="mediaSelect-showhide-target"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <sameDeskMob
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        text="Same Image Desktop/Mobile"
                                                        name="./sameDeskMob"
                                                        value="{Boolean}true"
                                                        uncheckedValue="{Boolean}false"
                                                        required="{Boolean}false" />
                                                    <increaseImageHeight
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        text="Increase Image Desktop (height: 370)"
                                                        name="./increaseImageHeight"
                                                        value="{Boolean}true"
                                                        uncheckedValue="{Boolean}false"
                                                        required="{Boolean}false" />
                                                    <increaseImageHeight200
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        text="Increase Image Desktop (height: 200)"
                                                        name="./increaseImageHeight200"
                                                        value="{Boolean}true"
                                                        uncheckedValue="{Boolean}false"
                                                        required="{Boolean}false" />
                                                    <roundCorners
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        text="Set round corners"
                                                        name="./roundCorners"
                                                        value="{Boolean}true"
                                                        uncheckedValue="{Boolean}false"
                                                        required="{Boolean}false" />
                                                    <padding
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        text="Set padding"
                                                        name="./paddingImg"
                                                        value="{Boolean}true"
                                                        uncheckedValue="{Boolean}false"
                                                        required="{Boolean}false" />

                                                    <imageLinkDesktop
                                                        jcr:primaryType="nt:unstructured"
                                                        path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                        <parameters
                                                            jcr:primaryType="nt:unstructured"
                                                            imageLabel="Desktop"
                                                            imageDescription="Drag and drop an image or select it from the dam for desktop view"
                                                            imagePrefixName="imageLinkDesktop"
                                                            altName="desktopAltImage"
                                                            imageIsRequired="{Boolean}false" />
                                                    </imageLinkDesktop>
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="image" />
                                            </setImageLinkDesktop>
                                            <setImageLinkMobile
                                                granite:class="mediaSelect-showhide-target"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <imageLinkMobile
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        class="cq-droptarget"
                                                        fileNameParameter="./imageLinkMobileName"
                                                        fileReferenceParameter="./imageLinkMobileReference"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./imageLinkMobile"
                                                        fieldDescription="Drag and drop an image or select it from the dam for mobile view"
                                                        fieldLabel="Mobile"
                                                        allowUpload="{Boolean}false" />
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="image" />
                                            </setImageLinkMobile>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabMedia>

                    <tabBackToPrevious
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Back To Previous"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <enableBackToPrevious
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                text="Use Back To Previous"
                                                name="./enableBackToPrevious"
                                                checked="{Boolean}true"
                                                uncheckedValue="false"
                                                value="{Boolean}true" />
                                            <ariaLabelText
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Aria Label"
                                                value="Back"
                                                name="./label" />
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabBackToPrevious>

                    <targetConfig
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="holdings888/components/dialog-include/target-config" />
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>