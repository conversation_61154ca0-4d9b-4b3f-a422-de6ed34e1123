.article-slider-component-xf:not(.main-container .article-slider-component-xf) {
  padding: 2rem 1rem 1rem 4rem;
}
.article-slider-component-xf {
   padding: 1rem 0 0 0;
  .internalLinksStyle {
    .boxTitle {
      display: none;
    }

    .article-slider-container-xf {
      position: relative;
      margin: 2rem auto 3.5rem;
      padding-top: 3rem;
    }

    .swiper-button-prev-xf {
      margin-top: 3rem;
    }

    .swiper-button-next-xf {
      margin-top: 3rem;
    }
  }

  .relatedArticlesStyle {
    position: relative;

    &::before {
      content: "";
      background: url("../resources/images/shadow-bg.png");
      width: 100%;
      height: 350px;
      left: 0;
      top: 5px;
      position: absolute;
      background-position: top;
      background-size: contain;
      background-repeat: no-repeat;
    }

    .boxTitle {
      background: linear-gradient(90deg, $black, hsla(0,0%,100%,0) 75%);
      padding: 0.3em calc(1em - 0.2rem);
      border-left: 0.4rem solid $green;
      margin-top: 1.5em;
      margin-bottom: 1em;
      font-size: 1.6rem;
      font-weight: 600;
      color: $green-2;
    }

    .article-slider-container-xf {
      position: relative;
      margin: 2rem auto 0;
      padding: 3rem 0 3.5rem 0;
    }

    &.article-green-gradient {
      .boxTitle {
        background: linear-gradient(90deg, $dark-green, $dark-4 75%);
        padding: 0.3em calc(1em - 0.2rem);
        border-left: 2px solid $raisin-black;
        margin-top: 1.5em;
        margin-bottom: 1em;
        font-size: 1.6rem;
        font-weight: 600;
        color: $white;
      }
    }
  }

  .article-slider-container-xf {
    a {
      text-decoration: none;
      color: $white;

      &:hover {
        color: $white;
      }
    }

    .slide-img-xf, .slide-text-xf {
      width: 20.25rem;
    }

    .slide-img-xf {
      height: auto;
      display: inline-block;

      img {
        width: 100%;
        height: auto;
      }
    }

    .slide-text-xf {
      padding: .8rem 0;
      display: block;
      text-align: center;
      color: $black;
      font-size: 15px;
      font-weight: $font-weight-semi-bold;
    }

  }

  .swiper-xf {
    width: 90%;
    margin: 0 auto;
    padding: 1.25rem 0;
    overflow-x: hidden;

    .wrapper-xf {
      width: 100%;

      .swiper-slide {
        display: flex;
        justify-content: center !important;
        align-items: center;
        height: auto;
        width: auto;

        align-items: baseline;
        justify-content: left;

        @media (min-width: $md) {
          justify-content: center;
        }
      }
    }
  }

  .swiper-button-prev-xf, .swiper-button-next-xf {
    display: block;
    position: absolute;
    width: auto;
    top: 33%;
  }

  .swiper-button-prev-xf {
    content: url("../resources/images/icons/double-arrow-left.png");

    left: 3.5rem;
    height: 1.5rem;

    @media (max-width: $md) {
      left: -0.5rem;
    }

    @media (max-width: $sm-air) {
      left: -1.5rem;
    }
  }

  .swiper-button-next-xf {
    content: url("../resources/images/icons/double-arrow-right.png");
    right: 3.5rem;
    height: 1.5rem;

    @media (max-width: $md) {
      right: -0.5rem;
    }

    @media (max-width: $sm-air) {
      right: -1.5rem;
    }
  }
}