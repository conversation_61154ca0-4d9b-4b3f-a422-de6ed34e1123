<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        jcr:primaryType="nt:unstructured"
        jcr:title="Live Game Tile"
        sling:resourceType="cq/gui/components/authoring/dialog"
        extraClientlibs="[888casino.components.author.editor,holdings888.dialog.altText]">
        <content
                granite:class="cmp-image__editor"
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/container">
                <items jcr:primaryType="nt:unstructured">
                        <tabs
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                                maximized="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                        <tab1
                                                jcr:primaryType="nt:unstructured"
                                                jcr:title="General Settings"
                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                margin="{Boolean}true">
                                                <items jcr:primaryType="nt:unstructured">
                                                        <columns
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                                                margin="{Boolean}true">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                        <column
                                                                                jcr:primaryType="nt:unstructured"
                                                                                granite:class="cmp-image__editor-image-with-alt"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                                <items jcr:primaryType="nt:unstructured">
                                                                                        <file
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                                                                <parameters
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        imagePrefixName="file"
                                                                                                        imageIsRequired="{Boolean}true"
                                                                                                        hideIsFetchPriorityHigh="{Boolean}true"/>
                                                                                        </file>
                                                                                        <link
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                path="/apps/holdings888/components/common888/dialog-include/link"
                                                                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                                                                <parameters
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        linkUrlName="linkImage"
                                                                                                        urlIsRequired="{Boolean}true"
                                                                                                        hideLabel="{Boolean}true"
                                                                                                />
                                                                                        </link>
                                                                                        <clickScript
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                resize="vertical"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                                                                fieldLabel="Play click event"
                                                                                                jcr:description="The JS Script that will be executed on click"
                                                                                                name="./clickScript"/>
                                                                                        <text
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                                fieldLabel="Text"
                                                                                                name="./text" />
                                                                                        <automation
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                jcr:title="Automation Tab"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                                                                margin="{Boolean}true">
                                                                                                <items jcr:primaryType="nt:unstructured">
                                                                                                        <attributeList
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                sling:resourceType="acs-commons/granite/ui/components/include"
                                                                                                                path="holdings888/components/common888/dialog-include/attribute-list" />
                                                                                                </items>
                                                                                        </automation>
                                                                                </items>
                                                                        </column>
                                                                </items>
                                                        </columns>
                                                </items>
                                        </tab1>
                                </items>
                        </tabs>
                </items>
        </content>
</jcr:root>