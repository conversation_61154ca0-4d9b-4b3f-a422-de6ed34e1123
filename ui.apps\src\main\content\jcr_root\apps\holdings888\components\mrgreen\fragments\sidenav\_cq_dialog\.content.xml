<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    xmlns:cq="http://www.day.com/jcr/cq/1.0"
    xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Sidenav Configuration"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <tabMain
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Banner Icon Configuration"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <content
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                                <items
                                    jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items
                                            jcr:primaryType="nt:unstructured">
                                            <mainLogoImage
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    imageLabel="Main Logo"
                                                    imageDescription="Main Logo Upload"
                                                    imagePrefixName="mainLogoImage"
                                                    imageIsRequired="{Boolean}true"
                                                    altName="imageAlt" />
                                            </mainLogoImage>
                                            <logoUrl
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="logo url"
                                                jcr:description="Insert the url of the CTA"
                                                rootPath="/content"
                                                name="./logoUrl" />
                                            <sidenavLogoImage
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    imageLabel="Main Logo"
                                                    imageDescription="Sidenav Logo Upload"
                                                    imagePrefixName="sidenavLogoImage"
                                                    imageIsRequired="{Boolean}true"
                                                    altName="sidenavimageAlt" />
                                            </sidenavLogoImage>
                                        </items>
                                    </column>
                                </items>
                            </content>
                        </items>
                    </tabMain>

                    <tabCta
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Cta"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <content
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                                <items
                                    jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items
                                            jcr:primaryType="nt:unstructured">
                                            <headingCta
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}4"
                                                text="CTA Configuration" />
                                            <type
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                emptyOption="{Boolean}false"
                                                multiple="{Boolean}false"
                                                name="./type">
                                                <items
                                                    jcr:primaryType="nt:unstructured">
                                                    <sidenavCTA
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Sidenav CTA (golden text)"
                                                        value="cta-sidenav"
                                                        selected="{Boolean}true" />
                                                    <primary
                                                        jcr:primaryType="nt:unstructured"
                                                        text="CTA Primary v1"
                                                        value="cta-primary-v1" />
                                                    <outline
                                                        jcr:primaryType="nt:unstructured"
                                                        text="CTA Outline"
                                                        value="cta-outline" />
                                                    <secondary
                                                        jcr:primaryType="nt:unstructured"
                                                        text="CTA Secondary v3"
                                                        value="cta-secondary-v3" />
                                                    <seconderyOutline
                                                        jcr:primaryType="nt:unstructured"
                                                        text="CTA White outline"
                                                        value="cta-secondary-v4" />
                                                </items>
                                            </type>
                                            <label
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="CTA Label"
                                                jcr:description="Insert the label of the CTA"
                                                name="./label" />
                                            <ariaLabel
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="ARIA Label (Accessibility)"
                                                fieldDescription="This attribute is used to provide additional information to help clarify or
                                                                                                further describe the purpose of a link. Can also be useful to people
                                                                                                with disabilities."
                                                jcr:description="Insert the Aria Label of the CTA (Accessibility)"
                                                required="{Boolean}false"
                                                name="./ariaLabel" />
                                            <ctaUrl
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="CTA url"
                                                jcr:description="Insert the url of the CTA"
                                                rootPath="/content"
                                                name="./ctaUrl" />
                                            <queryParameters
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="holdings888/components/dialog-include/query-parameters" />
                                            <newWindow
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                text="Open link in new tab"
                                                value="{Boolean}true"
                                                name="./newWindow"
                                                uncheckedValue="{Boolean}false"
                                                checked="{Boolean}false" />
                                            <secondScript
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                fieldLabel="Cta Link Script"
                                                resize="vertical"
                                                jcr:description="The JS Script that will be executed on click"
                                                name="./secondScript" />
                                        </items>
                                    </column>
                                </items>
                            </content>
                        </items>
                    </tabCta>
                    <tab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Sidenav Configuration"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <sidenavConfiguration
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}4"
                                                text="Sidenav Configuration" />


                                            <sidenavTitle
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Sidenav Title"
                                                name="./sidenavItemTitle" />
                                            <subSidenav
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                granite:class="cmp-image__editor-image-with-alt-multifield"
                                                fieldLabel="subSidenav">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    name="subSidenav" />
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./subSidenavItem">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <titleSectionSubSidenav
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                            level="{Long}4"
                                                            text="Sidenav Item Configuration" />
                                                        <subSidenavItemText
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                            fieldLabel="Sidenav Item Text"
                                                            name="./subSidenavItemText" />
                                                        <subSidenavItemLink
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                            fieldLabel="Sidenav Item Link"
                                                            jcr:description="Insert the url of the Item"
                                                            rootPath="/content"
                                                            name="./subSidenavItemLink" />
                                                        <mainIconImage
                                                            jcr:primaryType="nt:unstructured"
                                                            path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                                            <parameters
                                                                jcr:primaryType="nt:unstructured"
                                                                imageLabel="Icon"
                                                                imageDescription="Upload Icon for the Sidenav Item"
                                                                imagePrefixName="iconImage"
                                                                imageIsRequired="{Boolean}flase"
                                                                altName="imageAlt" />
                                                        </mainIconImage>
                                                    </items>
                                                </field>
                                            </subSidenav>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tab>
                    <paddingTab
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="holdings888/components/dialog-include/padding" />
                    <targetConfig
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="holdings888/components/dialog-include/target-config" />
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>