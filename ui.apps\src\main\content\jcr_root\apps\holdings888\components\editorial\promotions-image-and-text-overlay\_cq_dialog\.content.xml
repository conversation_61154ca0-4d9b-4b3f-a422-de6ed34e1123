<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
    xmlns:cq="http://www.day.com/jcr/cq/1.0"
    xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Promotions Image and Text Overlay"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <properties
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Properties"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        granite:class="cq-RichText-FixedColumn-column"
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <image
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="holdings888/components/dialog-include/image-upload" />
                                            <imageAlt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Image Alt Text"
                                                name="./imageAlt"
                                                required="{Boolean}true" />
                                            <mainText
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Main Title"
                                                fieldLabel="Main Text"
                                                name="./mainText" />
                                            <hoveringText
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Title for hovering."
                                                fieldLabel="Hovering Text"
                                                name="./hoveringText" />
                                            <url
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Card link"
                                                jcr:description="Insert the url of the CTA"
                                                required="{Boolean}true"
                                                rootPath="/content"
                                                name="./url" />


                                            <textEditor
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                granite:class="cq-dialog-dropdown-showhide"
                                                fieldLabel="Add TnC"
                                                name="./textEditor">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".editor-options-showhide-target" />
                                                <items
                                                    jcr:primaryType="nt:unstructured">
                                                    <richText
                                                        jcr:primaryType="nt:unstructured"
                                                        text="No"
                                                        selected="{Boolean}true"
                                                        value="" />
                                                    <freeHtml
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Yes"
                                                        value="tncDetails" />
                                                </items>
                                            </textEditor>

                                            <tncDetails
                                                granite:class="editor-options-showhide-target"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items
                                                    jcr:primaryType="nt:unstructured">
                                                    <text
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="acs-commons/granite/ui/components/include"
                                                        path="holdings888/components/dialog-include/rich-text" />
                                                    <innerLinkLabel
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldLabel="TnC Link Label for Small Screens"
                                                        name="./innerLinkLabel" />
                                                    <innerLinkHref
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldLabel="TnC Link Href for Small Screens"
                                                        name="./innerLinkHref" />
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    showhidetargetvalue="tncDetails" />
                                            </tncDetails>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </properties>
                    <additionalFunctionality
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Additional Functionality"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        granite:class="cq-RichText-FixedColumn-column"
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">

                                            <ctaScript
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                fieldDescription="The content will be added to the onclick attribute"
                                                fieldLabel="CTA Script"
                                                name="./ctaScript" />
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </additionalFunctionality>
                    <targetConfig
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="holdings888/components/dialog-include/target-config" />
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>