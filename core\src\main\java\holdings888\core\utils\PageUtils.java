package holdings888.core.utils;

import com.day.cq.search.PredicateGroup;
import com.day.cq.search.Query;
import com.day.cq.search.QueryBuilder;
import com.day.cq.search.result.SearchResult;
import com.day.cq.wcm.api.Page;
import com.day.cq.wcm.api.PageManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.day.cq.wcm.api.constants.NameConstants.*;
import static com.day.cq.wcm.api.constants.NameConstants.NN_CONTENT;
import static holdings888.core.utils.Constants.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jcr.RepositoryException;
import javax.jcr.Session;

public class PageUtils {

    private static final Logger logger = LoggerFactory.getLogger(PageUtils.class);
    protected static final String PROMOTIONS_LOBBY_TEMPLATE_BASE = "--promotions-lobby";
    protected static final String CONTENT_BANNER_SLIDER_TEMPLATE_BASE = "--content-banner-slider";

    public static String getCurrentPagePath(Resource resource) {
        PageManager pageManager = resource.getResourceResolver().adaptTo(PageManager.class);
        Page currentPage = pageManager.getContainingPage(resource);
        return currentPage.getPath();
    }

    public static Page getCurrentPage(Resource resource, ResourceResolver resourceResolver) {
        PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
        return Objects.requireNonNull(pageManager).getContainingPage(resource);
    }

    /**
     * Get value (String) of property contains in a resource
     *
     * @param resource
     * @param property
     * @return
     */
    public static String getValueOfProperty(Resource resource, String property) {
        ValueMap valueMap = resource.getValueMap();
        if (valueMap.containsKey(property)) {
            return valueMap.get(property).toString();
        }
        return StringUtils.EMPTY;
    }

    public static String setRichTextLinks(String text, ResourceResolver resourceResolver,
                                          SlingHttpServletRequest slingHttpServletRequest) {
        if (text != null && !text.isEmpty()) {

            String textParts[] = text.split("\\s+"); // break text int string array

            try {
                for (int i = 0; i < textParts.length; i++) {
                    String divider = "\""; // set href divider
                    if (textParts[i].contains("href")) { // check if array cell has href structure
                        if (textParts[i].contains("'")) {// check the quotation mark of the url structure
                            divider = "'";
                        }
                        String editText[] = textParts[i].split(divider); // break the url structure into array string

                        if (editText.length > 1) { // if its broken to array
                            String checkUrl = editText[1]; // get the url

                            if (checkUrl.startsWith(SLASH)) { // if its relative
                                String relativePublishLink = LinkUtils
                                        .initializeLink(checkUrl, null, resourceResolver, slingHttpServletRequest)
                                        .getRelativePublishLink();
                                if (relativePublishLink != null) {
                                    editText[1] = relativePublishLink.replaceAll(HTML, SLASH);
                                }
                                textParts[i] = String.join("\"", editText);
                                if (editText.length == 2) {
                                    textParts[i] += "\"";
                                }
                            } else if (checkUrl.startsWith(HTTP) || checkUrl.startsWith(HTTPS)) { // if its absolute
                                for (int j = 0; j < ADDSCUT_LINKS_LIST.length; j++) {
                                    if (getOriginFromStringUrl(ADDSCUT_LINKS_LIST[j])
                                            .equals(getOriginFromStringUrl(checkUrl))) { // if it exists return true
                                        textParts[i - 1] += " class=\"addsCut\"";
                                    } // check if the url exists
                                } // scan liks array
                            } // check if is relative urk
                        } // check editText
                    } // check if it has href
                } // scan richtext
            } catch (Exception e) {
                logger.error("[888] - Rich Text - Url Processor", e);
            }

            text = String.join(" ", textParts);
        }

        return text;
    }

    public static String getOriginFromStringUrl(String url) throws URISyntaxException {
        URI uri = new URI(url); // convert string to uri object
        String[] originArray = uri.getHost().split("[.]"); // break origin to array
        String finalOrigin = originArray[0];
        if (originArray.length > 2) {
            finalOrigin = originArray[1];
        }
        return finalOrigin;
    }// getOriginFromStringUrl

    /**
     * Retrieve Navigation Title or Title
     *
     * @param page
     * @return
     */
    public static String setLabel(Page page) {
        return Objects.nonNull(page.getNavigationTitle()) ? page.getNavigationTitle() :
                (Objects.nonNull(page.getTitle()) ? page.getTitle() :
                        StringUtils.capitalize(page.getName().replace(holdings888.core.utils.Constants.DASH, holdings888.core.utils.Constants.SPACE_TEXT)));
    }

    /**
     * Retrieve correct Parent XF Promotion Page
     *
     * @param path
     * @param resourceResolver
     * @return
     */
    public static String findPromotionPagePathForXF(String path, ResourceResolver resourceResolver) {
        return path.contains(CONTENT_EXPERIENCE_FRAGMENTS_ROOT_PATH) ? findPromotionPagePath(path, resourceResolver) : StringUtils.EMPTY;
    }

    public static String findPromotionPagePath(String path, ResourceResolver resourceResolver) {
        if (null != path && resourceResolver != null) {
            String brandCountryAndLang = extractLocalBrand(path);
            if (StringUtils.isNotEmpty(brandCountryAndLang)) {
                brandCountryAndLang = CONTENT_ROOT_PATH + brandCountryAndLang;

                Resource langRoot = resourceResolver.getResource(brandCountryAndLang);
                if (langRoot != null) {
                    //find first page with template  "*--promotions-lobby"
                    Iterable<Resource> children = langRoot.getChildren();
                    for (Resource child : children) {
                        Page page = child.adaptTo(Page.class);
                        if (isPromoLobbyPage(page)) {
                            return page.getPath();
                        }
                    }
                }
            }
        }
        return StringUtils.EMPTY;
    }

    public static List<String> findPromotionPagePaths(String path, ResourceResolver resourceResolver) {
        List<String> promoPagePaths = new ArrayList<>();
        if (null != path && resourceResolver != null) {
            String brandCountryAndLang = extractLocalBrand(path);
            if (StringUtils.isNotEmpty(brandCountryAndLang)) {
                brandCountryAndLang = CONTENT_ROOT_PATH + brandCountryAndLang;
                Resource langRoot = resourceResolver.getResource(brandCountryAndLang);
                if (langRoot != null) {
                    QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
                    Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());
                    map.put("type", NT_PAGE);
                    map.put("path", langRoot.getPath());
                    map.put("property", NN_CONTENT + SLASH + NN_TEMPLATE);
                    map.put("property.operation", "like");
                    map.put("property.value", "%" + PROMOTIONS_LOBBY_TEMPLATE_BASE);
                    map.put(P_LIMIT, String.valueOf(-1));
                    Query query = queryBuilder.createQuery(PredicateGroup.create(map), resourceResolver.adaptTo(Session.class));
                    SearchResult result = query.getResult();
                    result.getHits().forEach(hit -> {
                        try {
                            promoPagePaths .add(hit.getPath());
                        } catch (RepositoryException e) {
                            logger.warn("Error retrieving promotion page path", e);
                        }
                    });
                }
            }
        }
        return promoPagePaths;
    }

    public static List<String> findPromotionPagePathsForXF(String path, ResourceResolver resourceResolver) {
        return path.contains(CONTENT_EXPERIENCE_FRAGMENTS_ROOT_PATH) ? findPromotionPagePaths(path, resourceResolver) : new ArrayList<>();
    }

    public static boolean isPromoLobbyPage(Page page) {
        return page != null && StringUtils.contains(page.getProperties().get(NN_TEMPLATE, String.class), PROMOTIONS_LOBBY_TEMPLATE_BASE);
    }

    public static boolean isContentBannerSliderPage(Page page) {
        return page != null && StringUtils.contains(page.getProperties().get(NN_TEMPLATE, String.class), CONTENT_BANNER_SLIDER_TEMPLATE_BASE);
    }

    /**
     * Retrieve correct Parent XF Content Banner SLider Page
     */
    public static String findContentBannerSliderPagePath(Page page) {
        if (isContentBannerSliderPage(page)) {
            return page.getPath();
        }
        if (page.getDepth() < 4) {
            return StringUtils.EMPTY;
        } else {
            return findContentBannerSliderPagePath(page.getParent());
        }
    }

    /**
     * Extract Local Brand part from path
     * <p>
     * Examples:
     * <p>
     * /content/888casino/com/en/anyPage/subpage ->  888casino/com/en
     * <p>
     * /content/experience-fragments/888casino/com/en/anyXFFolder/anyXF ->  888casino/com/en
     * @param path
     * @return
     */
    public static String extractLocalBrand(String path) {
        //  /content/(888casino/com/en)/anyPage/subpage ->  888casino/com/en
        //  /content/(experience-fragments/)(888casino/com/en)/anyXFFolder/anyXF ->  888casino/com/en
        String regex = "/content/(experience-fragments/)?([^/]*/[^/]*/[^/]*)/";
        Matcher matcher = Pattern.compile(regex).matcher(path);
        return matcher.find() ? matcher.group(2) : StringUtils.EMPTY;
    }
}
