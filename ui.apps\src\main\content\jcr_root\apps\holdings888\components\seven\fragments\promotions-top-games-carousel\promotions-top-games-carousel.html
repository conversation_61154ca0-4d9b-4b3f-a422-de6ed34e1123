<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-include="clientlibs.html" />
<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='listItems'}"/>
<sly data-sly-use.additionalListItems="${'holdings888/utils/multifield.js' @ multifieldName='additionalListItems'}"/>

<div class="promotion-title">
    <sly data-sly-resource="${'holdings888/components/seven/editorial/complex-title' @ resourceType='holdings888/components/seven/editorial/complex-title'}"/>
</div>

<div class="top-games-slider-container" data-currency="${properties.apiCurrency}" data-brand="${properties.apiBrand}">
    <div class="title-and-buttons">
        <div class="slider-container-title">
            <sly data-sly-resource="${'holdings888/components/seven/editorial/title' @ resourceType='holdings888/components/seven/editorial/title'}"/>
        </div>
        <div class="top-games-slider-buttons">
            <div class="top-games-butt-next">
                <span class="">
                    <svg width="2.5rem" height="2.5rem" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="">
                        <g>
                            <path fill="#4c1723" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z">
                            </path>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="top-games-butt-prev">
                <span class="">
                    <svg width="2.5rem" height="2.5rem" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="">
                        <g>
                            <path fill="#4c1723" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z">
                            </path>
                        </g>
                    </svg>
                </span>
            </div>
        </div>
    </div>
    <div class="top-games-slider">
        <div class="top-games-slider-carousel-wrapper">
            <div class="top-games-slider-roll">
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text=properties.text}"/>
                ${richText.text @ context='html'}
                <sly data-sly-resource="${'holdings888/components/seven/editorial/cta' @ resourceType='holdings888/components/seven/editorial/cta'}"/>
            </div>
            <div class="top-games-slider-wrapper">
                <div class="swiper-top-games">
                    <div class="swiper-wrapper">
                        <sly data-sly-list="${additionalListItems}">
                            <sly data-sly-use.gameLinkAdditional="${'holdings888.core.models.LinkModel' @ urlToProcess=item.properties.additionalDestinationUrl}"/>
                            <div class="swiper-slide additional-games" title="${item.properties.additionalGameIconAlt}" data-redirection="${gameLinkAdditional.relativePublishLink}" data-additional-icon="${item.properties.additionalGameIconReference @ context='uri'}">
                                <div class="game-info">
                                  <span class="game-name">${item.properties.additionalGameName}</span>
                                </div>
                            </div>
                        </sly>
                        <!-- Rest of API games -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="game-configurations" data-sly-list="${listItems}" style="display: none;">
    <sly data-sly-use.gameIcon="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=item.properties.gameIconReference}"/>
    <sly data-sly-set.gameIconPath="${gameIcon.renditions['webp'].path || gameIcon.renditions['original'].path}"/>
    <div class="game-configuration"
            data-game-id="${item.properties.gameId}"
            data-game-name="${item.properties.gameName}"
            data-icon="${gameIconPath @ context='uri'}"
            data-icon-alt="${item.properties.gameIconAlt}"
            data-destination-url="${item.properties.destinationUrl}"></div>
</div>
