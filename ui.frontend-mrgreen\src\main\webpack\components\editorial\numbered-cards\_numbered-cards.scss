.numbered-cards-component {
    padding: 4rem 0;

    @media (max-width: 840px){
        padding: 2rem 0;
    }

    .content__list {
        .list-item {
            display: inline-block;
            margin-bottom: 40px;
            width: 100%;

            @media (max-width: 840px) {
                margin-bottom: 20px;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
            }

            > div {
                float: left;
            }

            .list-item-number {
                @media (max-width: 840px) {
                    order: 0;
                }
                .number{
                    @extend .flex-centered, .rounded-circle;
                    background-color: $dark-shade-gray;
                    color: $black;
                    font-size: 24px;
                    font-weight: $font-weight-semi-bold;
                    width: 68px;
                    height: 68px;
                    line-height: 68px;
                    margin-bottom: 0.625rem;

                    @media (max-width: 840px) {
                        width: 50px;
                        height: 50px;
                        line-height: 50px;
                    }
                }
            }
            .list-item-image {
                width: 240px;

                img {
                    width: 100%;
                    height: auto;
                    padding: 0 15px;
                }

                @media (max-width: 840px) {
                    width: 210px;
                    order: 2;

                    img {
                        padding: 15px 0 15px;
                    }
                }
            }
            .list-item-title {
                width: 58%;
                height: fit-content;

                @media (max-width: 840px) {
                    width: 77%;
                    order: 1;
                }

                .list-title {
                    font-size: 1.6rem;
                    font-weight: $font-weight-regular;
                    font-family: $font-family-10;
                    line-height: $line-height;
                    color: $green-7;
                    margin-bottom: 1rem;
                    
                    @media (max-width: 840px) {
                        padding: 0 15px;
                        margin-bottom: 0;
                    }
                }
            }
            .list-item-text {
                width: 55%;
                font-size: $font-size-3;
                height: fit-content;

                p {
                    margin-bottom: 1rem;
                    font-weight: $font-weight-semi-bold;
                    font-size: 1.5rem;

                    a {
                        color: $green-7;
                    }
                }

                ul li:before {
                    color: $white;
                }

                @media (max-width: 840px) {
                    width: 100%;
                    order: 3;
                }
            }
        }
    }
}
