<!--/* Custom image dialog
        USAGE: 
        1. Add the following to the dialog:
        <image
                jcr:primaryType="nt:unstructured"
                path="/apps/holdings888/components/casino/editorial/claim-banner/dialog-include/double-image/"
                sling:resourceType="acs-commons/granite/ui/components/include">
                <parameters
                        jcr:primaryType="nt:unstructured"
                        fieldsetTitle="Image"
                        imagePrefixName="image"
                        imageIsRequired="{Boolean}true"
                        />
        </image>

        Parameters (optional node) for overriding the default values: 
        - fieldsetTitle: Title of the fieldset
        - imagePrefixName: Prefix for the image field names
        - imageIsRequired: Whether the images are required
*/-->

<jcr:root
        xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
    <items jcr:primaryType="nt:unstructured">
        <image
                jcr:primaryType="nt:unstructured"
                jcr:title="${{fieldsetTitle:Image}}"
                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
            <items jcr:primaryType="nt:unstructured">
                <imageDesktop
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                        allowUpload="{Boolean}false"
                        autoStart="{Boolean}false"
                        class="cq-droptarget"
                        fieldLabel="Desktop Asset"
                        fileReferenceParameter="./${{imagePrefixName:image}}DesktopPath"
                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                        multiple="{Boolean}false"
                        name="./${{imagePrefixName:image}}FileReference"
                        required="${{(Boolean)imageIsRequired:false}}"
                        uploadUrl="${suffix.path}"
                        useHTML5="{Boolean}true"/>
                <imageMobile
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                        allowUpload="{Boolean}false"
                        autoStart="{Boolean}false"
                        class="cq-droptarget"
                        fieldLabel="Mobile Asset"
                        fileReferenceParameter="./${{imagePrefixName:image}}MobilePath"
                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                        multiple="{Boolean}false"
                        name="./mobile${{imagePrefixName}}FileReference"
                        required="${{(Boolean)imageIsRequired:false}}"
                        uploadUrl="${suffix.path}"
                        useHTML5="{Boolean}true"/>
            </items>
        </image>
    </items>
</jcr:root>