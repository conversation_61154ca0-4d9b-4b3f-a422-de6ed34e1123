<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
      xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
      xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
      jcr:primaryType="nt:unstructured"
      jcr:title="Accordion Properties"
      extraClientlibs="[holdings888.components.author.editor]"
      sling:resourceType="cq/gui/components/authoring/dialog">
      <content
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
            <items jcr:primaryType="nt:unstructured">
                  <tabs
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/tabs"
                        maximized="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                              <tab1
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Card 1"
                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                    margin="{Boolean}true">
                                    <items jcr:primaryType="nt:unstructured">
                                          <card1image1
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                      jcr:primaryType="nt:unstructured"
                                                      imageLabel="First linked image"
                                                      imageDescription="Left linked image at the bottom of the card"
                                                      imagePrefixName="card1image1"
                                                      altName="altCard1image1"/>
                                          </card1image1>
                                          <card1image1link
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Link for the image"
                                                jcr:description="Insert the link for the image"
                                                rootPath="/content"
                                                name="./card1image1link" />
                                          <card1image2
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                      jcr:primaryType="nt:unstructured"
                                                      imageLabel="First linked image"
                                                      imageDescription="Left linked image at the bottom of the card"
                                                      imagePrefixName="card1image2"
                                                      altName="altCard1image2"/>
                                          </card1image2>
                                          <card1image2link
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Link for the image"
                                                jcr:description="Insert the link for the image"
                                                rootPath="/content"
                                                name="./card1image2link"/>
                                    </items>
                              </tab1>
                              <tab2
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Card 2"
                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                    margin="{Boolean}true">
                                    <items jcr:primaryType="nt:unstructured">
                                          <enabletab2
                                                  jcr:primaryType="nt:unstructured"
                                                  sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                  text="Enable Tab"
                                                  name="./enableTab2"
                                                  value="true"
                                                  uncheckedValue="false" />
                                          <card2image1
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                      jcr:primaryType="nt:unstructured"
                                                      imageLabel="First linked image"
                                                      imageDescription="Left linked image at the bottom of the card"
                                                      imagePrefixName="card2image1"
                                                      altName="altCard2image1"
                                                />
                                          </card2image1>
                                          <card2image1link
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Link for the image"
                                                jcr:description="Insert the link for the image"
                                                rootPath="/content"
                                                name="./card2image1link" />
                                          <card2image2
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                                <parameters
                                                      jcr:primaryType="nt:unstructured"
                                                      imageLabel="First linked image"
                                                      imageDescription="Left linked image at the bottom of the card"
                                                      imagePrefixName="card2image2"
                                                      altName="altCard2image2"
                                                />
                                          </card2image2>
                                          <card2image2link
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Link for the image"
                                                jcr:description="Insert the link for the image"
                                                rootPath="/content"
                                                name="./card2image2link" />
                                    </items>
                              </tab2>
                               <tabPadding
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="holdings888/components/dialog-include/padding" />
                        </items>
                  </tabs>
            </items>
      </content>
</jcr:root>