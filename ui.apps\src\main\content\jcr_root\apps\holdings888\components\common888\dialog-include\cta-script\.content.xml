<!--/* Common ctaScript dialog with parametrization
        USAGE:
        1. Add the following to the dialog:
        <ctaScript
                jcr:primaryType="nt:unstructured"
                path="/apps/holdings888/components/common888/dialog-include/cta-script"
                sling:resourceType="acs-commons/granite/ui/components/include">
                <parameters
                        jcr:primaryType="nt:unstructured"
                        ctaScriptName="ctaScript"
                        ctaScriptLabel="CTA Script"
                        generateScriptName="generateScript"
                        damRoot="holdings888"/>
        </ctaScript>

        Parameters (optional node) for overriding the default values:
        - ctaScriptName: the name of the ctaScript field
        - ctaScriptLabel: the label of the ctaScript field
        - generateScriptName: the name of the generateScript field
        - damRoot: the root path of the dam for smart actions lists (holdings888 or mrgreen)

        2. Add clientlib to dialog
        extraClientlibs="[holdings888.dialog.SAGenerator]"

        for using in multifield:
        Add granite:class="cta-multifield" to multifield node
        sling:resourceType="granite/ui/components/coral/foundation/form/multifield"

*/-->

<jcr:root
        xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        jcr:primaryType="nt:unstructured"
        granite:class="smart-actions-cta-script-container"
        sling:resourceType="granite/ui/components/coral/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <ctaScript
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                resize="vertical"
                jcr:description="The JS Script that will be executed on click"
                granite:class="smart-actions-cta-script"
                name="./${{ctaScriptName:ctaScript}}"
                fieldLabel="${{ctaScriptLabel:CTA Script}}"/>
        <smartActions
                jcr:primaryType="nt:unstructured"
                path="holdings888/components/common888/dialog-include/smart-actions"
                sling:resourceType="acs-commons/granite/ui/components/include">
            <parameters
                    jcr:primaryType="nt:unstructured"
                    generateScriptName="${{generateScriptName:generateScript}}"
                    damRoot="${{damRoot:holdings888}}"
            />
        </smartActions>
    </items>
</jcr:root>