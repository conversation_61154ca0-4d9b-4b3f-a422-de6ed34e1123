.cmp-game-info-navbar {
    @media only screen and (max-width: $sm-max) {
        display: none;
    }

    background: transparent;
    margin-bottom: 15px;

    .cmp-game-info-navbar {
        &__container {
            margin: 0 auto;
        }

        &__row {
            max-width: 100%;
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }

        &__logo {
            width: 100%;
            max-width: 14rem;
            padding: 1rem 0 1.5rem;
            @media only screen and (min-width: $sm) {
                margin-left: -1rem;
            }
            @media only screen and (min-width: $lg-max) {
                max-width: 16rem;
            }
        }

        &__list {
            list-style: none;
            margin-left: 0;
            margin-bottom: 1rem;
        }

        &__item {
            display: inline-block;
            &::after {
                content: "|";
                color: $anchor-menu-divider;
                cursor: default;
            }
            &:last-child::after {
                display: none;
            }
            &::before {
                display: none;
            }
        }

        &__anchor {
            font-size: 13px;
            color: $anchor-menu-color;
            line-height: 1.4rem;
            text-decoration: none;
            padding: 0 0.5rem;
            font-weight: $font-weight-semi-bold;
            font-family: $font-family-9;

            &.uppercaseClass {
                text-transform: uppercase;
            }

            &:hover {
                color: $green-3;
                cursor: pointer;
            }
        }
    }
}
.highlightFirst {
    .cmp-game-info-navbar {
        &__item:first-child .cmp-game-info-navbar__anchor {
            color: $green-3;
            font-weight: $font-weight-bold;
        }
    }
}
