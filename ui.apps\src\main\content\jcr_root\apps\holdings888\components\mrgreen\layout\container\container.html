<sly data-sly-use.container="com.adobe.cq.wcm.core.components.models.LayoutContainer" />
<sly data-sly-use.url="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.url}" />
<sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.script}" />

<div
    class="${properties.cssClass} ${properties.backgroundSelection} ${properties.cornerRadius ? 'container-corner-radius' : ''} ${properties.url ? 'position-relative' : ''} ${properties.paddingTop} ${properties.paddingBottom} ${properties.widePadding ? 'wide-padding' : ''}"
    id="${currentStyle.containerId}"
    data-mbox-id="${properties.mboxId}">
    <a
        class="container-link"
        href="${url.relativePublishLink}"
        data-sly-unwrap="${!url.relativePublishLink}"
        onclick="${scriptProcessor.processedScript @ context='unsafe'}"></a>
    <div class="container-wrapper ${properties.isFullWidth ? '' : 'main-container'} ${properties.colGridDesktop} ${properties.colGridTablet} ${properties.colGridMobile} ${properties.isFullWidthWithPadding ? 'full-width-padded' : ''}">
        <div
            data-sly-test="${properties.displayBackground}"
            class="display-background"
            style="background-image: url('${properties.imagePath @ context='unsafe'}')"
            aria-label="${properties.imageAlt}">
            <sly
                data-sly-use.simpleTemplate="simple.html"
                data-sly-call="${simpleTemplate.simple @ container=container}"></sly>
        </div>

        <sly
            data-sly-test="${!properties.displayBackground}"
            data-sly-use.simpleTemplate="simple.html"
            data-sly-call="${simpleTemplate.simple @ container=container}"></sly>
    </div>
</div>
