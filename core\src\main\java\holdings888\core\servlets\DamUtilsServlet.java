package holdings888.core.servlets;

import com.adobe.granite.ui.components.ds.DataSource;
import com.adobe.granite.ui.components.ds.SimpleDataSource;
import com.adobe.granite.ui.components.ds.ValueMapResource;
import com.day.cq.wcm.api.Page;
import com.day.cq.wcm.api.PageManager;

import holdings888.core.bean.BlogAuthorItem;
import holdings888.core.models.FlagImageModel;
import holdings888.core.utils.BlogUtils;
import org.apache.http.HttpHeaders;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceMetadata;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.api.wrappers.ValueMapDecorator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.day.cq.commons.jcr.JcrConstants.JCR_PRIMARYTYPE;
import static com.day.cq.commons.jcr.JcrConstants.NT_UNSTRUCTURED;
import static com.day.cq.dam.api.DamConstants.NT_DAM_ASSET;
import static holdings888.core.utils.PageUtils.getValueOfProperty;

public class DamUtilsServlet {

    private static final Logger logger = LoggerFactory.getLogger(DamUtilsServlet.class);

    /**
     * Populate drop down list of select request with elements present in assetPath
     * sorted alphabetically
     *
     * @param request
     * @param assetPath
     */
    public static void populateDropDownListWithAssets(SlingHttpServletRequest request, String assetPath) {

        try {
            ResourceResolver rr = request.getResourceResolver();
            Resource resource = rr.getResource(assetPath);

            Map<String, String> dropDownMap = new LinkedHashMap<>();

            if (resource != null) {
                Iterator<Resource> iterator = resource.listChildren();
                List<Resource> list = new ArrayList<>();
                iterator.forEachRemaining(list::add);
                list.stream().filter(s -> getValueOfProperty(s, JCR_PRIMARYTYPE).equals(NT_DAM_ASSET)).forEach(res -> {
                    dropDownMap.put(res.adaptTo(FlagImageModel.class).getTitle(), res.getPath());
                });
            }

            request.setAttribute(DataSource.class.getName(), getDataSource(dropDownMap, rr));

        } catch (Exception e) {
            logger.warn("[888] - DamUtilsServlet - Error on get %s dropdown values - Exception: %s", assetPath,
                    e.getMessage());
        }
    }

    private static boolean isValidPathSegment(String referer) {
        return (referer != null && referer.contains("content/sites/createpagewizard.html"));

    }

    public static void populateDropDownListWithAuthors(SlingHttpServletRequest request) {
        int HOME_PAGE_ROOT_CONTENT_DEPTH = 3;

        try {
            // Retrieve the 'item' parameter from the request
            String path = request.getParameter("item");
            if (path == null) {
                String referer = request.getHeader(HttpHeaders.REFERER);
                if (isValidPathSegment(referer)) {
                    String[] refereStrings = referer.split(".html");
                    if (refereStrings.length > 1) {
                        path = refereStrings[1];
                    }
                }
            }
            ResourceResolver resourceResolver = request.getResourceResolver();
            Resource resource = resourceResolver.getResource(path);
            PageManager pageManager = resourceResolver.adaptTo(PageManager.class);

            // Get the absolute parent page at a specified depth
            Page currentPage = pageManager.getContainingPage(path);

            // Get the home page based on depth
            Page homePage = currentPage.getAbsoluteParent(HOME_PAGE_ROOT_CONTENT_DEPTH);

            // Retrieve the 'blogauthors' property from the home page
            String blogAuthorsValue = homePage.getProperties().get("blogauthors", String.class);

            // <author name, author node path>
            Map<String, String> dropDownMap = new LinkedHashMap<>();

            if (resource != null) {
                List<BlogAuthorItem> authors = BlogUtils
                        .getAllAuthors(resourceResolver.getResource(blogAuthorsValue).adaptTo(Page.class));
                authors.forEach(author -> {
                    dropDownMap.put(author.getName(), author.getNodePath());
                });
            }
            request.setAttribute(DataSource.class.getName(), getDataSource(dropDownMap, resourceResolver));

        } catch (Exception e) {
            logger.warn("[888] - DamUtilsServlet - Error on get dropdown values - Exception: {}",
                    e.getMessage());
        }
    }

    public static void populateDropDownListWithCategories(SlingHttpServletRequest request, String assetPath) {

        try {
            ResourceResolver rr = request.getResourceResolver();
            Resource resource = rr.getResource(assetPath);

            // <author name, author node path>
            Map<String, String> dropDownMap = new LinkedHashMap<>();

            if (resource != null) {
                List<String> categories = BlogUtils.getAllCategories(rr, rr.getResource(assetPath).adaptTo(Page.class));
                categories.forEach(categoryPath -> {
                    String categoryName = rr.getResource(categoryPath).adaptTo(Page.class).getTitle();
                    dropDownMap.put(categoryName, categoryPath);
                });
            }
            request.setAttribute(DataSource.class.getName(), getDataSource(dropDownMap, rr));

        } catch (Exception e) {
            logger.warn("[888] - DamUtilsServlet - Error on get %s dropdown values - Exception: %s", assetPath,
                    e.getMessage());
        }
    }

    private static DataSource getDataSource(Map<String, String> dropDownMap, ResourceResolver rr) {

        List<Resource> resourceList = new ArrayList<>();
        for (Map.Entry<String, String> entry : dropDownMap.entrySet()) {
            ValueMap valueMap = new ValueMapDecorator(new HashMap<>());
            valueMap.put("text", entry.getKey());
            valueMap.put("value", entry.getValue());

            Resource valueMapResource = new ValueMapResource(rr, new ResourceMetadata(), NT_UNSTRUCTURED, valueMap);

            resourceList.add(valueMapResource);
        }
        return new SimpleDataSource(resourceList.iterator());
    }

    private static DataSource getCasinoDataSource(Map<String, String> dropDownMap, ResourceResolver rr) {

        List<Resource> resourceList = new ArrayList<>();
        for (Map.Entry<String, String> entry : dropDownMap.entrySet()) {
            ValueMap valueMap = new ValueMapDecorator(new HashMap<>());

            valueMap.put("text", entry.getValue());
            valueMap.put("value", entry.getValue());

            Resource valueMapResource = new ValueMapResource(rr, new ResourceMetadata(), NT_UNSTRUCTURED, valueMap);

            resourceList.add(valueMapResource);
        }
        return new SimpleDataSource(resourceList.iterator());
    }

    public static void populateCasinoDropDownListWithCategories(SlingHttpServletRequest request, String url) {
        populateDropDownForProperties(request, url, "category");
    }

    public static void populateCasinoDropDownListWithAudiences(SlingHttpServletRequest request, String url) {
        populateDropDownForProperties(request, url, "audience");
    }

    private static void populateDropDownForProperties(SlingHttpServletRequest request, String url , String  propertyName) {
        Map<String, String> dropDownList = new LinkedHashMap<>();
        ResourceResolver rr = request.getResourceResolver();
        try {
            Resource resource = rr.getResource(url);
            if (Objects.nonNull(resource)) {
                Iterable<Resource> properties = rr.getChildren(resource);

                for (Resource child : properties) {
                    Map<String, Object> propertiesMap = child.getValueMap();
                    String propertyValue = (String) propertiesMap.get(propertyName);
                    dropDownList.put(propertyValue, propertyValue);
                }
            }

        } catch (Exception e) {
            logger.warn("[888] - DamUtilsServlet - Error on get casino {} dropdown values - Exception: {}", propertyName,
                    e.getMessage());
        }
        request.setAttribute(DataSource.class.getName(), getCasinoDataSource(dropDownList, rr));
    }
}
