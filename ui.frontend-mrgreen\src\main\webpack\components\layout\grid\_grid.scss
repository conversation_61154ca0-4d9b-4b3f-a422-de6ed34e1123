.grid-component {
  display: grid;
  width: 100%;
  max-width: 100%;

  .column-container {
    width: 100%;
    padding: 0;
    margin: 0;
    position: relative;

    &.wrapTablet{
      @media (max-width: 800px) and (orientation: landscape)   {
        display: flex;
        flex-direction: column;
        padding: 0 10%;
        align-items: center;
      }

      @media only screen and (min-width: $sm) and (max-width: $md-max){
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        padding: 0 10%;
      }
    }

    &.minWidth{
      @media only screen and (min-width: $sm) and (max-width: $md-max){
        width: 85%;
        margin: 0 auto;
      }
    }

    .column {
      position: relative;
      padding: 0;
      margin: 0;
      float: left;
      height: auto !important;

      &.divider{
        .parsys{
          margin: 0.8rem;
        }

        p{
          height: 100%;

          @media (min-width: $sm) and (max-width: $md-max){
            height: 22rem;
          }

          @media (min-width: $md){
            height: 16rem;
          }
        }

        &:not(:last-child):after{
          content: " ";
          width: 100%;
          position: absolute;
        }

        &:not(:first-child):after{
          border-left: none;
        }

        @media (min-width: $sm){
          &:not(:last-child):after{
            border-top: none;
            width: unset;
          }

          &:not(:first-child):after{
            border-left: 1px solid $grid-divider-color;
            content: " ";
            height: 90%;
            width: auto;
            position: absolute;
            top: 0;
          }
        }
      }

      &.divider-noRow{
        .parsys{
          margin: 0.8rem;
        }

        &:not(:last-child):after{
          content: " ";
          width: 100%;
          position: absolute;
        }

        &:not(:first-child):after{
          border-left: none;
        }

        @media (min-width: $md){
          &:not(:last-child):after{
            border-top: none;
          }

          &:not(:first-child):after{
            border-left: 1px solid $grid-divider-color;
            content: " ";
            height: 90%;
            width: auto;
            position: absolute;
            top: 0;
          }
        }
      }

      .parsys > div {
        padding: 1rem;

        @media (max-width: $sm-grid) {
          padding: 1rem 0;
        }
      }

      &:first-child {
        .parsys > div {
          padding-left: 0;
        }
      }

      &:last-child {
        .parsys > div {
          padding-right: 0;
        }
      }

    }

    &.one-column {
      &>.column {
        width: 100%;
      }
    }
    &.two-columns-66-33,
    &.two-columns-33-66,
    &.two-columns-50,
    &.two-columns-33-33{
      .column{
        &.divider{
          &:last-child:after{
            border-top: none;
            width: unset;
          }
          &:first-child:after{
            display: none;
          }

          @media (min-width: $sm){
            &:last-child:after{
              border-right: none
            }

            &:first-child:after{
              border-top: none;
              border-right: 1px solid $grid-divider-color;
              content: " ";
              height: 90%;
              width: auto;
              position: absolute;
              top: 0;
            }
          }
        }
      }
    }

    &.two-columns-33-33{
      .column{
        &.divider{
          &:last-child:after{
            left: 50%;
          }
        }
      }
    }

    &.two-columns-33-33-left{
      .column{
        &.divider{
          &:last-child:after{
            left: 33.33%;
          }
        }
      }
    }

    &.two-columns-66-33,

    &.two-columns-33-66,

    &.two-columns-50,

    &.two-columns-33-33,

    &.three-columns {
      &:not(.edit-mode) {
       @media(max-width: $sm) {
          .column {
          width: 100%;
          }
        }
        @media only screen and (min-width: $sm) and (max-width: $md-max) {
          .column {
              width: 100%;

            &.ipadRow{
              width: 33.33%;

              .parsys > div {
                padding: 1rem;
              }
            }
          }
        }

        &.wrapTablet{
          @media (max-width: 800px) and (orientation: landscape)   {
            .column {
              width: 60%;
              padding: 1%;
              &:nth-child(2) {
                .parsys > div {
                  padding-left: unset;
                  padding-right: unset;
                  padding:  1rem 0;
                }
              }
            }
          }

          @media only screen and (min-width: $sm) and (max-width: $md-max){
            .column {
              width: 50%;
              padding: 1%;
              &:nth-child(2) {
                .parsys > div {
                  padding-left: unset;
                  padding-right: unset;
                  padding:  1rem 0;
                }
              }
            }
          }
        }
      }
    }

    &.two-columns-66-33 {
     .column {
            width: 100%;

        @media(min-width: $md) {
          &:first-child {
            width: 66.66666667%;
            padding: 1rem;
          }
          &:last-child {
            width: 33.33333333%;
            padding: 1rem;
          }
        }
      }

      &.landscape-two{
        @media(min-width: $sm-grid) {
          .column {
            &:first-child {
              width: 66.66666667%;

              padding: 1rem;
            }
            &:last-child {
              width: 33.33333333%;
              padding: 1rem;
            }
          }
        }
      }
    }

    &.two-columns-33-33 {
      text-align: center;
      display: block;
      @media(min-width: $md) {
        justify-content: center;
        display: flex;
      }
      .column {
        width: 100%;

        @media(min-width: $md) {
          &:first-child {
            width: 33.33333333%;
            padding: 1rem;
          }
          &:last-child {
            width: 33.33333333%;
            padding: 1rem;
          }
        }
      }
      &.landscape-two{
        @media(min-width: $sm-grid) {
          .column {
            &:first-child {
              width: 33.33333333%;
              display: inline-block;
              float: inherit;
              padding: 1rem;
            }
            &:last-child {
              width: 33.33333333%;
              display: inline-block;
              float: inherit;
              padding: 1rem;
            }
          }
        }
      }
    }

    &.two-columns-33-33-left {
      @media(min-width: $md) {
        justify-content: start;
      }
    }

    &.two-columns-33-66 {
      .column {
          width: 100%;
        @media(min-width: $md) {
            &:first-child {
              width: 33.33333333%;
              padding:  1rem;
            }
            &:last-child {
              width: 66.66666667%;
              padding:  1rem;
            }
        }
      }

      &.landscape-two{
        .column {
          width: 100%;
          @media(min-width: $sm-grid) {
            &:first-child {
              width: 33.33333333%;
              padding: 1rem;
            }
            &:last-child {
              width: 66.66666667%;
              padding: 1rem;
            }
          }

        }
      }
    }

    &.two-columns-50 {
      .column {
        width: 100%;

        @media(min-width: 769px) {
          &:first-child {
            width: 50%;
          }
          &:last-child {
            width: 50%;

          }
        }
      }
      &.landscape-two{
        .column {
          @media(min-width: $sm) and (max-width: $sm-grid - 1) and (orientation: landscape){
            width: 50%;
            padding: 1rem;
          }

          @media (min-width: $sm-grid) {
            width: 50%;
            padding: 1rem;
          }
        }

        &.inverted {
          &:not(.edit-mode) {
            @media (max-width: $sm-grid) {
              flex-direction: column-reverse;
              display: flex;
            }

            @media(min-width: $sm) and (max-width: $sm-grid - 1) and (orientation: landscape){
              flex-direction: row;
            }
          }
        }
      }
    }

    &.three-columns {
      .column {
          width: 100%;
        .parsys > div {
          padding: 1rem 0.66rem;

          @media (max-width: $md-max) {
            padding: 1rem 0;
          }
        }
        &:first-child {
          .parsys > div {
            padding-left: 0;
          }
        }

        &:last-child {
          .parsys > div {
            padding-right: 0;
          }
        }

          &:nth-child(2) {
            .parsys > div {
              padding: 1rem 0;
              @media(min-width: $md) {
                padding: 1rem 0.33rem;
              }
            }
          }


        @media(min-width: $md) {
          width: 33.33%;
          padding-left: .5rem;
          padding-right: .5rem;
        }
      }
    }
    &.four-columns {
      .column {
        width: 100%;

        .parsys > div{
          padding: 1rem;

          @media (max-width: $md-max) {
            padding: 1rem 0;
          }
        }
        @media(min-width: $sm-air) {
          width: 25%;
        }
      }
    }

    &.inverted {
      &:not(.edit-mode) {
        &:not(.landscape-two) {
          @media (max-width: $md-max) {
            display: flex;
            flex-direction: column-reverse;
          }
        }
      }
    }
  }
  .left-gradient, .right-gradient {
    position: absolute;
    width: 110%;
    height: 110%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 100%;
    background-color: #015536;
    filter: blur(60px);
    z-index: 0;
  }
}
