function addListenerScroll($cmp){
    $cmp.forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            let navbarHeight = 0;
            const cyHeader = document.querySelector('.cy-header-regulation-data');
            const cyNavbar = document.querySelector('.cy-navbar-container');
            const fixedCategories = document.querySelector('.promotions-category-icons-anchor-cmp');
            const mainHeader = document.querySelector('.uc-main-header');
            const contentArea = document.querySelector('.uc-content-area');
            let container = window;

            if (cyHeader) {
                navbarHeight += cyHeader.clientHeight;
            }
            if (cyNavbar) {
                navbarHeight += cyNavbar.clientHeight;
            }
            if (mainHeader && contentArea) {
                navbarHeight += mainHeader.clientHeight;
                container = contentArea;
            }
            if (window.innerWidth <= 768 && fixedCategories) {
                navbarHeight += fixedCategories.clientHeight;
            }

            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                const offsetTop = targetSection.offsetTop;
                container.scrollTo({
                    top: offsetTop - navbarHeight,
                    behavior: 'smooth'
                });
            }
        });
    });
}

var anchorLinks = document.querySelectorAll('.root a[href^="#"]');
addListenerScroll(anchorLinks);
