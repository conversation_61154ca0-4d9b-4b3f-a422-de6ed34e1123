'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TSConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const SOURCE_ROOT = __dirname + '/src/main/webpack';

const resolve = {
    extensions: ['.js', '.ts'],
    plugins: [new TSConfigPathsPlugin({
        configFile: './tsconfig.json'
    })]
};

module.exports = {
    resolve: resolve,
    entry: {
        poker : SOURCE_ROOT + '/poker/main.ts',
        'poker-blog' : SOURCE_ROOT + '/poker/main-blog.ts',
        'poker-top-section' : SOURCE_ROOT + '/poker/main-top-section.ts',
        'poker-amp' : SOURCE_ROOT + '/poker/amp/main.ts',
        'poker-blog-amp' : SOURCE_ROOT + '/poker/amp/main-blog.ts',
				'poker-claim' : SOURCE_ROOT + '/components/claim-poker/claim-poker.ts',

    },
    optimization: {
        minimize:false
    },
    output: {
        filename: (chunkData) => {
            return chunkData.chunk.name === 'dependencies' ? 'clientlib-dependencies/[name].js' : 'clientlib-[name]/[name].js';
        },
        path: path.resolve(__dirname, 'dist')
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'ts-loader'
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve
                        }
                    }
                ]
            },
            {
                test: /\.css$/i,
                use: ["style-loader", "css-loader"],
            },
            {
                test: /\.scss$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            url: false
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            plugins() {
                                return [
                                    require('autoprefixer')
                                ];
                            }
                        }
                    },
                    {
                        loader: 'sass-loader',
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve
                        }
                    }
                ]
            }
        ]
    },
    plugins: [
        new CleanWebpackPlugin(),
        new ESLintPlugin({
            extensions: ['js', 'ts', 'tsx']
        }),
        new MiniCssExtractPlugin({
            filename: 'clientlib-[name]/[name].css'
        }),
        new CopyWebpackPlugin({
            patterns: [
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/poker'), to: './clientlib-poker' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/poker'), to: './clientlib-poker-blog' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/poker'), to: './clientlib-poker-top-section' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/poker'), to: './clientlib-poker-amp' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/poker'), to: './clientlib-poker-blog-amp' },
            ]
        })
    ],
    stats: {
        assetsSort: 'chunks',
        builtAt: true,
        children: false,
        chunkGroups: true,
        chunkOrigins: true,
        colors: false,
        errors: true,
        errorDetails: true,
        env: true,
        modules: false,
        performance: true,
        providedExports: true,
        source: true,
        warnings: true
    }
};
