<!DOCTYPE HTML>
<html data-sly-use.pageModel="holdings888.core.models.PageModel"
      data-sly-use.version="holdings888.core.models.CodeBaseVersioningModel"
      data-sly-test.page="${pageModel.page}"
      lang="${page.language}"
      data-sly-use.pwa="com.adobe.cq.wcm.core.components.models.PWA"
      data-sly-use.head="head.html"
      data-sly-use.script="script.html"
      data-sly-use.customfooterlibs="customfooterlibs.html"
      data-sly-use.customheaderlibs="customheaderlibs.html"
      data-sly-use.redirect="redirect.html"
      data-sly-use.bodySkipToMainContent="body.skiptomaincontent.html">
    <head>
        <sly data-sly-call="${head.head @ pageModel = pageModel, pwa = pwa, version = version}"></sly>
    </head>

    <body>
        <!--/* Splash loader from client */-->
        <!-- Placeholder: StaticPreloaderContainer -->
        <!-- Placeholder: StaticPreloaderInlineScripts -->

        <sly data-sly-test="${pageModel.externalJsCssFooterTop}">
            <! -- External CSS Body Top -->
            ${pageModel.externalJsCssFooterTop @ context="unsafe"}
            <! -- / External CSS Body Top -->
        </sly>
        <div id="page">
            <div dir="${pageModel.textDirection != 'none' ? pageModel.textDirection : ''}" data-sly-unwrap="${pageModel.textDirection == 'none'}">
                <link href="https://webassets.images4us.com/fonts/fonts.css?v=2.4.617" rel="preload stylesheet" type="text/css">
                <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
                <sly data-sly-call="${customheaderlibs.customheaderlibs @ pageModel = pageModel}"></sly>
                <sly data-sly-call="${script.body @ pageModel = pageModel}"></sly>
                <sly data-sly-include="body.html"></sly>
                <sly data-sly-call="${customfooterlibs.customfooterlibs @ pageModel = pageModel}"></sly>
            </div>
        </div>
        <sly data-sly-test="${pageModel.externalJsCssFooterBottom}">
            <! -- External CSS JS Body Bottom -->
            ${pageModel.externalJsCssFooterBottom @ context="unsafe"}
            <! -- / External CSS Body Bottom -->
        </sly>
    </body>

</html>