promoLobby = {
    promotionsLabels: window.promotionsGeneralLabels  ? window.promotionsGeneralLabels : {
        lastSeenLabel: "Last seen on the ",
        readMore: "Read More",
        readLess: "Read Less",
        flashLabel: "NEW",
    },
    itemRead: function (id) {
        let promoList = localStorage.getItem("newPromoList");
        if (promoList === null) {
            localStorage.setItem("newPromoList", "{}");
            promoList = localStorage.getItem("newPromoList");
        }
        if (promoList) {
            promoList = JSON.parse(promoList);
            let date = new Date();
            promoList[id] = promoList[id] || date;
            promoLobby.showReadDate(id, promoList[id]);
            localStorage.setItem("newPromoList", JSON.stringify(promoList));
        }
    },
    showReadDate: function (id, date) {
        let item = document.querySelector('.promoCr[promourlid="' + id + '"]'); // target the promo item with that "id"
        if (item) {
            item.querySelector(".newItem").classList.add("readed");
            let theDate = new Date(date);
            item.querySelector(".newItem").textContent = promoLobby.promotionsLabels.lastSeenLabel +
                theDate.toLocaleDateString() + " " + theDate.toLocaleTimeString();
        }
    },
    newPromoItems: function () {
        let newPromoList = localStorage.getItem("newPromoList");
        if (!document.querySelector('.newItemOut')) {
            let newItemOut = document.createElement("span");
            newItemOut.classList.add("newItemOut");
            let newItem = document.createElement("span");
            newItem.classList.add("newItem");
            newItem.textContent = promoLobby.promotionsLabels.flashLabel;
            newItemOut.appendChild(newItem);
            document.querySelector(".promo-description").prepend(newItemOut);
        }
        else {
           document.querySelectorAll('.newItem:not(.readed)').forEach(function (newItem) {
                        newItem.textContent = promoLobby.promotionsLabels.flashLabel;
                    });
        }
        if (newPromoList !== null) {
            newPromoList = JSON.parse(newPromoList);
            for (let id in newPromoList) {
                promoLobby.showReadDate(id, newPromoList[id]);
            }
        }
    },
    setReadBtn: function (button, isClosed) {
        button.querySelector("span:first-child").textContent = isClosed ?
            promoLobby.promotionsLabels.readMore : promoLobby.promotionsLabels.readLess;
    },
    moveTermsLink: function(cmp) {
        cmp.querySelectorAll(".promoCTAsig").forEach(function (promoCta) {
            let pTag = promoCta.querySelector("p");
            let aTag = promoCta.querySelector("a.promoCTAFullTerms");
    
            if (pTag && aTag) {
                pTag.appendChild(aTag);
            }
        });
    },    
    togglePromo: function (cmp) {
        let clickables = cmp.querySelectorAll(".promoRM");
        clickables.forEach(function (clickable) {
            clickable.addEventListener("click", function () {
                let card = this.closest(".promoCr")
                    , readMoreButton = card.querySelector(".promoRM")
                    , fullText = card.querySelector(".promoFullText");
                cmp.querySelectorAll(".promoRM").forEach(function (rm) {
                    if (rm !== readMoreButton) {
                        rm.classList.remove("active");
                    }
                });
                cmp.querySelectorAll(".promoCr").forEach(function (cr) {
                    if (cr !== card) {
                        cr.classList.remove("promoOpened");
                    }
                });
                if (card.classList.contains("promoOpened")) {
                    card.classList.remove("promoOpened");
                    promoLobby.textSlide(fullText);
                    promoLobby.setReadBtn(readMoreButton, true);
                } else {
                    readMoreButton.classList.add("active");
                    card.classList.add("promoOpened");
                    promoLobby.textSlide(fullText);
                    promoLobby.setReadBtn(readMoreButton, false);
                    promoLobby.itemRead(card.getAttribute("promourlid"));
                }
            });
        });
    },    
    textSlide : function (promoText) {
        const active = promoText.style.height !== ''
            && promoText.style.height !== '0px';

        if(active){
            promoText.style.height = "0px";
            promoText.addEventListener('transitionend', () => {
                promoText.style.display = 'none';
            }, {once: true});
            return;
        }
        promoText.style.display = 'block';
        promoText.style.height = "auto";
        const height = promoText.clientHeight + "px";
        promoText.style.height = 0;
        setTimeout(() => {
            promoText.style.height = height;
        }, 0);

        promoText.addEventListener('transitionend', () => {
            promoText.style.display = 'auto';
        }, {once: true});
    },
    termsPopupToggle: function(fullTermsLink) {
        let fullTerms = document.querySelector("." + fullTermsLink.getAttribute("id"));
        fullTermsLink.addEventListener("click", function() {
            if (document.querySelector('.pageCover') != null) {
                return;
            }
            if ("Popup" === fullTermsLink.getAttribute("ter") && document.querySelector('.pageCover') == null) {
                document.querySelector("body").style.overflowY = "hidden";
                document.querySelector("body").insertAdjacentHTML("beforeend", '<div class="pageCover"></div>');
                document.querySelector("body, .pageCover").addEventListener("touchmove", function(e) {
                    e.preventDefault();
                });
            }

            if (window.innerWidth < 1280) {
                let fullTermsHeight = window.innerHeight - promoLobby.getUcHeaderHeight() - 10;
                fullTerms.style.maxHeight = fullTermsHeight + "px";
                window.addEventListener("resize", function() {
                    let fullTermsHeight = window.innerHeight - promoLobby.getUcHeaderHeight() - 10;
                    fullTerms.style.maxHeight = fullTermsHeight + "px";
                });
            }

            promoLobby.slideToggle(fullTerms);
        });


    },
    slideToggle: function(element) {
        const currentDisplay = getComputedStyle(element).display;
        if (currentDisplay === 'none') {
            element.style.display = 'block';
            const elementHeight = element.clientHeight;
            element.style.height = 0;
            setTimeout(() => {
                element.style.height = `${elementHeight}px`;
            } , 0);
        } else {
            element.style.height = `${element.offsetHeight}px`;
            element.offsetHeight;
            element.style.height = 0;

            element.addEventListener('transitionend', function transitionEnd() {
                element.removeEventListener('transitionend', transitionEnd);
                element.style.display = 'none';
                element.style.height = '';
            });
        }
    },
    getUcHeaderHeight: function() {
        return document.querySelector(".uc-main-header") ? document.querySelector(".uc-main-header").offsetHeight : 50;
    },
    closeTerms: function() {
        document.querySelectorAll(".closeTNC").forEach(function (closeBtn) {
            closeBtn.addEventListener("click", function () {
                let fullTerms = this.closest(".promoCrFullTermsIn").closest(".promoCrFullTerms");
                toggleTerms(fullTerms);
            });
        });

        document.querySelectorAll(".promoSigImg").forEach(function (promoSigImg) {
            promoSigImg.addEventListener("touchstart", function () {
                let fullTerms = this.closest(".promoCrFullTerms");
                toggleTerms(fullTerms);
            });
        });

        function toggleTerms(fullTerms) {
            promoLobby.slideToggle(fullTerms);
            document.querySelector("body").style.overflowY = "visible";
            document.querySelector(".pageCover")?.remove();
            document.querySelector("body, .pageCover").removeEventListener("touchmove", function(e) {
                e.preventDefault();
            });
        }
    },
    init: function (cmp) {
        promoLobby.moveTermsLink(cmp);
        promoLobby.togglePromo(cmp);
        promoLobby.newPromoItems();
        cmp.querySelectorAll(".promoCTAFullTerms").forEach(function (ft) {
            promoLobby.termsPopupToggle(ft);
        });
        cmp.querySelectorAll(".promoRM").forEach(function (rm) {
            promoLobby.setReadBtn(rm, true);
        });
        promoLobby.closeTerms();
    }

};

function promotionsCardsInit() {
    document.querySelectorAll(".promotions-card-component").forEach(function (cmp) {
        promoLobby.init(cmp);
    });
    document.querySelectorAll('.promotions-card-component img').forEach(function(img) {
        img.removeAttribute('title');
    });
}

promotionsCardsInit();




