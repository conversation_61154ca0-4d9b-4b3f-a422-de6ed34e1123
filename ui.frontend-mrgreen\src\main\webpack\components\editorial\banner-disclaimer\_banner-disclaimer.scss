.banner-disclaimer-component{
  display:block;
  margin:0;
  position: relative;
  z-index: 119;

  &.ctaVariation {
    z-index: 0;

     .disclaimer-container {
        color: $black;
        font-family: $font-family;

        &.disclaimer-main-container {
          padding: 1rem;
          width: 100%;

          @media (max-width: 1279px) {
            padding: 0.2rem;
          }

          @media (max-width: $md) {
            padding: 0;
            margin-top: 0.5rem;
            margin-bottom: 0;
          }
        }

       .disclaimer-content {
           text-align: justify;

             p {
                font-size: 1.1rem;
                line-height: 16.5px;
                font-weight: $font-weight-semi-bold;

                 @media (max-width: 1279px) {
                    font-size: .95rem;
                    font-weight: $font-weight-normal;
                    line-height: 1.4;
                 }

                 @media (max-width: 767px) {
                  color: $white;
                 }
             }
       }

     }

    .cta-container {
      display: flex;
      justify-content: center;
      padding: 0.5rem 0rem;
      margin: 4rem 0;

      @media (max-width: 768px){
        margin: 0;
      }

      .cta-template {
          max-width: 30rem;
          min-width: 16rem;
          width: auto;

          &.cta-primary {
            a:hover {
              span {
                color: $green-7;
              }
            }
          }

          &.cta-secondary {
            a:hover {
              span {
                color: $white;
              }
            }
          }
          &.cta-primary-glow {
            a:hover {
              span {
                color: $green-7;
              }
            }
          }

          a {
            padding: 1.3rem 7rem;

            @media (max-width: 767px) {
              padding: 1.3rem 0;
            }
          }
      }
    }

    &.disableSticky-tablet {
        @media (max-width: 768px){
            background-color: $dark-grey !important;
        }
    }

    .disclaimer-orbit-aling {
        padding: 0;
        margin-left: 11rem;
    }
  }

  &.ctaVariationSticky {

    .disclaimer-container {
      .disclaimer-content {
        p {
          margin: 0 2rem;
        }
      }
    }

    z-index: 119;
    background-color: $black;

    .cta-container {
        padding: 1.8rem;
        margin: 0;
        width: 100%;

        .cta-template {
            @media (max-width: 768px){
                max-width: 100%;
                width: 100%;
            }
        }
    }
  }

  &.fixed-banner-disclaimer {
      position: fixed;
      bottom: 0px;
      right: 0;
      width: 100%;
  }

  &.no-display-banner-disclaimer {
      display: none;
  }

  &.promoStyle {
    background-color: $black;


    @media (max-width: $md) {
       padding:0.8rem 0;
    }

   .disclaimer-container{
      color: white;
       .disclaimer-content{
           padding: 0.8rem;
           @media (max-width: $md) {
               padding: 0;
           }

           @media (max-width: $lg) {
               margin: 0 0;
           }

           p {
               font-size:$font-size-5;
               line-height: $line-height-3;

               @media (max-width: $md) {
                   margin: 0 1rem;
                   font-size: 9.75px !important;
               }

               @media (max-width: $sm) {
                font-size: 7.6px !important;
            }
           }

       } //p

    }//disclaimer-container

    .disclaimer-main-container {
        width: 100%;
    } // disclaimer-main-container

    .disclaimer-orbit-aling {
        padding: 0;
        margin-left: 11rem;

        .disclaimer-content {
            margin: 0 20rem;
            padding: .8rem 2.56rem;

            p {
                margin: 0 2rem;
            }
        }
    }
  }//promoStyle

  &.hpStyle {
     background-color:$black;

     .disclaimer-container {
          color:$white;
          text-align:center;
           .disclaimer-content {
                 p {
                    font-size: $font-size-5;
                    padding: 0.5rem 1rem
                 }//p
           }
      }//disclaimer-container
  }//hpStyle

  &.disclaimerInnerPromotionPage {
    z-index: 0;

    .disclaimer-container {
        color: #888;
        text-align: justify;
        margin-top: 3rem;

         .disclaimer-content {

            p {
                font-size: 1.1rem;
                margin-bottom: 1.5rem;
            }
         }
    }

    .disclaimer-main-container {
        padding: 0 0 0 4rem;
        width: 83%;

        @media (max-width: 1700px) {
            padding: 0 2rem;
            width: 100%;
        }
    } // disclaimer-main-container

    .disclaimer-container-title {
        width: 100%;
        margin-top: 0;
        padding: 0 0 1.5rem 0;
        border-top: 1px solid #353535;

        .disclaimer-content {

            p {
                font-size: 0.95rem;
            }
         }
    }

    .disclaimer-container-title::before {
        content: "";
        display: inline-block;
        width: 100%;
        border-top: 1px solid #000;
        transform: translateY(-1.2rem);
    }

  }
}//banner-disclaimer-component

.banner-disclaimer-none {
    display: none;
}
