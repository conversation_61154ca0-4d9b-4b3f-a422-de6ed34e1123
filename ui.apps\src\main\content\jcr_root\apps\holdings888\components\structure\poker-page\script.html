<template data-sly-template.head="${ @ pageModel}">
  <!-- Data Layer -->
  <script>
    let modelDataLayer = '${pageModel.dataLayer @ context="unsafe"}',
      modelFunctionKeys = '${pageModel.functionKeys @ context="unsafe"}',
      dataLayerJson = {};

    if (modelDataLayer !== "") {
      let functionKeys = modelFunctionKeys.split(","),
        jsFunction = "";

      dataLayerJson = JSON.parse(modelDataLayer);

      for (let key of functionKeys) {
        jsFunction = decodeURIComponent(dataLayer<PERSON>son[key]);
        try {
          dataLayerJson[key] = eval(jsFunction);
        } catch (e) {
          dataLayerJson[key] = null;
          console.log(
            "WARNING: Error in Data Layer function [" + key + "]: " + e.message
          );
        }
      }
    }

    window.dataLayer = window.dataLayer || [];
    dataLayer.push(dataLayerJson);
  </script>

  <sly data-sly-test="${pageModel.gtmID}">
    <!--Google Tag Manager-->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.setAttribute("class", "optanon-category-C0001");
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(
        window,
        document,
        "script",
        "dataLayer",
        '${pageModel.gtmID @ context="unsafe"}'
      );
    </script>
  </sly>

  <sly data-sly-test="${pageModel.js}">
    <!--Custom JS-->
    <script>
      ${pageModel.js @ context="unsafe"}
    </script>
  </sly>

  <sly data-sly-test="${pageModel.css}">
    <!--Custom CSS-->
    <script>
      let style = document.createElement("style");
      style.setAttribute("type", "text/css");
      document.head.appendChild(style);
      style.appendChild(
        document.createTextNode('${pageModel.css @ context="unsafe"}')
      );
    </script>
  </sly>
</template>

<template data-sly-template.body="${ @ pageModel}">
  <sly data-sly-test="${pageModel.gtmID}">
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=${pageModel.gtmID}"
        height="0"
        width="0"
        style="display: none; visibility: hidden">
      </iframe>
    </noscript>
  </sly>

  <sly data-sly-test="${pageModel.jsonLd}">
    <!--JSON LD Schema-->
    <script>
      let modelJsonLd = '${pageModel.jsonLd @ context="unsafe"}';
      try {
        let jsonLdJSON = JSON.parse(modelJsonLd);
        let jsonLdScript = document.createElement("script");
        jsonLdScript.setAttribute("type", "application/ld+json");
        jsonLdScript.innerHTML = modelJsonLd;
        document.head.appendChild(jsonLdScript);
      } catch (e) {
        console.log("WARNING: Error in JSON LD Schema: " + e.message);
      }
    </script>
  </sly>

  <sly data-sly-test="${pageModel.articleSchema}">
    <!--Article JSON LD Schema-->
    <script>
      let articleSchema = '${pageModel.articleSchema @ context="unsafe"}';
      try {
        let jsonLdJSON = JSON.parse(articleSchema);
        let jsonLdScript = document.createElement("script");
        jsonLdScript.setAttribute("type", "application/ld+json");
        jsonLdScript.innerHTML = articleSchema;
        document.head.appendChild(jsonLdScript);
      } catch (e) {
        console.log("WARNING: Error in JSON LD Schema: " + e.message);
      }
    </script>
  </sly>

    
    <sly data-sly-test="${pageModel.blogArticleCustomSchemaLD}">
        <!--blog Article Custom JSON LD Schema-->
        <script>
            let blogArticleCustomSchemaLD = '${pageModel.blogArticleCustomSchemaLD @ context="unsafe"}';
            try {
                let jsonLdJSON = JSON.parse(blogArticleCustomSchemaLD);
                let jsonLdScript = document.createElement('script');
                jsonLdScript.setAttribute('type', 'application/ld+json');
                jsonLdScript.innerHTML = blogArticleCustomSchemaLD;
                document.head.appendChild(jsonLdScript);
            } catch (e) {
                console.log('WARNING: Error in blog Article Custom JSON LD Schema: ' + e.message);
            }
        </script>
    </sly>

    <sly data-sly-test="${pageModel.js}">
        <!--Custom JS-->
        <script>
          ${pageModel.js @ context="unsafe"}
        </script>
      </sly>
    
      <sly data-sly-test="${pageModel.css}">
        <!--Custom CSS-->
        <script>
          let style = document.createElement("style");
          style.setAttribute("type", "text/css");
          document.head.appendChild(style);
          style.appendChild(
            document.createTextNode('${pageModel.css @ context="unsafe"}')
          );
        </script>
      </sly>

  <!-- Adobe Data Layer -->
  <script data-sly-test.dataLayerEnabled="${pageModel.page.data}">
    window.adobeDataLayer = window.adobeDataLayer || [];
    adobeDataLayer.push({
      page: JSON.parse("${pageModel.page.data.json @ context='scriptString'}"),
      event: "cmp:show",
      eventInfo: {
        path: 'page.${pageModel.page.id @ context="scriptString"}',
      },
    });
  </script>
</template>

<template data-sly-template.body="${ @ pageModel}">
    <!--Custom JS for body-->
    <script>
       
    </script>
