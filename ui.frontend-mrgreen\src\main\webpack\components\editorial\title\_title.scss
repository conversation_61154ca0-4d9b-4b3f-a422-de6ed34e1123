.title-component {
  word-break: break-word;

  .title-default-color {
    color: $black;
  }

  .title-white-color{
    color: $white;
  }

  .title-dark-green{
    color: $green-7;
  }
  .title-green-color{
    color: $green-3;
  }


  .default-font{
    font-family: $font-family;
  }
  .title-font-variant-2{
    font-family: $font-family-2;
  }
  .title-font-variant-3{
    font-family: $font-family-3;
  }
  .title-font-variant-4{
    font-family: $font-family-4;
  }
  .title-font-variant-5{
    font-family: $font-family-5;
  }
  .title-font-variant-6{
    font-family: $font-family-6;
  }
  .title-font-variant-7{
    font-family: $font-family-7;
  }
  .title-font-variant-8{
    font-family: $font-family-8;
  }
  .title-font-variant-9{
    font-family: $font-family-9;
  }

  .title-underline {
    text-decoration: underline;
  }

  .no-underline {
    text-decoration: none;
  }

  .fs-23{
    font-size: 2.3rem;
  }
  .fs-18{
    font-size: 1.8rem;
  }
  .fs-16{
    font-size: 1.6rem;
  }
  .fs-15{
    font-size: 1.5rem;
  }
  .fs-24{
    font-size: 2.45rem;
  }
  .fs-27 {
    font-size: 2.7rem;
  }
  .fs-30{
    font-size: 3.0rem;
    @media only screen and (max-width: 460px){
      font-size: 1.4rem;
    }
  }
  .fs-32 {
    font-size: 3.2rem;
    @media only screen and (max-width: 840px) {
      font-size: 2.3rem;
    }
  }
  .fs-35{
    font-size: 3.5rem;
  }
}
