<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        jcr:primaryType="nt:unstructured"
        jcr:title="Promotion Dialog"
        sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
        cq:showOnCreate="{Boolean}false">
        <items jcr:primaryType="nt:unstructured">
                <column
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                                <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items
                                                jcr:primaryType="nt:unstructured">
                                                <category
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                        emptyOption="{Boolean}true"
                                                        ordered="{Boolean}true"
                                                        fieldLabel="Category"
                                                        name="./category"
                                                        fieldDescription="Teaser to be displayd in Carousel in the Promotion lobby. Mandatory Data for teaser configuration : Category, Teaser Order In Carousel, Top Title, Teaser Image, Bottom Text, Top Cta Title, Top CTA url">
                                                        <datasource
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="/apps/getCasinoCategories" />
                                                </category>
                                                <audiences
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                        fieldDescription="Select the Audiences"
                                                        composite="{Boolean}true"
                                                        fieldLabel="Audiences">
                                                        <field
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                                name="./audiences">
                                                                <items
                                                                        jcr:primaryType="nt:unstructured">
                                                                        <audience
                                                                                jcr:primaryType="nt:unstructured"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                                emptyOption="{Boolean}true"
                                                                                ordered="{Boolean}true"
                                                                                fieldLabel="Audience"
                                                                                name="./audience"
                                                                                fieldDescription="It will display the teaser in the correct XF linked to the selected audiences.">
                                                                                <datasource
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="/apps/getCasinoAudiences" />
                                                                        </audience>
                                                                        <orderXF
                                                                                jcr:primaryType="nt:unstructured"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                                                                fieldDescription="Determines the render order of the teaser in the XF carousel."
                                                                                fieldLabel="Teaser Order In Carousel XF"
                                                                                name="./orderXf"
                                                                                renderReadOnly="{Boolean}true">
                                                                                <granite:data
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        allowBulkEdit="{Boolean}true"
                                                                                        cq-msm-lockable="order" />
                                                                        </orderXF>
                                                                </items>
                                                        </field>
                                                </audiences>
                                                <explanation
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                        level="{Long}4"
                                                        text="Dates usage: To activate the teaser - set any valid dates range for now.
                                                        To de-activate the teaser - set both dates in the past.
                                                        If you want to schedule the teaser activation - set the future dates range.
                                                        NOTE: DONT change teaser's category without de-activating it with previous category! " />
                                                <startDate
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                                        displayTimezoneMessage="{Boolean}true"
                                                        displayedFormat="DD-MM-YYYY HH:mm Z"
                                                        valueformat="DD/MM/YYYY HH:mm Z"
                                                        fieldLabel="Start Date"
                                                        name="./startDate"
                                                        type="datetime"
                                                        validation="dates.notchanged"
                                                        granite:id="startDateExportToTarget">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="startDate" />
                                                </startDate>
                                                <endDate
                                                        cq:showOnCreate="{Boolean}true"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                                        displayTimezoneMessage="{Boolean}true"
                                                        displayedFormat="DD-MM-YYYY HH:mm Z"
                                                        valueformat="DD/MM/YYYY HH:mm Z"
                                                        fieldLabel="End Date"
                                                        name="./endDate"
                                                        type="datetime"
                                                        validation="dates.notchanged"
                                                        granite:id="endDateExportToTarget">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="endDate" />
                                                </endDate>
                                                <exportToTargetWrapper
                                                        jcr:primaryType="nt:unstructured"
                                                        granite:id="exportToTargetWrapper"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items
                                                                jcr:primaryType="nt:unstructured">
                                                                <exportToTarget
                                                                        granite:id="exportToTargetScheduled"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/anchorbutton"
                                                                        text="Export to Target scheduled"
                                                                        variant="primary"
                                                                        size="L"
                                                                        icon="arrowUp"
                                                                        x-cq-linkchecker="skip"/>
                                                        </items>
                                                </exportToTargetWrapper>
                                                <mboxId
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        allowBulkEdit="{Boolean}true"
                                                        fieldDescription="Defines the teaser's Mbox-Id."
                                                        fieldLabel="Mbox-Id"
                                                        name="./mboxId"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="mboxId" />
                                                </mboxId>
                                        </items>
                                </column>
                                <top
                                        jcr:primaryType="nt:unstructured"
                                        jcr:title="Top Title"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                        <items jcr:primaryType="nt:unstructured">
                                                <topTitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldDescription="Defines the teasers top unlinked text."
                                                        fieldLabel="Top Title"
                                                        name="./topTitle"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                cq-msm-lockable="topTitle" />
                                                </topTitle>
                                                <topLinkText
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        allowBulkEdit="{Boolean}true"
                                                        fieldDescription="Defines the teasers top linked text."
                                                        fieldLabel="Top Link Text"
                                                        name="./topLinkText"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="topLinkText" />
                                                </topLinkText>
                                                <topLink
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldDescription="Top link's URL"
                                                        fieldLabel="Top link url"
                                                        name="./topUrl"
                                                        nodeTypes="cq:Page"
                                                        rootPath="/content/888casino">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="topUrl" />
                                                </topLink>
                                        </items>
                                </top>
                                <Image jcr:primaryType="nt:unstructured"
                                        jcr:title="Teaser Image"
                                       granite:class="cmp-image__editor-image-with-alt"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                        <items jcr:primaryType="nt:unstructured">
                                                <teaserImage
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        fieldLabel="Teaser Image"
                                                        fieldDescription="Defines the image displayed in the teaser on the lobby carousel."
                                                        rootPath="/content/dam/mrgreen"
                                                        mimeTypes="[image/gif,image/jpeg,image/webp,image/png,image/tiff,image/svg+xml]"
                                                        name="./teaserImageField"
                                                        fileNameParameter="./teaserImageFileName"
                                                        fileReferenceParameter="./teaserImage"
                                                        multiple="{Boolean}false"
                                                        useHTML5="{Boolean}true"
                                                        allowUpload="{Boolean}false" />
                                                <teaserImageAltText
                                                        jcr:primaryType="nt:unstructured"
                                                        granite:class="cmp-image__editor-alt-text"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        allowBulkEdit="{Boolean}true"
                                                        fieldDescription="Text will be loaded when asset is not found."
                                                        fieldLabel="Teaser Image Alt Text"
                                                        name="./teaserImageAltText"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="teaserImageAltText" />
                                                </teaserImageAltText>
                                        </items>
                                </Image>
                                <bottomTextAndCta
                                        jcr:primaryType="nt:unstructured"
                                        jcr:title="Teaser Bottom Section"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                        <items jcr:primaryType="nt:unstructured">
                                                <bottomText
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="acs-commons/granite/ui/components/include"
                                                        path="holdings888/components/common888/dialog-include/rich-text-for-page-properties"
                                                        fieldDescription="Defines the bottom unlinked text."
                                                        fieldLabel="Bottom Text"
                                                        
                                                        renderReadOnly="{Boolean}true">
                                                        <parameters
                                                                jcr:primaryType="nt:unstructured"
                                                                textName="bottomText"
                                                                textLabel="Botto Text"
                                                                isRequired="{Boolean}false"/>
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                cq-msm-lockable="bottomText" />
                                                </bottomText>
                                                <bottomTextSubtitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldDescription="Defines the bottom unlinked text."
                                                        fieldLabel="Bottom Text Subtitle"
                                                        name="./bottomTextSubtitle"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                cq-msm-lockable="bottomTextSubtitle" />
                                                </bottomTextSubtitle>
                                                <topCTATitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldDescription="Top CTA displayed text."
                                                        fieldLabel="Top Cta Title"
                                                        name="./topCTATitle"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                cq-msm-lockable="topCTATitle" />
                                                </topCTATitle>
                                                <topCTALink
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldDescription="Top Cta URL."
                                                        fieldLabel="Top CTA url"
                                                        name="./topCTALink"
                                                        renderReadOnly="{Boolean}true"
                                                        nodeTypes="cq:Page"
                                                        rootPath="/content/mrgreen">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="topCTALink" />
                                                </topCTALink>
                                                <topCTAScript
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="acs-commons/granite/ui/components/include"
                                                        path="/apps/holdings888/components/common888/dialog-include/cta-script">
                                                        <parameters
                                                                jcr:primaryType="nt:unstructured"
                                                                ctaScriptName="topCTAScript"
                                                                ctaScriptLabel="Top CTA Script"
                                                                generateScriptName="topCTAgenerateScript"/>
                                                </topCTAScript>
                                                <bottomCTATitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldDescription="Bottom Cta text (Optional) - Based on this field the Bottom Cta will be rendered."
                                                        fieldLabel="Bottom Cta Title"
                                                        name="./bottomCTATitle"
                                                        renderReadOnly="{Boolean}true">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                cq-msm-lockable="bottomCTATitle" />
                                                </bottomCTATitle>
                                                <bottomCTALink
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldDescription="Bottom Cta URL."
                                                        fieldLabel="Bottom CTA url"
                                                        name="./bottomCTALink"
                                                        renderReadOnly="{Boolean}true"
                                                        nodeTypes="cq:Page"
                                                        rootPath="/content/mrgreen">
                                                        <granite:data
                                                                jcr:primaryType="nt:unstructured"
                                                                allowBulkEdit="{Boolean}true"
                                                                cq-msm-lockable="bottomCTALink" />
                                                </bottomCTALink>
                                                <bottomCTAScript
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="acs-commons/granite/ui/components/include"
                                                        path="/apps/holdings888/components/common888/dialog-include/cta-script">
                                                        <parameters
                                                                jcr:primaryType="nt:unstructured"
                                                                ctaScriptName="bottomCTAScript"
                                                                ctaScriptLabel="Bottom CTA Script"
                                                                generateScriptName="bottomCTAgenerateScript"/>
                                                </bottomCTAScript>
                                                <column
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                        <items jcr:primaryType="nt:unstructured">
                                                                <additionalTextOptions
                                                                        jcr:primaryType="nt:unstructured"
                                                                        granite:class="cq-dialog-dropdown-showhide"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                        name="./additionalTextOptions">
                                                                        <items
                                                                                jcr:primaryType="nt:unstructured">
                                                                                <noAdditionalText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        text="No Additional Text"
                                                                                        selected="{Boolean}true"
                                                                                        value="NoAdditionalText" />
                                                                                <TextBefore
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        text="Additional Text Before CTA"
                                                                                        value="AdditionalTextBeforeLink" />
                                                                                <TextAfter
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        text="Additional Text After Link"
                                                                                        value="AdditionalTextAfterLink" />
                                                                                <RichTextConfiguration
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        text="Free Rich Text"
                                                                                        value="FreeRichText" />
                                                                        </items>
                                                                        <granite:data
                                                                                jcr:primaryType="nt:unstructured"
                                                                                cq-dialog-dropdown-showhide-target=".additionalTextConfiguration" />
                                                                </additionalTextOptions>
                                                                <AdditionalTextBeforeLink
                                                                        granite:class="additionalTextConfiguration"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                                                        <items
                                                                                jcr:primaryType="nt:unstructured">
                                                                                <linkText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                        fieldDescription="Bottom link text"
                                                                                        fieldLabel="Link Text"
                                                                                        name="./bottomLinkTextBefore"
                                                                                        renderReadOnly="{Boolean}true">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="bottomLinkTextBefore" />
                                                                                </linkText>
                                                                                <linkUrl
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                                                        fieldDescription="Additional Text - Link URL."
                                                                                        fieldLabel="Link URL"
                                                                                        name="./disclaimerLinkBefore"
                                                                                        renderReadOnly="{Boolean}true"
                                                                                        nodeTypes="cq:Page"
                                                                                        rootPath="/content/mrgreen">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="disclaimerLinkBefore" />
                                                                                </linkUrl>
                                                                                <extraText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                        fieldDescription="Bottom link text"
                                                                                        fieldLabel="Extra Text"
                                                                                        name="./extraTextBefore"
                                                                                        renderReadOnly="{Boolean}true">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="extraTextBefore" />
                                                                                </extraText>
                                                                        </items>
                                                                        <granite:data
                                                                                jcr:primaryType="nt:unstructured"
                                                                                showhidetargetvalue="AdditionalTextBeforeLink" />
                                                                </AdditionalTextBeforeLink>
                                                                <AdditionalTextAfterLink
                                                                        granite:class="additionalTextConfiguration"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                                                        <items
                                                                                jcr:primaryType="nt:unstructured">
                                                                                <linkText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                        fieldDescription="Bottom link text"
                                                                                        fieldLabel="Link Text"
                                                                                        name="./bottomLinkTextAfter"
                                                                                        renderReadOnly="{Boolean}true">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="bottomLinkTextAfter" />
                                                                                </linkText>
                                                                                <linkUrl
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                                                        fieldDescription="Additional Text - Link URL."
                                                                                        fieldLabel="Link URL"
                                                                                        name="./disclaimerLinkAfter"
                                                                                        renderReadOnly="{Boolean}true"
                                                                                        nodeTypes="cq:Page"
                                                                                        rootPath="/content/mrgreen">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="disclaimerLinkAfter" />
                                                                                </linkUrl>
                                                                                <extraText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                        fieldDescription="Bottom link text"
                                                                                        fieldLabel="Extra Text"
                                                                                        name="./extraTextAfter"
                                                                                        renderReadOnly="{Boolean}true">
                                                                                        <granite:data
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                allowBulkEdit="{Boolean}true"
                                                                                                cq-msm-lockable="extraTextAfter" />
                                                                                </extraText>
                                                                        </items>
                                                                        <granite:data
                                                                                jcr:primaryType="nt:unstructured"
                                                                                showhidetargetvalue="AdditionalTextAfterLink" />
                                                                </AdditionalTextAfterLink>
                                                                <FreeRichText
                                                                        granite:class="additionalTextConfiguration"
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                                                        <items
                                                                                jcr:primaryType="nt:unstructured">
                                                                                <teaserDisclaimerText
                                                                                        jcr:primaryType="nt:unstructured"
                                                                                        sling:resourceType="acs-commons/granite/ui/components/include"
                                                                                        path="holdings888/components/common888/dialog-include/rich-text-for-page-properties">
                                                                                </teaserDisclaimerText>
                                                                        </items>
                                                                        <granite:data
                                                                                jcr:primaryType="nt:unstructured"
                                                                                showhidetargetvalue="FreeRichText" />
                                                                </FreeRichText>
                                                        </items>
                                                </column>
                                        </items>
                                </bottomTextAndCta>
                        </items>
                </column>
        </items>
</jcr:root>
