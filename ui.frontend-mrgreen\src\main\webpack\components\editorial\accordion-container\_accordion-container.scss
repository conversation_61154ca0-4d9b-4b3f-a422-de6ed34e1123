.accordion-container-cmp {

  .accordion-tab {
    width: 100%;
    background: $dark-grey;
    border-radius: 1.2rem;
    padding: 2rem;
    margin-bottom: 0.9rem;
    cursor: pointer;
    border: none;

    &.active {
      .accordion-tab-head {
        .accordion-icon::before {
          content: "-";
        }
      }
    }

    .accordion-tab-head{

      .accordion-title{
        text-align: left;
        margin-right: auto;
      }

      .accordion-title, .accordion-icon{
        font-size: 1.6rem;
        color: $white;
        font-weight: $font-weight-light;
        line-height: $line-height;
      }
      .accordion-icon::before {
        content: "+";
      }
    }
  }
  .accordion-panel {
    display: none;
    overflow: hidden;
    &.open{
      background-color: #1c1c1c;
      border-radius: 1.2rem;
      margin: -3.2rem 0 1.2rem;
      padding: 3.2rem 2rem 2rem;
    }
    &.regular-p{
      p {
        font-size: $font-size-16;
        line-height: $line-height;
        font-weight: $font-weight-regular;
        color: $white;
      }

      ul li,
      ol li {
        font-weight: $font-weight-regular;
      }
    }
    p {
      font-size: $font-size-3;
      line-height: $line-height-2;
      font-weight: $font-weight-semi-bold;
      color: $white;
    }

    ul li {
      color: $white;
      font-size: $font-size-3;
      line-height: $line-height-2;
      font-weight: $font-weight-semi-bold;

      &::before {
        color: $white;
      }
    }

    ol li {
      color: $white;
      font-size: $font-size-3;
      line-height: $line-height-2;
      font-weight: $font-weight-semi-bold;

      &::marker {
        color: $white;
      }
    }
  }
}
