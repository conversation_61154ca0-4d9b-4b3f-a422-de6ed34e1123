<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Claim Poker Configuration"
    extraClientlibs="[holdings888.richtext,rte.dialog.rich-text,core.wcm.components.contentfragment.v1.dialog,888poker.components.author.editor,holdings888.dialog.SAGenerator]"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <bannerTab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Banner Image"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <bannerSuccessImage
                                    jcr:primaryType="nt:unstructured"
                                    path="/apps/holdings888/components/editorial/claim-poker/dialog-include/double-image/"
                                    sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        fieldsetTitle="Banner Success Image"
                                        imagePrefixName="bannerSuccessImage"
                                />
                            </bannerSuccessImage>
                            <bannerErrorImage
                                    jcr:primaryType="nt:unstructured"
                                    path="/apps/holdings888/components/editorial/claim-poker/dialog-include/double-image/"
                                    sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        fieldsetTitle="Banner Error Image"
                                        imagePrefixName="bannerErrorImage"
                                />
                            </bannerErrorImage>
                        </items>
                    </bannerTab>
                    <offerTab
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Offer content"
                            sling:resourceType="granite/ui/components/coral/foundation/container"
                            margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <upperSentenceText
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset"
                                    jcr:title="Upper Sentence Text">
                                    <items jcr:primaryType="nt:unstructured">
                                        <successText
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                            <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    textName="upSuccessText"
                                                    textLabel="Success Text"/>
                                        </successText>
                                        <errorText
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                            <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    textName="upErrorText"
                                                    textLabel="Error Text"/>
                                        </errorText>
                                        <usedText
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                            <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    textName="upUsedText"
                                                    textLabel="Used Text"/>
                                        </usedText>
                                        <expiredText
                                                jcr:primaryType="nt:unstructured"
                                                path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                                sling:resourceType="acs-commons/granite/ui/components/include">
                                            <parameters
                                                    jcr:primaryType="nt:unstructured"
                                                    textName="upExpiredText"
                                                    textLabel="Expired Text"/>
                                        </expiredText>
                                    </items>
                            </upperSentenceText>
                            <offerText
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset"
                                    jcr:title="OfferText">
                                <items jcr:primaryType="nt:unstructured">
                                    <successText
                                            jcr:primaryType="nt:unstructured"
                                            path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                textName="successText"
                                                textLabel="Success Text"/>
                                    </successText>
                                    <usedText
                                            jcr:primaryType="nt:unstructured"
                                            path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                textName="usedText"
                                                textLabel="Used Text"/>
                                    </usedText>
                                    <errorText
                                            jcr:primaryType="nt:unstructured"
                                            path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                textName="errorText"
                                                textLabel="Error Text"/>
                                    </errorText>
                                    <expiredText
                                            jcr:primaryType="nt:unstructured"
                                            path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                textName="expiredText"
                                                textLabel="Expired Text"/>
                                    </expiredText>
                                </items>
                            </offerText>
														<ctaTab
															jcr:primaryType="nt:unstructured"
															jcr:title="CTA"
															sling:resourceType="granite/ui/components/coral/foundation/container"
															margin="{Boolean}true">
															<items jcr:primaryType="nt:unstructured">
																<headingType
																	jcr:primaryType="nt:unstructured"
																	sling:resourceType="granite/ui/components/coral/foundation/heading"
																	level="{Long}3"
																	text="CTA" />
																<label
																	jcr:primaryType="nt:unstructured"
																	sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
																	fieldLabel="CTA Label"
																	jcr:description="Insert the label of the CTA"
																	name="./CTAlabel" />
																<ariaLabel
																	jcr:primaryType="nt:unstructured"
																	sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
																	fieldLabel="ARIA Label (Accessibility)"
																	fieldDescription="This attribute is used to provide additional information to help clarify or further describe the purpose of a link. Can also be useful to people with disabilities."
																	jcr:description="Insert the Aria Label of the CTA (Accessibility)"
																	name="./CTAariaLabel" />
																<url
																	jcr:primaryType="nt:unstructured"
																	sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
																	fieldLabel="CTA url"
																	jcr:description="Insert the url of the CTA"
																	rootPath="/content"
																	name="./CTAurl" />
																<ctaCasScript
																	jcr:description="The JS Script that will be execute on click"
																	jcr:primaryType="nt:unstructured"
																	sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
																	fieldLabel="Custom Script"
																	name="./ctaScript"
																	resize="vertical" />
															</items>
														</ctaTab>
                        </items>
                    </offerTab>
                    <casinoTab
                            jcr:primaryType="nt:unstructured"
													sling:resourceType="granite/ui/components/coral/foundation/include"
													path="/apps/holdings888/components/editorial/claim-poker/dialog-include/casino-tab/" />
										<sportsTab
													jcr:primaryType="nt:unstructured"
													sling:resourceType="granite/ui/components/coral/foundation/include"
													path="/apps/holdings888/components/editorial/claim-poker/dialog-include/sports-tab/" />
													 <miscTab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Misc"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <validationDomain
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Validation domain"
                                jcr:description="If it is empty, relative path /claim/random/ will be used."
                                name="./validationDomain" />
                        </items>
                    </miscTab>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
