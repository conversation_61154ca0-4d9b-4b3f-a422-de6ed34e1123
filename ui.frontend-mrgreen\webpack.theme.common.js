'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TSConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const resolve = {
    extensions: ['.js', '.ts'],
    plugins: [
        new TSConfigPathsPlugin({
            configFile: './tsconfig.json',
        }),
    ],
};

module.exports = {
    context: path.resolve(__dirname, './src/main/webpack'),
    resolve: resolve,
    entry: {
        mrgreen: './mrgreen/main.ts',
        'mrgreen-top-section': './mrgreen/main-top-section.ts',
        'mrgreen-back-to-top': './components/fragment/back-to-top/back-to-top.ts',
        'mrgreen-breadcrumb': './components/fragment/breadcrumb/breadcrumb.ts',
        'mrgreen-seo-content-container': './components/fragment/seo-content-container/seo-content-container.ts',
        'mrgreen-promotions-top-games-carousel': './components/fragment/promotions-top-games-carousel/promotions-top-games-carousel.ts',
        'mrgreen-footer-v2': './components/fragment/footer-v2/footer-v2.ts',
        'mrgreen-related-articles-card-slider': './components/fragment/related-articles-card-slider/related-articles-card-slider.ts',
        'mrgreen-article-slider': './components/fragment/article-slider/article-slider.ts',
        'mrgreen-header-bar-light-lp': './components/fragment/header-bar-light-lp/header-bar-light-lp.ts',
        'mrgreen-sidebar': './components/fragment/sidebar/sidebar.ts',
        'mrgreen-promotion-teaser': './components/fragment/promotion-teaser/promotion-teaser.ts',
        'mrgreen-sidenav': './components/fragment/sidenav/sidenav.ts',
    },
    optimization: {
        minimize: false,
        splitChunks: {
            chunks: 'all', // The option could be set to 'async', so all vendors libs will be included in EACH component
            // file, but it's better to have them in separate files
            cacheGroups: {
                // "This chunk contains all the external libraries used in the project"
                defaultVendors: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'mrgreen-vendors', // The chunk should be the same as location name in HtmlPageItemsConfig
                    chunks: 'all',
                },
            },
        },
    },
    output: {
        filename: 'aem-static/js/[name].js',
        path: path.resolve(__dirname, 'dist'),
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'ts-loader',
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve,
                        },
                    },
                ],
            },
            {
                test: /\.scss$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            url: false,
                        },
                    },
                    {
                        loader: 'sass-loader',
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve,
                        },
                    },
                ],
            },
            {
                test: /\.(ico|jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)(\?.*)?$/,
                use: {
                    loader: 'file-loader',
                    options: {
                        name: '[path][name].[ext]',
                    },
                },
            },
        ],
    },
    plugins: [
        new CleanWebpackPlugin(),
        new ESLintPlugin({
            extensions: ['js', 'ts', 'tsx'],
        }),
        new MiniCssExtractPlugin({
            filename: 'aem-static/css/[name].css',
        }),
        new CopyWebpackPlugin({
            patterns: [{ from: './resources', to: './aem-static/resources' }],
        }),
    ],
    stats: {
        assetsSort: 'chunks',
        builtAt: true,
        children: false,
        chunkGroups: true,
        chunkOrigins: true,
        colors: false,
        errors: true,
        errorDetails: true,
        env: true,
        modules: false,
        performance: true,
        providedExports: false,
        source: false,
        warnings: true,
    },
};
