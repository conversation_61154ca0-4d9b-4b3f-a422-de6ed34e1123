.promotions-carousel {
    .promotion-carousel-component {
        color: $white;
        box-sizing: inherit;
        font-size: 15px;
        padding: 2em 0;
        flex-direction: row;
        display: flex;
        transition: all 0.4s ease;
        @media only screen and (max-width: 768px) {
            padding: 1.5rem;
        }
        @media only screen and (min-width: 769px) and (max-width: 1024px) {
            padding: 2rem;
        }

        .promotion-carousel-container {
            max-width: 100%;
            height: 100%;
            width: 100%;
            .promotions-carousel-top {
                display: flex;
                justify-content: space-between;
                background: linear-gradient(90deg,rgba(252, 246, 213, .5),hsla(0deg,0%,100%,0%) 75%);
                margin: 0 0 2em;
                padding: 0 0 0.2em;
                width: 100%;
                clip-path: polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 1% 50%, 0% 0%);
                
                .promotions-carousel-title {
                    line-height: 1.5;
                    color: $white;
                    font-weight: $font-weight-semi-bold;
                    font-size: 1.5rem;
                    box-sizing: inherit;
                    display: flex;
                    align-items: center;
                    margin-left: 2rem;
                    h2 {
                        box-sizing: inherit;
                        font-style: normal;
                        line-height: 1.2em;
                        margin: 0;
                        padding: 0;
                        font-size: 2.1rem;
                        text-transform: capitalize;
                        color: #0ea79b;;
                        border: none;
                        background: transparent;
                        font-weight: $font-weight-medium;
                        display: block;
                        position: relative;
                        letter-spacing: .2rem;
                        font-family: Oleo-Script-Regular, sans-serif;
                    }

                    h2::before {
                        content: "";
                        display: inline-block;
                        border-top: 8px solid transparent;
                        border-left: 6px solid $primary-color;
                        border-bottom: 8px solid transparent;
                        margin-right: 1rem;
                        margin-left: 1rem;
                        transform: translateY(15%);
                        
                    }
                }

                .arrows {
                    position: relative;
                    top: 0.2em;
                    display: none;

                    @media (min-width: calc($sm-grid + 1px)) {
                        display: flex;
                    }
                    .arrow-right {
                        background: $primary-color;
                        width: 3.8rem;
                        height: 3.8rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 100%;
                        margin: 0 0.2rem;
                        border: 0.1rem solid $gray4;
                        cursor: pointer;
                        font-size: medium;
                    }
                    .arrow-left {
                        background: $primary-color;
                        width: 3.8rem;
                        height: 3.8rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 100%;
                        margin: 0 0.2rem;
                        border: 0.1rem solid $gray4;
                        cursor: pointer;
                        font-size: medium;
                    }
                }
            }

            .swiper {
                position: relative;
                max-width: 100%;
                box-sizing: content-box;
                overflow-x: hidden;
                height: 100%;
                .swiper-wrapper {
                    flex-direction: column;
                    transform: translateX(0);
                    display: flex;
                    width: 100%;
                    gap: 1rem;
                    transition: transform 0.5s ease;
                    @media (min-width: calc($sm-grid + 1px) ) {
                        flex-direction: row;
                    }
                }
            }
        }
    }
}
