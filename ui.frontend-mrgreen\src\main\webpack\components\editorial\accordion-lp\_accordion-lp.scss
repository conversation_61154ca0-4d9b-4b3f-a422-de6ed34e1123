.cmp-accordion-lp {
    &.accordion-lp-v0{
        width: 100%;
        @media (orientation: portrait) {
            max-width: 100%;
            padding: 3%;
        }
        .cmp-accordion-lp {
            &__wrapper {
                width: 100%;
                max-width: 70%;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                flex-wrap: nowrap;
                padding-bottom: 1%;
                position: relative;
                z-index: 99;
                @media screen and (max-width: $md-grid){
                    max-width: 100%;
                    padding: 0 1rem;
                }
            }
            &__tab {
                background: transparent;
                border: none;
                cursor: pointer;
                margin-bottom: 0.9rem;
                color: $white;
            }
            &__head {
                padding-right: 1.5rem;
            }
            &__title {
                margin: 3.8rem 0;
                text-align: left;
                font-weight: $font-weight-bold;
                @media (orientation: portrait) {
                    margin: 1.8rem 0;
                }
                @media (max-width: $md) {
                    margin: 1.8rem 0;
                }
                p{
                    margin-bottom: 0;
                }

                @media (orientation: landscape) and (max-width: 1380px) {
                    width: 94%;
                }

                @media (orientation: landscape) and (max-width: 1280px) {
                    width: 96%;
                }
                
                @media (max-width: $md) {
                    width: 96%;
                }
            }
            &__title > * {
                font-size: 2.4vw;
                position: relative;
                cursor: pointer;
                @media (max-width: $md) {
                    font-size: 3vw;
                    padding-right: 1rem;
                }

                @media (max-width: $sm-air) {
                    font-size: 5vw;
                    padding-right: 1rem;
                }

                
                @media (max-width: $sm) {
                    padding-right: 0.5rem;
                    font-size: 5.2vw;
                }
            }
            &__icon {
                &.collapse-icon::before {
                    background-image: var(--collapse-icon-url);
                }
                &.expand-icon::before {
                    background-image: var(--expand-icon-url);
                }
            }
            &__icon:before {
                content: " ";
                width: 3.5rem;
                height: 3.5rem;
                position: absolute;
                right: 1%;
                transform: translateY(-50%);
                background-size: 100%;
                background-repeat: no-repeat;
                transition: 0.3s all;

                @media (max-width: $md) {
                    width: 3rem;
                    height: 3rem;
                }
                @media (max-width: $sm) {
                    width: 8vw;
                    height: 8vw;
                }
            }

            &__panel {
                display: none;
                animation: growOut 300ms ease-in-out forwards;
                transform-origin: top center;
            }
        }

        @keyframes growOut {
            0% {
                transform: scale(0);
            }
            80% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
    }
    &.accordion-lp-v1{
        .cmp-accordion-lp {
            &__wrapper {
                width: 75%;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                flex-wrap: nowrap;
                position: relative;

                @media (max-width: $sm) {
                    width: 100%;
                    padding: 0 1rem;
                }
            }
            &__tab {
                background: transparent;
                border: none;
                cursor: pointer;
                margin-bottom: 4rem;
                color: $white;
            }
            &__head {
                padding: 1.8rem 0;

            }
            &__title {
                text-align: left;
                font-weight: $font-weight-regular;

                @media (orientation: landscape) and (max-width: 1380px) {
                    width: 90%;
                }
                
                @media (max-width: $md) {
                    width: 90%;
                }

                @media (max-width: $sm-air) {
                    width: 88%;
                }
            }
            &__title > * {
                font-size: 3rem;

                @media (max-width: $sm) {
                    font-size: 1.8rem;
                }
            }
            &__icon {
                &.collapse-icon::before {
                    transform: rotate(180deg);
                    background: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220.736%22%20height%3D%2212.856%22%20viewBox%3D%220%200%2020.736%2012.856%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Group_41279%22%20data-name%3D%22Group%2041279%22%20transform%3D%22translate(363.307%20-43.737)%20rotate(45)%22%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_37%22%20data-name%3D%22Rectangle%2037%22%20width%3D%223.519%22%20height%3D%2214.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-213.067%20274.921)%22%20fill%3D%22%23fff%22%3E%3C%2Frect%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_38%22%20data-name%3D%22Rectangle%2038%22%20width%3D%223.519%22%20height%3D%2214.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-209.548%20286.064)%20rotate(90)%22%20fill%3D%22%23fff%22%3E%3C%2Frect%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E');
                    background-color: $green-6;
                    background-repeat: no-repeat;
                    background-position: center center;

                    @media (max-width: $sm) {
                        background: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220.736%22%20height%3D%2212.856%22%20viewBox%3D%220%200%2020.736%2012.856%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Group_41279%22%20data-name%3D%22Group%2041279%22%20transform%3D%22translate(363.307%20-43.737)%20rotate(45)%22%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_37%22%20data-name%3D%22Rectangle%2037%22%20width%3D%223.519%22%20height%3D%2210.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-213.067%20277.921)%22%20fill%3D%22%23fff%22%2F%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_38%22%20data-name%3D%22Rectangle%2038%22%20width%3D%223.519%22%20height%3D%2210.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-210.548%20286.064)%20rotate(90)%22%20fill%3D%22%23fff%22%2F%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E');
                        background-color: $green-6;
                        background-repeat: no-repeat;
                        background-position: center center;
                    }
                }
            }
            &__icon:before {
                width: 1.4rem;
                height: 1.4rem;
                content: "";
                background: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220.736%22%20height%3D%2212.856%22%20viewBox%3D%220%200%2020.736%2012.856%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Group_41279%22%20data-name%3D%22Group%2041279%22%20transform%3D%22translate(363.307%20-43.737)%20rotate(45)%22%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_37%22%20data-name%3D%22Rectangle%2037%22%20width%3D%223.519%22%20height%3D%2214.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-213.067%20274.921)%22%20fill%3D%22%23015536%22%3E%3C%2Frect%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_38%22%20data-name%3D%22Rectangle%2038%22%20width%3D%223.519%22%20height%3D%2214.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-209.548%20286.064)%20rotate(90)%22%20fill%3D%22%23015536%22%3E%3C%2Frect%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E');
                background-repeat: no-repeat;
                background-color: $black;
                border: 0.2rem solid $green-6;
                padding: 1.5rem;
                border-radius: 100px;
                background-position: center center;
                transition: all .4s ease;
                position: absolute;
                right: 1.8rem;
                top: 1.6rem;

                @media (max-width: $sm) {
                    width: 0.2rem;
                    height: 0.2rem;
                    background: url('data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220.736%22%20height%3D%2212.856%22%20viewBox%3D%220%200%2020.736%2012.856%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Group_41279%22%20data-name%3D%22Group%2041279%22%20transform%3D%22translate(363.307%20-43.737)%20rotate(45)%22%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_37%22%20data-name%3D%22Rectangle%2037%22%20width%3D%223.519%22%20height%3D%2210.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-213.067%20277.921)%22%20fill%3D%22%23015536%22%2F%3E%0A%20%20%20%20%20%20%20%20%3Crect%20id%3D%22Rectangle_38%22%20data-name%3D%22Rectangle%2038%22%20width%3D%223.519%22%20height%3D%2210.662%22%20rx%3D%221.759%22%20transform%3D%22translate(-210.548%20286.064)%20rotate(90)%22%20fill%3D%22%23015536%22%2F%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E');
                    background-repeat: no-repeat;
                    background-color: $black;
                    border: 0.2rem solid $green-6;
                    background-position: center center;
                }
            }

            &__panel {
                display: none;
                margin: -2.5rem 0 1rem;
                ol li, ul li{
                    font-weight: $font-weight-light;
                    font-size: 1.4rem;
                    line-height: 1.6;
                }
                p {
                    font-size: 1.4rem;
                    line-height: 1.6;
                }
            }
        }
    }

    &.accordion-lp-v2 {
        .cmp-accordion-lp {
            &__wrapper {
                width: 100%;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                flex-wrap: nowrap;
                position: relative;
            }

            &__tab {
                background: transparent;
                border: none;
                cursor: pointer;
                margin-bottom: 1rem;
                color: $white;
                width: fit-content;
            }

            &__head {
                padding: 0 0 1.8rem;
            }

            &__title {
                text-align: left;
                font-weight: $font-weight-semi-bold;
                color: $black;
                text-decoration: underline $black;
            }

            &__title-expand {
                p::before {
                    content: "[+] ";
                    font-weight: $font-weight-semi-bold;
                    color: $black;
                    text-decoration: underline $black;
                }
            }

            &__title-collapse {
                p::before {
                    content: "[-] ";
                    font-weight: $font-weight-semi-bold;
                    color: $black;
                    text-decoration: underline $black;
                }
            }

            &__title > * {
                font-size: 1.3rem;
                font-weight: $font-weight-semi-bold;
            }

            &__panel {
                display: none;
                margin: -2.5rem 0 1rem;
            }
        }
    }

    &.accordion-lp-v3 {
        .cmp-accordion-lp__wrapper {
            .cmp-accordion-lp__tab {
                background: none;
                color: inherit;
                border: none;
                font: inherit;
                cursor: pointer;

                .cmp-accordion-lp__head {
                    .cmp-accordion-lp__title p {
                        font-size: 1.5rem;
                    }
                }
            }
        }
    }

    &.accordion-lp-v4 {
        color: $white;

        li::before {
            color: $white;
        }
        
        .cmp-accordion-lp__wrapper {
            background-color: $dark-2;
            padding: 1.8rem;
            border-radius: 12px;

            .cmp-accordion-lp__tab {
                background: none;
                color: $white;
                border: none;
                font: inherit;
                cursor: default;
                pointer-events: none;

                .cmp-accordion-lp__head {
                    .cmp-accordion-lp__title p {
                        font-size: 1.5rem;
                    }
                }
            }

            .cmp-accordion-lp__panel {
                .rich-text {
                    .rich-text-component {
                        .text {
                            p,
                            a,
                            span,
                            li,
                            li::before {
                                color: $white;
                            }
                        }
                    }
                }
            }
        }
    }

}
