<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-test.hasContent="${properties.cardTitle && properties.imageReferenceCard}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=true, classAppend='promotion-teaser'}"/>
<sly data-sly-use.disclaimer="${'holdings888.core.models.RichTextImp'}"/>
<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>

<sly data-sly-test="${hasContent}">
    <!-- Add Back To Top ClientLib -->
    <sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"
         data-sly-call="${clientlib.css @ categories='holdings888.casino-promotion-teaser'}"/>

    <sly data-sly-call="${clientlibs.fe @ locations='casino-promotion-teaser-css'}"/>
    <!-- End Clientlib -->

    <!-- Promotion Card  -->
    <div class="promotion-teaser-cmp" data-mbox-id="${properties.mboxId}">
        <div class="promotion-teaser-cmp__card">
            <div class="teaser-wrapper">
                <div class="teaser-inner">
                    <div class="teaser-inner__image">
                        <sly data-sly-use.imageCard="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageCardReference}"/>
                        <sly data-sly-set.imageCardPath="${imageCard.renditions['webp'].path || imageCard.renditions['original'].path}"/>
                        <img src="${imageCardPath}" alt="${properties.imageAltCard}" title="${properties.imageAltCard}" loading="lazy">
                    </div>
                    <div class="teaser-inner__text">
                        <h3>${properties.cardTitle}</h3>
                        <p>${properties.description @ context='html'}</p>
                    </div>
                    <div class="teaser-inner__buttons">
                        <sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-link-template.html"/>
                        <sly data-sly-use.ctaLink1="${'holdings888/components/common888/utils/childNode.js' @ nodeName='ctaLink1'}" />
                        <sly data-sly-use.url1="${'holdings888.core.models.LinkModel' @ urlToProcess=ctaLink1.properties.linkUrl }" />
                        <div class="cta-component d-flex justify-content-start" data-sly-test="${ctaLink1.properties.linkLabel}">
                            <sly data-sly-call="${cta.default @
                                label=ctaLink1.properties.linkLabel,
                                ctaSize='cta-default-size',
                                url=url1.relativePublishLink,
                                ctaType='cta-primary-v2',
                                ctaScript=ctaLink1.properties.script,
                                ctaAddsCut=url1.addsCut,
                                properties=ctaLink1.properties
                                }"/>
                        </div>

                        <sly data-sly-use.ctaLink2="${'holdings888/components/common888/utils/childNode.js' @ nodeName='ctaLink2'}" />
                        <sly data-sly-use.url2="${'holdings888.core.models.LinkModel' @ urlToProcess=ctaLink2.properties.linkUrl }" />
                        <div class="cta-component d-flex justify-content-start" data-sly-test="${ctaLink2.properties.linkLabel}">
                            <sly data-sly-call="${cta.default @
                                label=ctaLink2.properties.linkLabel,
                                ctaSize='cta-default-size',
                                url=url2.relativePublishLink,
                                ctaType='cta-secondary',
                                ctaScript=ctaLink2.properties.script,
                                ctaAddsCut=url2.addsCut,
                                properties=ctaLink2.properties
                                }"/>
                        </div>
                    </div>
                    <div class="teaser-inner__disclaimer">
                        ${disclaimer.text @ context='html'}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Promotion Card -->
</sly>
