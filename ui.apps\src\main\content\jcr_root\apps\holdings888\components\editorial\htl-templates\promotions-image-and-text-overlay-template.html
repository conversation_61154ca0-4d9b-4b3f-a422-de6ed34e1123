<template data-sly-template.manual="${@ properties, link, innerLinkHref}">
    <div class="promotions-image-and-text-overlay-component" data-mbox-id="${properties.mboxId}">
        <div class="promotions-image-and-text-overlay-container main-image-and-text-overlay">
            <sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html"/>
            <sly data-sly-call="${image.basic @ imagePath=properties.imagePath, alt=properties.imageAlt, cssClassNames='promotion-img'}"/>
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScript}" />
            <sly data-sly-use.termsLink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='termsLink'}" />
            <sly data-sly-set.hasTncClass="${properties.textEditor == 'tncDetails' ? 'tnc-extended' : ''}" />            
            
            <div class="wrapper-container">
                <a href="${link}" class="wrapper" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                    <div class="bottom-text">
                        <div class="wrapper-text ${hasTncClass}">
                            <div data-sly-test="${properties.mainText}" class="hovering-text main-text ${hasTncClass}">
                                ${properties.mainText}
                                <sly data-sly-test="${properties.innerLinkLabel}">                                    
                                <br>
                                    <span data-href="${innerLinkHref}" class="inner-link">${properties.innerLinkLabel}</span>
                                </sly>
                            </div>
                            <div class="line"></div>
                            <div data-sly-test="${properties.hoveringText}" class="hovering-text ${hasTncClass}">
                                ${properties.hoveringText}
                                <sly data-sly-test="${properties.text}">
                                <div class="pc-tnc">${properties.text @ context='html'}</div> 
                                </sly>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>

 
<template data-sly-template.auto="${@ promotion, link}">
    <div class="promotions-image-and-text-overlay">
        <div class="promotions-image-and-text-overlay-component">
            <div class="promotions-image-and-text-overlay-container main-image-and-text-overlay">
                <img class="promotion-img" src="${promotion.image}" alt="${promotion.imageAlt}"
                     loading="lazy">
                     <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScript}" />
                   <div class="wrapper-container">
                        <a href="${link}" class="wrapper" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                        <div class="bottom-text">
                            <div class="wrapper-text ${promotion.textEditor == 'tncDetails' ? 'tnc-extended' : ''}">
                                <div data-sly-test="${promotion.title}"
                                    class="hovering-text main-text ${promotion.textEditor == 'tncDetails' ? 'tnc-extended' : ''}">${promotion.title}
                                    <sly data-sly-test="${promotion.innerLinkLabel}">
                                        <br>
                                        <span data-sly-test="${promotion.innerLinkLabel}"
                                                class="inner-link"
                                                data-href="${promotion.innerLinkHref}">
                                                ${promotion.innerLinkLabel}
                                        </span>
                                    </sly>
                                </div>                                 
                                <div class="line"></div>
                                <div data-sly-test="${promotion.text}"
                                    class="hovering-text ${promotion.textEditor == 'tncDetails' ? 'tnc-extended' : ''}">${promotion.text}
                                    <sly data-sly-test="${promotion.tncText}">
                                    <div class="pc-tnc__carousel">${promotion.tncText @ context='html'}</div>
                                    </sly>
                                </div>                            
                            </div>
                        </div>
                    </a>
                 </div>
            </div>
        </div>
    </div>
</template>
 
<template data-sly-template.amp="${@ properties, link}">
    <div class="promotions-image-and-text-overlay-component" data-mbox-id="${properties.mboxId}">
        <div class="promotions-image-and-text-overlay-container main-image-and-text-overlay">
            <amp-img class="promotion-img" src="${properties.imagePath}" alt="${properties.imageAlt}" layout="responsive" width="600" height="338"></amp-img>
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScript}" />
            <a href="${link}" class="wrapper" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <div class="bottom-text">
                    <div class="wrapper-text">
                        <div data-sly-test="${properties.mainText}" class="hovering-text main-text">${properties.mainText}</div>
                        <div class="line"><div class="line-style"></div></div>
                        <div data-sly-test="${properties.hoveringText}" class="hovering-text">
                            <span class="two-lines-max"> ${properties.hoveringText} </span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
</template>
 
<template data-sly-template.ampauto="${@ promotion, link}">
    <div class="promotions-image-and-text-overlay-component" data-mbox-id="${properties.mboxId}">
        <div class="promotions-image-and-text-overlay-container main-image-and-text-overlay">
            <amp-img class="promotion-img" src="${promotion.image}" alt="${promotion.imageAlt}" layout="responsive" width="600" height="338"></amp-img>
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScript}" />
            <a href="${link}" class="wrapper" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <div class="bottom-text">
                    <div class="wrapper-text">
                        <div data-sly-test="${promotion.title}" class="hovering-text main-text">${promotion.title}</div>
                        <div class="line"><div class="line-style"></div></div>
                        <div data-sly-test="${promotion.text}" class="hovering-text">
                            <span class="two-lines-max"> ${promotion.text} </span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
</template