.sidenav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5%;
}
.sidenav-wrapper.sidenav-wrapper--open {
    width: 25rem; /* adjust as needed */
    @media (max-width: $md-max) {
        width: 15rem;
    }
}
.sidenav-wrapper {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    background-color: $sidenav-background-color;
    overflow-x: hidden;
    padding-top: 6rem;
    transition: 0.5s;
    background: $sidenav-background;
    font-family: $font-family-12;
}

.sidenav-wrapper img {
    width: 3rem;
    height: auto;
}
.sidenav .header-bar-light-lp-components__sidenav-menu {
    width: 3rem;
    height: 3rem;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 0.5rem;
    z-index: 999;
    scale: 1.3;
    margin-right: 3.2rem;
}
.sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 2.5rem;
    font-size: 3.6rem;
    margin-left: 5rem;
    text-decoration: none !important;
    color: $gold !important;
    display: block;
    transition: 0.3s;
    cursor: pointer;
}
.sidenav-wrapper a {
    text-decoration: none !important;
    color: $gold !important;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0 0.8rem;
    margin-bottom: 1.6rem;
}
.sidenav-wrapper li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0 0.8rem;
    margin-bottom: 1.6rem;
    list-style: none;
    line-height: unset !important;
}
.sidenav .logo-image {
    z-index: 999;
    padding-top: 0.3rem;
    @media (max-width: $md-max) {
        width: 8rem;
    }
}
.sidenav .logo-image img {
    width: 100%;
    height: auto;
    @media (max-width: $md-max) {
        width: 100%;
        height: auto;
    }
}

.sidenav-wrapper ul {
    margin-left: 0;
}
.sidenav-wrapper .sidenav-item-label {
    text-decoration: none;
    font-size: 2rem;
    color: $gold;
    display: block;
    transition: 0.3s;
    cursor: pointer;
}
.cta-sidenav {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 999;
    a {
        font-family: 'JostBold';
        cursor: pointer;
        text-decoration: none;
        color: $gold;
        outline: 0;
        box-shadow: none;
        font-size: 1.6rem;
    }
}
.sidenav-image img {
    height: 3rem;
    width: auto;
}
