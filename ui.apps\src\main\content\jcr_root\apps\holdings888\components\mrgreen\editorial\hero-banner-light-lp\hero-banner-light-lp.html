<div class="custom-banner ${properties.aboveCtaOption == 'aboveCtaOffer' ? 'cta-offer' : ''} ${properties.activateCover ? 'backgroundCover' : ''} ${properties.activateContain ? 'backgroundContain' : ''} ${properties.newOfferStyleChanges ? 'new-offer-style' : ''}
 ${properties.setComponentBackgroundColor ? 'dark-background' : ''} ${properties.aboveCtaOption == 'none' ? 'no-cta-offer' : ''} ${properties.aboveCtaOption == 'aboveCtaimage' ? 'above-image' : ''} ${properties.backgroundImageContain ? 'backgroundContain' : ''}
 ${properties.offerAboveImage ? 'offer-above-image' : ''}"
     role="img"
     aria-label="${properties.imageAlt || 'Image Alternative'}"
     title="${properties.imageTitle}"
     data-background-desktop="${properties.backgroundImageDesktop @ context='uri'}"
     data-background-mobile="${properties.backgroundImageMobile @ context='uri'}" data-mbox-id="${properties.mboxId}">

    <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaimage'}">
        <img src="${properties.aboveCtaImage @ context='uri'}" class="above-cta-image" alt="${properties.aboveCtaImageAlt || 'Above CTA Image'}" title="${properties.aboveCtaImageAlt || 'Above CTA Image'}" loading="lazy"/>
    </sly>
    <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaRichText'}">
        <div class="above-cta-rich-text">
            <sly data-sly-resource="${'above-cta-rich-text' @ resourceType='holdings888/components/mrgreen/editorial/rich-text'}"/>
        </div>
    </sly>
    <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaOffer'}">

            <div class="offerContainer">
                <p class="offer-type">${properties.aboveCtaOfferType}</p>
                <div class="offerPrice">
                    <p class="offer-amount">${properties.aboveCtaOfferAmount}</p>
                    <p class="offer-amount-text">${properties.aboveCtaOfferAmountText}</p>
                </div>
                <div class="subtitle">
                    <p>${properties.aboveCtaOfferSubtitle}</p>
                    <p>${properties.aboveCtaOfferSubtitleRightText}</p>
                </div>
                <div class="offer-text ${properties.deactivateCover ? 'deactivateCover' : ''}">
                    <p>${properties.aboveCtaOfferText}</p>  
                </div>
            </div>

    </sly>

    <div class="cta-button-container ${properties.setMaxWidth ? 'max-width-40' : ''} ${properties.optionSelect == 'additionalImages' ? 'has-image' : ''} 
    ${properties.optionSelect == 'richText' ? 'has-text' : ''} ${properties.optionSelect == 'imagesAndRichText' ? 'has-imageAndRichText' : ''} ${properties.aboveCtaOption == 'aboveCtaOffer' ? 'has-offer' : ''}
    ${properties.hideImage ? 'hide-images-cnt' : ''}">
        <div class="cta-comp ${properties.newOfferStyleChanges ? 'double-cta' : ''}">
            <sly data-sly-resource="${'cta' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"/>
            <sly data-sly-test="${properties.newOfferStyleChanges}">
                <div class="cta-comp-login">
                    <sly data-sly-resource="${'rich-text-login' @ resourceType='holdings888/components/mrgreen/editorial/rich-text'}"/>
                    <sly data-sly-resource="${'cta-login' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"/>
                </div>
            </sly>
        </div>
        <sly data-sly-test="${properties.optionSelect == 'additionalImages'}">
            <div class="additional-images ${properties.marginImage ? 'marginImage' : ''}">
                <sly data-sly-test="${properties.image1Path}">
                    <img src="${properties.image1Path}" alt="${properties.image1Alt || 'Additional Image 1'}" title="${properties.image1Title || 'Additional Image 1'}" data-redirect-url="${properties.image1Link}" class="clickable-image ${properties.hideImage ? 'hide-images' : ''}" loading="lazy"/>
                </sly>
                <sly data-sly-test="${properties.androidImagePath}">
                    <img src="${properties.androidImagePath}" alt="${properties.androidImageAlt || 'Android Image'}" title="${properties.androidImageTitle || 'Additional Image 1'}" data-redirect-url="${properties.androidImageLink}" class="clickable-image additional-image-android" loading="lazy"/>
                </sly>
                <sly data-sly-test="${properties.iosImagePath}">
                    <img src="${properties.iosImagePath}" alt="${properties.iosImageAlt || 'IOS Image'}" title="${properties.iosImageTitle || 'IOS Image'}" data-redirect-url="${properties.iosImageLink}" class="clickable-image additional-image-ios" loading="lazy"/>
                </sly>
            </div>
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'richText'}">
            <div class="cta-rte">
                <sly data-sly-resource="${'rich-text' @ resourceType='holdings888/components/mrgreen/editorial/rich-text'}"/>
            </div>    
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'threeElements'}">
            <div class="rte-threel">
                <div class="three-elements-container">
                    <sly data-sly-resource="${'three-elements-with-lines' @ resourceType='holdings888/components/mrgreen/editorial/three-elements-with-lines'}"/>
                </div>
                <div class="rte-container">
                    <sly data-sly-resource="${'threeElements-rich-text' @ resourceType='holdings888/components/mrgreen/editorial/rich-text'}"/>
                </div>
            </div> 
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'imagesAndRichText'}">
            <div class="and-rich-text">    
                <div class="additional-images">
                    <sly data-sly-test="${properties.image1Path2}">
                        <img src="${properties.image1Path2}" alt="${properties.image1Alt2 || 'Additional Image 1'}" title="${properties.image1Title2 || 'Additional Image 1'}" data-redirect-url="${properties.image1Link2}" class="clickable-image" loading="lazy"/>
                    </sly>
                </div>
                <div class="adim-rte-container">
                    <sly data-sly-resource="${'ImageAndRichText' @ resourceType='holdings888/components/mrgreen/editorial/rich-text'}"/>
                </div>
            </div>
        </sly>
    </div>
</div>
