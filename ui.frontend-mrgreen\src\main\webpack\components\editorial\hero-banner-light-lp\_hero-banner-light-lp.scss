.hero-banner-light-lp {
    @media (max-width: $md) and (orientation: portrait) {
        height: 126vw;
    }

    @media (max-width: 780px) and (orientation: portrait) {
        height: 130vw;
    }

    @media (max-width: 544px) {
        height: 140vw;
    }

    @media (max-width: 544px) and (orientation: landscape) {
        height: 56vw;
    }

    @media (max-width: 390px) {
        height: 150vw;
    }

    @media (max-width: 330px) {
        height: 167vw;
    }

    &:has(.cta-wide-size) {
        @media (max-width: $md) and (orientation: portrait) {
            height: 134vw;
        }

        @media (max-width: 544px) {
            height: 140vw;
        }

        @media (max-width: 390px) {
            height: 150vw;
        }

        @media (max-width: 330px) {
            height: 167vw;
        }
    }

    &:has(.cta-offer) {
        + .hero-banner-light-lp .custom-banner .above-cta-image {
            @media (max-width: 1240px) and (min-height: 800px) and (orientation: landscape) {
                width: 30%;
                left: 15%;
                top: 20%;
            }

            @media (max-width: $md) {
                width: 30%;
                left: 15%;
                top: 20%;
            }
        }
    }

    &:has(.dark-background) {
        background-color: $dark-3;
    }

    &:has(.new-offer-style) {
        @media (max-width: $md) and (orientation: portrait) {
            height: 140vw;
        }

        @media (max-width: 640px) and (orientation: portrait) {
            height: 780px;
        }

        @media (max-width: 544px) and (orientation: landscape) {
            height: auto;
        }
    }

    &:has(.no-cta-offer) {
        @media (max-width: 330px) {
            height: 150vw;
        }
    }

    &:has(.backgroundContain) {
        @media (max-width: 544px) and (orientation: portrait) {
            height: 134vw;
        }

        @media (max-width: 330px) and (orientation: portrait) {
            height: 140vw;
        }
    }

    .custom-banner {
        background-size: cover;
        background-repeat: no-repeat;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        padding-top: calc(9 / 16 * 75%);
        position: relative;

        &.backgroundContain {
            @media (max-width: $md) and (min-width: 760px) and (orientation: portrait) {
                background-size: contain;
                padding-top: calc(10 / 9 * 92%);
            }

            @media (max-width: 544px) and (orientation: portrait) {
                background-size: contain;
                padding-top: calc(10 / 9 * 90%);
            }

            @media (max-width: 390px) and (orientation: portrait) {
                padding-top: calc(10 / 9 * 87%);
            }
            
            .cta-button-container {
                @media (max-width: 330px) and (orientation: portrait) {
                    bottom: -42%;
                }
            }
        }
        .cta-button-container {
            .cta-comp {
                .cta-component {
                    .cta-template.cta-default-size {
                        a {
                            font-size: 1.6vw;
                            font-family: $font-family;
                            min-width: 22.2vw;
                            padding: 1.32vw 0;

                            @media (max-width: $md) and (orientation: portrait) {
                                font-size: 3.8vw;
                                min-width: 93vw;
                                padding: 4.1vw 0;
                                border-radius: 7rem;
                            }

                            @media (max-width: 548px) and (orientation: portrait) {
                                font-size: 5vw;
                                min-width: 93vw;
                                padding: 3.9vw 0;
                            }
                        }
                    }
                }
            }

            .three-elements-container {
                width: 100%;
                max-width: 90vw;
                margin: auto;
                .cmp-three-elm {
                    width: 100%;
                    .cmp-three-elm__step {
                        .cmp-three-elm__step-no {
                            font-size: 3.6vw;
                        }
                    }
                }
            }
        }

        &.above-image {
            &:has(.cta-default-size) {
                .cta-button-container {
                    @media (max-width: $md) and (min-width: 768px) and (orientation: portrait) {
                        bottom: -18%;
                    }

                    .cta-rte {
                        @media (max-width: $md) and (min-width: 768px) and (orientation: portrait) {
                            max-width: unset;
                        }
                    }
                }

                .above-cta-image {
                    @media (max-width: 768px) {
                        width: 31%;
                        left: 2%;
                        top: 3%;
                    }
                }
            }
        }

        &.cta-offer {
            padding-top: calc(9 / 16 * 75% + 5vw);

            @media (max-width: 1025px) and (orientation: portrait) {
                padding-top: 105.5555555556%;
                height: auto;
            }

            @media (max-width: 780px) and (min-width: 640px) {
                padding-top: calc(9 / 16 * 75% + 20vw);
            }

            @media (max-width: 770px) and (min-height: 920px) {
                padding-top: 105.5555555556%;
                height: auto;
            }

            .cta-button-container {
                @media (max-width: 1240px) and (min-height: 800px) and (orientation: landscape) {
                    left: 15%;
                }

                @media (max-width: 640px) {
                    gap: 0;
                }

                .three-elements-container {
                    @media (max-width: 640px) {
                        order: 3;
                    }

                    .cmp-three-elm {
                        .cmp-three-elm__step {
                            .cmp-three-elm__step-content {
                                .cmp-three-elm__step-title {
                                    p {
                                        @media (max-width: 850px) and (orientation: landscape) {
                                            font-size: 1.3vw;
                                        }
                                    }
                                }

                                .cmp-three-elm__step-text {
                                    p {
                                        font-size: 1rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.no-cta-offer {
            .cta-button-container.has-image {
                bottom: 54%;

                @media (max-width: 1025px) and (orientation: portrait) {
                    position: absolute;
                    bottom: -10%;
                }

                @media (max-width: $sm) {
                    position: unset;
                    padding-top: 0;
                }

                @media (max-width: $sm) and (orientation: landscape) {
                    position: absolute;
                }

                .additional-images {
                    @media (max-width: $sm) {
                        margin-top: 0;
                    }
                }
            }
            .cta-button-container {
                bottom: 54%;

                @media (max-width: 1025px) and (orientation: portrait) {
                    position: absolute;
                    bottom: -10%;
                }

                @media (max-width: $sm) {
                    position: unset;
                    padding-top: 0;
                }

                @media (max-width: $sm) and (orientation: landscape) {
                    position: absolute;
                }

                .cta-comp {
                    .cta-component {
                        .cta-default-size {
                            a {
                                font-size: 1.5vw;
                                min-width: 22.92vw;
                                padding: 1.42vw 0;

                                @media (max-width: 1180px) and (max-height: 820px) and (orientation: landscape) {
                                    min-width: 31.5vw;
                                }

                                @media (max-width: $md) {
                                    min-width: 32.4vw;
                                }

                                @media (max-width: $md) and (orientation: portrait) {
                                    min-width: 57.8vw;
                                    font-size: 3vw;
                                    padding: 1.75vw 0;
                                }

                                @media (max-width: $sm) {
                                    font-size: 2.4rem;
                                    min-width: 288px;
                                    padding: 1.7rem 0;
                                }

                                @media (max-width: $sm) and (orientation: landscape) {
                                    font-size: 1.5vw;
                                    min-width: 176px;
                                    padding: 0.65rem 0;
                                }

                                @media (max-width: 350px) {
                                    min-width: 271px;
                                    padding: 1.55rem 0;
                                }

                                span {
                                    font-family: $font-family-12;
                                }
                            }
                        }
                    }
                }

                .additional-images {
                    @media (max-width: $sm) {
                        margin-top: 0;
                    }
                }
            }
        }

        &.new-offer-style {
            height: 848px;
            background-size: cover;
            background-position: right;

            @media (max-width: 1480px) {
                height: 667px;
            }

            @media (max-width: 1280px) {
                height: unset;
                background-position: right;
            }

            @media (max-width: 940px) and (orientation: landscape) {
                background-position: 70%;
            }

            @media (max-width: $md) and (orientation: portrait) {
                background-position: top;
            }

            @media (max-width: 780px) and (min-width: 640px) and (orientation: portrait) {
                height: 390px;
                background-position: center;
            }

            @media (max-width: 940px) and (max-height: 430px) and (orientation: landscape) {
                height: 450px;
            }

            @media (max-width: 513px) and (orientation: landscape) {
                background-position: 60%;
            }

            @media (max-width: 430px) {
                height: 442px;
                background-position: top;
            }

            .above-cta-rich-text {
                width: 42%;
                bottom: 47%;

                @media (max-width: 1380px) and (orientation: landscape) {
                    width: 30%;
                }

                @media (max-width: 1200px) and (orientation: landscape) {
                    width: 50%;
                }

                @media (max-width: $md) {
                    bottom: 55%;
                }

                @media (max-width: $md) and (orientation: portrait) {
                    bottom: 46%;
                    width: 50%;
                }

                @media (max-width: 820px) and (orientation: portrait) {
                    width: 46%;
                }

                @media (max-width: 780px) {
                    bottom: 60%;
                }

                @media (max-width: 780px) and (orientation: portrait) {
                    width: 48%;
                    bottom: 50%;
                }

                @media (max-width: $sm) and (orientation: portrait) {
                    width: 90%;
                    bottom: 58%;
                    left: 3%;
                }

                @media (max-width: 390px) and (orientation: portrait) {
                    width: 78%;
                }

                @media (max-width: 330px) and (orientation: portrait) {
                    bottom: 56%;
                }

                .rich-text-component {
                    .text {
                        h2 {
                            margin-block-start: 0;
                            margin-block-end: .5em;
                        }
                    } 
                }
            }

            .cta-button-container {
                bottom: 24%;

                @media (max-width: 1200px) and (orientation: landscape) {
                    bottom: 30%;
                }

                @media (max-width: $md) {
                    bottom: 30%;
                }

                @media (max-width: 780px) {
                    bottom: 44%;
                }

                @media (max-width: 940px) and (max-height: 430px) and (orientation: landscape) {
                    bottom: 32%;
                }

                @media (max-width: 670px) and (max-height: 430px) and (orientation: landscape) {
                    bottom: 40%;
                }

                @media (max-width: $md) and (orientation: portrait) {
                    bottom: -14%;
                }

                @media (max-width: 820px) and (orientation: portrait) {
                    bottom: -18%;
                }

                @media (max-width: $sm) and (orientation: portrait) {
                    bottom: -38%;
                }

                @media (max-width: 390px) and (orientation: portrait) {
                    bottom: -40%;
                }

                @media (max-width: 330px) and (orientation: portrait) {
                    bottom: -40%;
                }

                .cta-comp.double-cta {
                    gap: 0;

                    @media (max-width: $md) and (orientation: portrait) {
                        display: flex;
                        flex-direction: column-reverse;
                        width: 100%;
                        align-items: center;
                        gap: 2rem;
                    }

                    @media (max-width: 820px) and (orientation: portrait) {
                        gap: 6rem;
                    }

                    .cta-comp-login {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;

                        .rich-text-component .text p {
                            display: none;
                            color: $white;
                            padding-bottom: 0;

                            @media (orientation: portrait) {
                                display: block;
                            }
                        }

                        .cta-component {
                            .cta-template.cta-primary {
                                a {
                                    padding: 0;
                                    background: transparent;
                                    border: none;
                                    line-height: normal;
                                    font-size: 1.2rem;
                                    text-decoration: underline;
                                    color: $gold;
                                    font-family: $font-family-12;
                                    min-width: 80px;

                                    @media (orientation: portrait) {
                                        min-width: auto;
                                    }
                                }

                                span {
                                    color: $gold;
                                    font-family: $font-family-12;
                                }
                            }
                        }
                    }
                }

                .three-elements-container {
                    .cmp-three-elm {
                        @media (max-width: $sm) and (orientation: portrait) {
                            flex-direction: column;
                            gap: 1rem;
                        }

                        .cmp-three-elm__step {
                            @media (max-width: $sm) and (orientation: portrait) {
                                padding-left: 3vw;
                            }
                        }
                    }
                }

                .rte-threel {
                    width: 66vw;

                    @media (max-width: 1180px) and (orientation: landscape) {
                        width: 88vw;
                    }

                    @media (max-width: $md) {
                        width: 90vw;
                    }

                    .rte-container {
                        padding: 3rem 0 2rem;
                        max-width: 46vw;

                        @media (max-width: 1180px) and (orientation: landscape) {
                            max-width: 64vw;
                        }

                        @media (max-width: $md) and (orientation: portrait) {
                            max-width: 90vw;
                        }

                        @media (max-width: 900px) and (orientation: landscape) {
                            padding: 1rem 0 2rem;
                        }

                        @media (max-width: 780px) {
                            padding: 1rem 0 2rem;
                        }

                        @media (max-width: 513px) and (orientation: portrait) {
                            max-width: 90vw;
                            padding: 0 0 2rem;
                        }

                        .rich-text-component {
                            .text p {
                                @media (max-width: 513px) and (orientation: portrait) {
                                    line-height: normal;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.offer-above-image {

            .above-cta-rich-text {
                @media (max-width: $md) and (orientation: portrait) {
                    bottom: unset;
                    top: -14%;
                    width: 90%;
                }

                @media (max-width: 820px) and (orientation: portrait) {
                    top: -16%;
                }

                @media (max-width: 430px) and (orientation: portrait) {
                    top: -26%;
                }

                @media (max-width: 420px) and (orientation: portrait) {
                    top: -30%;
                }

                @media (max-width: 390px) and (orientation: portrait) {
                    top: -34%;
                }

                @media (max-width: 330px) and (orientation: portrait) {
                    top: -41%;
                }

                .rich-text-component .text {
                    @media (max-width: $md) and (orientation: portrait) {
                        text-align: center;
                    }

                }
            }

            .cta-button-container {
                &.hide-images-cnt {
                    @media (max-width: 840px) and (orientation: portrait) {
                        bottom: -10%;
                    } 

                    @media (max-width: 430px) and (orientation: portrait) {
                        gap: 0;
                        bottom: -34%;
                    } 

                    @media (max-width: 390px) and (orientation: portrait) {
                        bottom: -38%;
                    }

                    @media (max-width: 330px) and (orientation: portrait) {
                        bottom: -42%;
                    } 
                }

                .additional-images {
                    .hide-images {
                        @media (max-width: 948px) {
                            display: none;
                        }
                    }

                    .additional-image-android,
                    .additional-image-ios {
                        display: none;

                        &.active {
                            @media (max-width: 640px) {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        &.backgroundCover {
            background-size: cover;
        }
        .above-cta-image {
            display: block;
            width: 31%;
            left: 15%;
            top: 15%;
            position: absolute;

            @media (max-width: $md) {
                width: 40%;
                left: 2%;
                top: 3%;
            }

            @media (max-width: 1200px) and (orientation: landscape) {
                width: 40%;
                left: 2%;
                top: 1%;
            }
        }

        .above-cta-rich-text {
            width: 28%;
            position: absolute;
            bottom: 40%;
            left: 15%;

            @media (max-width: 1240px) {
                width: 40%;
                left: 2%;
            }

            @media screen and (max-width: $sm-grid) and (orientation: landscape) {
                width: 55%;
                bottom: 49%;
            }

            &.clickable {
                width: 100%;
            }
        }

        .offerContainer {
            width: 60%;
            position: absolute;
            top: 11vw;
            left: 15%;
            padding: 0px 2.5rem;
            border-left: 5px solid $green-6;
            @media screen and (min-width: 911px) and (max-width: 1080px) {
                top: 11vw;
            }

            @media (max-width: 670px) and (orientation: landscape) {
                top: 18vw;
            }

            .offer-type {
                color: $white;
                font-weight: bold;
                letter-spacing: 1.3px;
                font-size: 1.3vw;
                font-family: $font-family-20;
                margin: 0;

                @media (max-width: $md) and (orientation: portrait) {
                    font-size: 1.6rem;
                }
            }

            .offerPrice {
                display: flex;
                flex-direction: column;
                font-family: $font-family-21;
                align-items: start;

                @media (max-width: 940px) and (orientation: landscape) {
                    flex-direction: row;
                    gap: 1rem;
                }

                .offer-amount,
                .offer-amount-text {
                    font-size: 6vw;
                    margin: 0;
                    font-weight: bold;
                    line-height: 1;
                    color: $green-6;
                    letter-spacing: -8px;

                    @media (max-width: 940px) and (orientation: landscape) {
                        font-size: 4vw;
                        letter-spacing: 0;
                    }
                }
            }

            .subtitle {
                display: flex;
                gap: 4rem;

                @media only screen and (max-width: 600px) and (orientation: portrait) {
                    width: 65% !important;
                }
                &.deactivateCover {
                    background-color: transparent;
                }

                p {
                    font-size: 6vw;
                    font-weight: bold;
                    line-height: 1;
                    color: $green-6;
                    letter-spacing: -8px;
                    font-family: $font-family-21;

                    @media (max-width: 940px) and (orientation: landscape) {
                        font-size: 4vw;
                        letter-spacing: 0;
                    }

                    &:last-child {
                        font-size: 2.3vw;
                        color: $white-smoke;
                        font-weight: 800;
                        letter-spacing: 1px;
                        line-height: 1;
                        text-transform: uppercase;
                        display: flex;
                        align-items: center;
                        font-family: $font-family-21;
                        transform: translateY(15px);

                        @media (max-width: $md) {
                            transform: translateY(0);
                        }
                    }
                }
            }

            .offer-text {
                background-color: $nero;
                padding: 5px 10px;
                display: inline-block;

                p {
                    font-size: 2.2vw;
                    color: $white;
                    font-weight: 800;
                    letter-spacing: 1px;
                    line-height: 1;
                    font-family: $font-family-21;
                }
            }
        }

        .cta-button-container {
            &.max-width-40 {
                .cta-rte {
                    max-width: 28vw;

                    @media (max-width: 548px) and (orientation: portrait) {
                        max-width: unset;
                    }
                }

                .rte-threel {
                    .rte-container {
                        max-width: 28vw;
                        padding: 1rem 0;

                        @media (max-width: 1380px) and (min-height: 920px) and (orientation: landscape) {
                            padding: 0.5rem 0;
                        }

                        @media (max-width: $md) and (orientation: portrait) {
                            max-width: 90vw;
                        }

                        .rich-text-component .text p {
                            padding-bottom: 0;
                        }
                    }
                }
            }

            bottom: 22%;
            left: 15%;
            height: 4.5vw;
            position: absolute;

            @media (max-width: 1480px) {
                bottom: 26%;
            }

            @media (max-width: 1240px) {
                left: 2%;
            }

            @media (max-width: $sm-grid) {
                bottom: 33%;
            }

            .cta-comp {
                display: flex;
                .cta-component {
                    display: flex;
                    justify-content: start;
                    align-items: center;
                    a {
                        font-family: $font-family-17;

                        @media screen and (orientation: landscape) {
                            font-size: 1.6vw;
                        }
                    }
                    @media screen and (max-width: 460px) {
                        .cta-fullwidth-size {
                            a {
                                font-size: 5vw;
                                border-radius: 8vw;
                            }
                        }
                    }
                    .cta-large-size {
                        a {
                            min-width: 420px;
                            padding: 1.6rem 0;

                            @media (max-width: 1480px) {
                                min-width: 320px;
                                padding: 1.1rem 0;
                            }

                            @media (max-width: 1200px) and (orientation: landscape) {
                                min-width: 369px;
                                padding: 0.9rem 0;
                            }

                            @media (max-width: $md) {
                                min-width: 304px;
                                padding: 0.75rem 0;
                            }

                            @media (max-width: $md) and (min-height: $md) and (orientation: landscape) {
                                min-width: 275px;
                                padding: 0.6rem 0;
                            }

                            @media (max-width: 920px) and (orientation: landscape) {
                                min-width: 248px;
                                padding: 0.55rem 0;
                            }

                            @media (max-width: $sm-grid) {
                                min-width: 220px;
                                padding: 0.5rem 0;
                            }

                            @media (max-width: 544px) {
                                min-width: 360px;
                                font-size: 2.4rem;
                            }

                            @media (max-width: 544px) and (orientation: landscape) {
                                min-width: 165px;
                                font-size: 1.6vw;
                            }

                            @media (max-width: 390px) {
                                min-width: 315px;
                            }

                            @media (max-width: 330px) {
                                min-width: 266px;
                            }
                        }
                    }
                    .cta-wide-size {
                        height: auto;
                        width: 100%;
                        a {
                            font-size: 1.6vw;
                            align-items: center;
                            min-width: 28vw;
                            padding: 1.75rem 0;

                            @media (max-width: 1480px) {
                                padding: 1.28rem 0;
                            }

                            @media (max-width: 1240px) and (min-height: 800px) and (orientation: landscape) {
                                padding: 1rem 0;
                            }

                            @media (max-width: $md) {
                                padding: 0.9rem 0;
                            }

                            @media (max-width: $sm-air) {
                                padding: 0.6rem 0;
                            }
                        }
                    }
                    .cta-medium-size {
                        height: auto;
                        width: 100%;
                        a {
                            font-size: 1.5vw;
                            align-items: center;
                            min-width: 18.15vw;
                            padding: 1.05vw 0;

                            @media (max-width: $md) and (min-width: 820px) and (orientation: portrait) {
                                min-width: 591px;
                                font-size: 3vw;
                            }

                            @media (max-width: 820px) and (min-width: 768px) and (orientation: portrait) {
                                min-width: 468px;
                                font-size: 3vw;
                            }

                            @media (max-width: $sm) and (orientation: portrait) {
                                width: 260px;
                                padding: 2.6vw 0;
                                font-size: 24px;
                            }

                            @media (max-width: 350px) and (orientation: portrait) {
                                font-size: 22px;
                                width: 245px;
                            }
                        }
                    }
                }
            }

            .additional-images {
                display: flex;
                justify-content: center;
                margin-top: 1rem;

                @media screen and (max-width: $sm) and (orientation: landscape) {
                    margin-top: 0 !important;
                }

                img {
                    object-fit: contain;
                    max-height: 5vw;
                    width: 20vw;
                    padding: 3% 3% 0 3%;

                    @media (max-width: $md) {
                        width: 28vw;
                    }

                    &:hover {
                        cursor: pointer;
                    }
                }

                .additional-image-android,
                .additional-image-ios {
                    display: none;

                    &.active {
                        display: none;
                    }
                }
            }

            .cta-rte {
                text-align: center;
                padding-top: 1rem;
                width: 28vw;

                .text p {
                    font-size: 0.7vw;
                    line-height: 1;

                    @media (max-width: $md) {
                        line-height: 1.2;
                    }
                }
            }

            .three-elements-container {
                padding-top: 0;
                width: 140%;
                max-width: 70vw;
                display: flex;

                @media screen and (max-width: 920px) and (orientation: landscape) {
                    z-index: 2;
                }

                @media screen and (min-width: 540px) and (max-width: 768px) {
                    padding-top: 0;
                    margin-bottom: -1rem;
                }

                @media screen and (max-width: 325px) {
                    padding-top: 0;
                }

                .cmp-three-elm-component {
                    @media screen and (max-width: 325px) {
                        transform: translateX(-15%);
                    }
                    .cmp-three-elm {
                        @media screen and (min-width: 911px) and (max-width: 1080px) {
                            padding-bottom: 1.1rem;
                        }
                        @media screen and (max-width: 768px) {
                            margin-bottom: 0;
                        }
                        @media screen and (max-width: 425px) {
                            margin-bottom: 1rem;
                        }
                        @media screen and (max-width: 375px) {
                            margin-bottom: 0;
                        }
                        .cmp-three-elm__step {
                            width: auto;
                            max-width: unset;
                        }
                    }
                }

                .cmp-three-elm {
                    &.darkGreenColor .cmp-three-elm__step {
                        @media (max-width: 640px) {
                            border-left-color: $green-6;
                        }
                    }

                    &.goldColor {
                        .cmp-three-elm__step {
                            border-color: $gold;
                            width: 26%;

                            @media (max-width: $sm) and (orientation: portrait) {
                                border-color: $green-3;
                                width: 31%;
                            }

                            .cmp-three-elm__step-no {
                                font-size: 5vw;
                                font-family: $font-family-5;
                                color: $gold;

                                @media (max-width: $sm) {
                                    font-size: 3rem;
                                    width: 2rem;
                                }
                            }

                            .cmp-three-elm__step-content {
                                margin-top: 6px;

                                @media (max-width: $sm) {
                                    margin-top: 0;
                                }

                                .cmp-three-elm__step-title p {
                                    color: #dddcaa;
                                    font-size: 14px;
                                    font-family: $font-family-13-a;

                                    @media (max-width: $sm) {
                                        font-size: 1rem;
                                    }
                                }
                                .cmp-three-elm__step-text p {
                                    font-size: 12px;
                                    color: #dddcaa;
                                    font-family: $font-family-12;
                                }
                            }
                        }

                        .cmp-three-elm__step:first-child {
                            @media (max-width: $sm) {
                                border-left: 0.3rem solid $green-3 !important;
                            }

                            @media (max-width: 550px) and (orientation: landscape) {
                                border-left: 0.3rem solid $gold !important;
                            }
                        }
                    }

                    .cmp-three-elm__step {
                        @media (max-width: 640px) {
                            padding-left: 2vw;
                        }
                        .cmp-three-elm__step-content {
                            .cmp-three-elm__step-title {
                                p {
                                    font-family: $font-family-18;

                                    @media (max-width: 1024px) and (min-width: 768px) {
                                        font-size: 1.6rem;
                                    }

                                    @media (max-width: 500px) {
                                        font-size: 1.1rem;
                                        line-height: 1.1;
                                    }
                                }
                            }

                            .cmp-three-elm__step-text {
                                p {
                                    font-family: $font-family-19;

                                    @media (max-width: 1024px) and (min-width: 768px) {
                                        font-size: 1.5rem;
                                    }

                                    @media (max-width: 500px) {
                                        font-size: 1rem;
                                        line-height: 1.1;
                                    }
                                }
                            }
                        }
                    }
                }

                &:has(.with-title) {
                    .cmp-three-elm-component {
                        flex-direction: column;

                        &__title {
                            font-size: 1.24vw;
                            font-weight: bold;
                            padding-left: 1.6vw;
                            margin: 3.8vw 0 1vw 0;
                        }
                    }
                }
            }

            .rte-threel {
                display: flex;
                flex-direction: column-reverse;

                .three-elements-container {
                    width: 100%;
                    max-width: 70vw;

                    @media (max-width: 1240px) {
                        max-width: 95vw;
                    }

                    .cmp-three-elm {
                        width: 100%;

                        .cmp-three-elm__step {
                            padding: .7vw 1vw .7vw 2vw;
                        }
                    }
                }

                .rte-container {
                    padding: 2rem 0 1rem 2rem;
                    width: 100%;
                    max-width: 70vw;

                    @media (max-width: 1440px) {
                        padding: 2rem 0 0;
                    }

                    @media (max-width: 1240px) {
                        padding: 1rem 0 0;
                        max-width: 95vw;
                    }

                    @media (max-width: 544px) {
                        padding: 2rem 0 0 0;
                        max-width: 100vw;
                    }

                    .text p {
                        font-size: 1.2rem;
                        line-height: 1;
                        font-family: $font-family-4;
                        padding-bottom: 0;

                        @media (max-width: 1400px) {
                            font-size: 1rem;
                        }

                        @media (max-width: $md) {
                            line-height: normal;
                        }
                    }
                }
            }

            .and-rich-text {
                display: flex;
                flex-direction: row;
                gap: 10vw;

                .adim-rte-container {
                    padding-right: 3rem;

                    .rich-text-component {
                        .text {
                            p {
                                font-size: 0.9vw;
                                line-height: 1.1vw;
                            }
                        }
                    }
                }
            }

            &.has-image {
                @media (max-width: 1480px) {
                    bottom: 23%;
                }
                @media (max-width: $md) and (orientation: portrait) {
                    position: absolute;
                    bottom: -20%;
                }
                @media (max-width: 640px) and (orientation: portrait) {
                    bottom: -30%;
                }
                @media (max-width: 640px) and (orientation: landscape) {
                    position: absolute;
                }
                @media (max-width: 330px) {
                    bottom: -40%;
                }
            }

            &.has-offer {
                @media (max-width: 1240px) and (min-height: 800px) and (orientation: landscape) {
                    left: 15%;
                }
                @media (max-width: $md) {
                    left: 15%;
                }

                .additional-images img {
                    @media (max-width: $md) {
                        width: 21vw;
                    }
                    @media (max-width: $md) and (orientation: portrait) {
                        width: 60vw;
                    }
                }
            }
        }

        &.cta-offer {
            .cta-button-container {
                height: fit-content;
                bottom: 0%;
                @media screen and (min-width: 911px) and (max-width: 1080px) {
                    bottom: 5%;
                    @media screen and (orientation: landscape) {
                        bottom: 10%;
                    }
                }
                @media screen and (min-width: 640px) and (max-width: 768px) and (max-height: 740px) and (orientation: landscape) {
                    &:not(:has(.cta-rte, .and-rich-text, .additional-images)) {
                        bottom: 4%;
                    }
                }
                @media screen and (min-width: 570px) and (max-width: 640px) and (max-height: 740px) and (orientation: landscape) {
                    bottom: -30%;
                }
                @media screen and (min-width: 911px) and (max-width: 1080px) {
                    &:has(.rte-threel) {
                        bottom: -7%;
                    }

                    &:has(.cta-rte) {
                        bottom: 5%;
                    }
                }
                @media screen and (max-width: $md) and (orientation: landscape) {
                    &:has(.rte-threel) {
                        bottom: -1%;
                        z-index: 1;
                    }
                }
                @media screen and (max-width: 940px) and (orientation: landscape) {
                    &:has(.rte-threel) {
                        bottom: 11%;
                    }
                }
                @media screen and (max-width: 640px) and (orientation: landscape) {
                    &:has(.rte-threel) {
                        bottom: -21%;
                    }
                }
                @media (max-width: $md) and (orientation: portrait) {
                    &:has(.rte-threel) {
                        bottom: -20%;
                    }
                }
                @media (max-width: $sm-air) and (orientation: portrait) {
                    &:has(.rte-threel) {
                        bottom: -26%;
                    }
                }
                @media (max-width: 640px) and (orientation: portrait) {
                    &:has(.rte-threel) {
                        bottom: -28%;
                    }
                }
                @media (max-width: 390px) and (orientation: portrait) {
                    &:has(.rte-threel) {
                        bottom: -34%;
                    }
                }
                @media (max-width: 330px) and (orientation: portrait) {
                    &:has(.rte-threel) {
                        bottom: -50%;
                    }
                }

                .rte-threel {
                    width: 70vw;

                    @media (max-width: $md) and (orientation: portrait) {
                        width: 90vw;
                    }
                }

                .rte-threel .rte-container {
                    width: 100%;

                    @media (max-width: 1240px) and (min-height: 800px) and (orientation: landscape) {
                        padding: 0.5rem 0;
                    }

                    .text p {
                        font-size: 0.8vw;
                    }
                }
            }
            height: auto;
        }
    }
    .rte-container {
        @media screen and (min-width: 910px) and (max-width: 1280px) {
            padding-top: 0.5rem !important;
        }
    }

    @media (max-width: $md) and (orientation: portrait) {
        .custom-banner {
            padding-top: calc(10 / 9 * 95%);
            .above-cta-image {
                display: none;
            }
            .above-cta-rich-text {
                bottom: 50%;
                left: 5%;
                width: 45%;
            }
            .above-cta-rich-text,
            .and-rich-text .adim-rte-container {
                width: 46%;
            }

            .offerContainer {
                top: 32vw;
                bottom: unset;
                left: 0;
                width: 100%;
                border-left: 2vw solid $color-link;
                padding: 0.2rem 1.5rem;

                .offerPrice {
                    flex-direction: row;
                    align-items: center;
                    gap: 1.5rem;

                    .offer-amount {
                        font-size: 4.5vw;
                        letter-spacing: 1.2px;
                    }

                    .offer-amount-text {
                        font-size: 4.5vw;
                        letter-spacing: 1.2px;
                    }
                }

                .subtitle {
                    width: 60%;
                    margin-top: 6px;
                    gap: 2rem;

                    &.deactivateCover {
                        background-color: transparent;
                        p {
                            color: $white !important;
                        }
                    }

                    p {
                        font-size: 4.5vw;
                        letter-spacing: 1.2px;
                    }
                }

                .offer-text {
                    font-size: 16px;
                }
            }

            .cta-button-container {
                height: fit-content;
                position: absolute;
                padding: 0 3vw;
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 1rem;
                align-items: center;
                left: 0;
                bottom: -12%;

                .cta-comp {
                    display: block;
                    .cta-component {
                        .cta-large-size {
                            a {
                                min-width: 591px;
                                padding: 0.6rem 0;
                                font-size: 3vw;
                            }
                        }
                        .cta-wide-size {
                            width: 100%;
                            padding: 1rem 2rem;
                            a {
                                min-width: 92.5vw;
                                font-size: 3.8vw;
                                padding: 1.5rem 0;
                                border-radius: 6rem;
                            }
                        }
                    }
                }
                .three-elements-container {
                    width: 100%;
                    max-width: 90vw;
                    margin: auto;
                    .cmp-three-elm {
                        width: 100%;

                        .cmp-three-elm__step {
                            .cmp-three-elm__step-no {
                                font-size: 3.6vw;
                            }
                        }
                    }
                }
                .cta-rte {
                    padding-top: 0;
                    text-align: center;
                    width: 100%;

                    .text p {
                        font-size: 1.7vw;
                        padding: 0.5rem 2rem;
                    }
                }
                .additional-images img {
                    max-height: 14vw;
                    width: 75vw;
                    padding: 0;
                }
                .rte-threel {
                    align-items: center;
                    .rte-container {
                        padding-top: 1vw;
                    }
                }
                .and-rich-text {
                    flex-direction: column;
                    gap: 0vw;
                    .adim-rte-container {
                        display: flex;
                        padding: 0rem 2rem;
                        margin: auto;
                        width: 100%;

                        .rich-text-component .text p {
                            font-family: $font-family-4;
                            font-size: 1.1rem !important;
                            line-height: 3.8vw !important;
                        }
                    }
                    .rich-text-component .text p {
                        font-size: 1.5vw !important;
                        line-height: 1.8vw !important;
                    }
                }
            }

            &.cta-offer {
                .cta-button-container {
                    position: absolute;
                    height: fit-content;
                    left: 0;
                    padding: 0 3vw;

                    .three-elements-container {
                        width: 90%;
                        margin: unset;
                        max-width: unset;
                    }
                    .rte-threel {
                        align-items: flex-start;
                        .rte-container {
                            width: 95%;

                            .text p {
                                font-size: 2.1vw;
                            }
                        }
                    }
                }
            }
            height: auto;
        }
    }

    @media (max-width: 840px) and (orientation: portrait) {
        .custom-banner {
            .above-cta-rich-text {
                bottom: 43%;
            }

            .cta-button-container {
                bottom: -16%;

                .cta-comp {
                    .cta-component {
                        .cta-template {
                            &.cta-large-size {
                                a {
                                    min-width: 468px;
                                }
                            }
                        }
                    }
                }
            }

            &.cta-offer {
                .cta-button-container {
                    .three-elements-container {
                        width: 100%;
                    }
                }
            }
        }
    }

    @media (max-width: 499px) {
        .custom-banner {
            .above-cta-rich-text {
                width: 55%;
                bottom: 28%;
            }

            .offerContainer {
                .offer-type {
                    margin: 0;
                    font-weight: $font-weight-medium;
                }
                .subtitle {
                    width: 60%;
                    margin: 6px 0;
                    flex-direction: column;
                    gap: 1rem;

                    p {
                        font-size: 4.5vw;
                        @media (orientation: portrait) {
                            font-size: 6vw;
                        }

                        &:last-child {
                            font-size: 21px;
                        }
                    }
                }
                .offer-text {
                    margin-bottom: 0;

                    p {
                        font-size: 25px;
                    }
                }
            }

            .cta-button-container {
                bottom: -30%;
                left: 0;

                .three-elements-container {
                    width: 100%;
                    max-width: 100vw;
                    margin: auto;
                    display: flex;

                    &:has(.with-title) {
                        justify-content: space-evenly;

                        .cmp-three-elm-component {
                            flex-direction: column;

                            &__title {
                                font-size: 4.3vw;
                                font-weight: bold;
                                text-align: center;
                                padding-left: 0;
                                margin: 2vw 0;
                            }

                            .cmp-three-elm {
                                .cmp-three-elm__step {
                                    width: auto;
                                    max-width: unset;
                                    border-left: 0.05vw solid #7cf700;

                                    .cmp-three-elm__step-no {
                                        font-size: 8vw;
                                    }
                                    .cmp-three-elm__step-content {
                                        .cmp-three-elm__step-text p {
                                            font-size: 1.8vw;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .cmp-three-elm {
                        .cmp-three-elm__step {
                            .cmp-three-elm__step-no {
                                font-size: 3rem;
                            }
                        }
                    }
                }

                .cta-comp {
                    .cta-component {
                        .cta-template {
                            &.cta-large-size {
                                a {
                                    min-width: 61.2vw;
                                    padding: 1.05rem 0;
                                    font-size: 24px;
                                }
                            }

                            &.cta-wide-size {
                                padding: 1rem 0;
                            }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 390px) {
        .custom-banner {
            .cta-button-container {
                bottom: -38%;
            }
        }
    }

    @media (max-width: 330px) {
        .custom-banner {
            .above-cta-rich-text {
                bottom: 16%;
            }

            .cta-button-container {
                bottom: -50%;

                .cta-rte .text p {
                    padding: 0;
                }
            }
        }
    }
}

.root.container:has(.hero-banner-light-lp) {
    background-color: $dark-3;
}
