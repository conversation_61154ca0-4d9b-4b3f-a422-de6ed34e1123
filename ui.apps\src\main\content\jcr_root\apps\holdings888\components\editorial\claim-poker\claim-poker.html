<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html" />
<sly data-sly-call="${clientlib.css @ categories='holdings888.poker-claim'}" />
<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-call="${clientlibs.fe @ locations='poker-claim-css'}"/>

<sly data-sly-test.isWCMMode="${wcmmode.edit || wcmmode.preview}">
    <input type="hidden" id="isWCMMode" value=true>
</sly>

<div id="fullContent" class="claim-banner-component">
    <div id="automation_claim_response" style="display:none">1</div>
    <div id="web-claim-page">
        <div class="mainWrapper">
      			<img src="" alt="" class="bannerImg"/>
            <div class="claimOffer">
                <h1 class="upperSentenceText"></h1>
                <p class="lowerSentenceText"></p>
        				<a href="" class="cta-button" onclick="" attr-ctaText="" style="display: none;" aria-label=""></a>
            </div>
        </div>
    </div>
</div>

<script>
    let claimPageData = {
        status: '',
        guid: "",
        validationDomain: "${properties.validationDomain @ context='scriptString'}",
        backgroundImage: {
            success: {
                pc: "${properties.bannerSuccessImageDesktopPath @ context='uri'}",
                mobile: "${properties.bannerSuccessImageMobilePath @ context='uri'}"
            },
            error: {
                pc: "${properties.bannerErrorImageDesktopPath @ context='uri'}",
                mobile: "${properties.bannerErrorImageMobilePath @ context='uri'}",
            },
        },
        offerBoxDefault: {
					    upperSentence: {
								success: "${properties.upSuccessText @ context='scriptString'}",
								used: "${properties.upUsedText @ context='scriptString'}",
								expired: "${properties.upExpiredText @ context='scriptString'}",
								error: "${properties.upErrorText @ context='scriptString'}",
							},
							message: {
									success: "${properties.successText @ context='scriptString'}",
									used: "${properties.usedText @ context='scriptString'}",
									error: "${properties.errorText @ context='scriptString'}",
									expired: "${properties.expiredText @ context='scriptString'}",
							},
							cta: {
									ctaText: "${properties.CTAlabel @ context='unsafe'}",
									ctaScript: "${properties.ctaScript @ context='unsafe'}",
									CTAariaLabel: "${properties.CTAariaLabel @ context='unsafe'}",
									ctaUrl: "${properties.CTAurl @ context='uri'}",
							},
        },
				offerBoxCasino: {
					    upperSentence: {
								success: "${properties.upCasSuccessText @ context='scriptString'}",
								used: "${properties.upCasUsedText @ context='scriptString'}",
								expired: "${properties.upCasExpiredText @ context='scriptString'}",
								error: "${properties.upCasErrorText @ context='scriptString'}",
							},
							message: {
									success: "${properties.successCasText @ context='scriptString'}",
									used: "${properties.usedCasText @ context='scriptString'}",
									error: "${properties.errorCasText @ context='scriptString'}",
									expired: "${properties.expiredCasText @ context='scriptString'}",
							},
							cta: {
									ctaText: "${properties.CTACaslabel @ context='unsafe'}",
									ctaScript: "${properties.ctaCasScript @ context='unsafe'}",
									CTAariaLabel: "${properties.CTACasariaLabel @ context='unsafe'}",
									ctaUrl: "${properties.CTACasurl @ context='uri'}",
							},
        },
				offerBoxSports: {
					    upperSentence: {
								success: "${properties.upSpoSuccessText @ context='scriptString'}",
								used: "${properties.upSpoUsedText @ context='scriptString'}",
								expired: "${properties.upSpoExpiredText @ context='scriptString'}",
								error: "${properties.upSpoErrorText @ context='scriptString'}",
							},
							message: {
									success: "${properties.successSpoText @ context='scriptString'}",
									used: "${properties.usedSpoText @ context='scriptString'}",
									error: "${properties.errorSpoText @ context='scriptString'}",
									expired: "${properties.expiredSpoText @ context='scriptString'}",
							},
							cta: {
									ctaText: "${properties.CTASpolabel @ context='unsafe'}",
									ctaScript: "${properties.ctaSpoScript @ context='unsafe'}",
									CTAariaLabel: "${properties.CTASpoariaLabel @ context='unsafe'}",
									ctaUrl: "${properties.CTASpourl @ context='uri'}",
							},
        },
    };
</script>

<sly data-sly-call="${clientlib.js @ categories='holdings888.poker-claim'}" />

<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-call="${clientlibs.fe @ locations='poker-claim-js'}"/>
