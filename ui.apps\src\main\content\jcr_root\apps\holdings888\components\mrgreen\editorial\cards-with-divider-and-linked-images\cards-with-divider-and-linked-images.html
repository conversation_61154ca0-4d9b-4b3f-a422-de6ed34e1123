<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html" />
<sly data-sly-test.hasContent="${true[<PERSON><PERSON><PERSON>]}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<div class="cards-with-divider-and-linked-images-cmp ${properties.paddingTop} ${properties.paddingBottom}">
    <div class="firstCard">
        <sly data-sly-resource="${'image1' @ resourceType='holdings888/components/editorial/image', decorationTagName='div'}"></sly>
        <div data-sly-resource="${'title1' @ resourceType='holdings888/components/editorial/title',decorationTagName = 'div'}"></div>
        <div data-sly-resource="${'text1' @ resourceType='holdings888/components/editorial/rich-text',decorationTagName = 'div'}"></div>
        <sly data-sly-use.image1link="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.card1image1link}" />
        <div class="images-wrapper">
            <a
                class="image-link"
                href="${image1link.relativePublishLink}"
                data-sly-unwrap="${!image1link.relativePublishLink}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly
                    data-sly-test="${!properties.properties.card1image1link}"
                    data-sly-call="${image.basic @ imagePath=properties.card1image1Reference, loading=properties.isFetchPriorityHigh, alt=properties.altCard1image1}"
            /></a>
            <sly data-sly-use.image2link="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.card1image2link}" />
            <a
                class="image-link"
                href="${image2link.relativePublishLink}"
                data-sly-unwrap="${!image2link.relativePublishLink}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly
                    data-sly-test="${!properties.properties.card1image2link}"
                    data-sly-call="${image.basic @ imagePath=properties.card1image2Reference, loading=properties.isFetchPriorityHigh, alt=properties.altCard1image2}" />
            </a>
        </div>
    </div>
    <div class="horizontal-divider" data-sly-test="${properties.enableTab2}"></div>
    <div class="secondCard" data-sly-test="${properties.enableTab2}">
        <sly data-sly-resource="${'image2' @ resourceType='holdings888/components/editorial/image', decorationTagName='div'}"></sly>
        <div data-sly-resource="${'title2' @ resourceType='holdings888/components/editorial/title',decorationTagName = 'div'}"></div>
        <div data-sly-resource="${'text2' @ resourceType='holdings888/components/editorial/rich-text',decorationTagName = 'div'}"></div>
        <sly data-sly-use.image3link="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.card2image1link}" />
        <div class="images-wrapper">
            <a
                class="image-link"
                href="${imag3link.relativePublishLink}"
                data-sly-unwrap="${!image3link.relativePublishLink}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly
                    data-sly-test="${!properties.properties.card2image1link}"
                    data-sly-call="${image.basic @ imagePath=properties.card2image1Reference, loading=properties.isFetchPriorityHigh,alt=properties.altCard2image1}" />
            </a>
            <sly data-sly-use.image4link="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.card2image2link}" />
            <a
                class="image-link"
                href="${image4link.relativePublishLink}"
                data-sly-unwrap="${!image4link.relativePublishLink}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly
                    data-sly-test="${!properties.properties.card2image2link}"
                    data-sly-call="${image.basic @ imagePath=properties.card2image2Reference, loading=properties.isFetchPriorityHigh,alt=properties.altCard2image2}" />
            </a>
        </div>
    </div>
</div>
