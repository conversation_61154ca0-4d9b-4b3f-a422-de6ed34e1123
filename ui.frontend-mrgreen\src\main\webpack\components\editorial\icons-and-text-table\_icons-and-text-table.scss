.icons-and-text-container {
    display: block;
    position: relative;
    padding: 0;
    background: $dark-2;
    border-radius: 1.2rem;
    clear: both;
    .grid-elements {
        position: relative;
        z-index: 0;
        max-width: 100%;
        width: 100%;
        padding: 0;
        margin: 0;
        .container-title{
            position: relative;
            z-index: 0;
            font-size: 1.6rem;
            text-transform: uppercase;
            background: transparent;
            line-height: inherit;
            margin: 0 auto ;
            max-width: fit-content;
            color: $white ;
            font-family: $font-family-2 ;
            border-bottom: 0.1rem solid $white ;
            padding: 4rem 0 1rem ;
            text-align: center;
        }
        .grid-wrapper {
            margin: 0;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            padding: 1.5em;
            @media (min-width: $sm-grid) {
                padding: 3em;
            }
            .grid-item {
                margin: 0;
                float: left;
                clear: both;
                width: 100%;
                padding: 1em 0;
                padding-left: 0;
                padding-right: 0;

                @media (min-width: $sm-grid) {
                    line-height: 1.5;
                    color: $white;
                    font-family: $font-family-2;
                    font-weight: $font-weight-semi-bold;
                    font-size: 1.5rem;
                    box-sizing: inherit;
                    margin: 0;
                    float: left;
                    clear: both;
                    width: 50%;
                    padding: 1em 0;
                }
                .wrapper {
                    margin: 0;
                    min-height: 0;
                    display: flex;
                    align-items: center;
                    padding: 0;
                    background: none;
                    border: 0;
                    height: auto;
                    @media (min-width: $sm-grid) {
                    line-height: 1.5;
                    color: $white;
                    font-family: $font-family-2;
                    font-weight: $font-weight-semi-bold;
                    font-size: 1.5rem;
                    box-sizing: inherit;
                    margin: 0;
                    min-height: 0;
                    display: flex;
                    align-items: center;
                    padding: 0;
                    background: none;
                    border: 0;
                    }
                    .image {
                        line-height: 1.5;
                        font-weight: $font-weight-semi-bold;
                        font-size: 1.5em;
                        box-sizing: inherit;
                        width: 5rem;
                        height: 5rem;
                        margin-right: 1em;

                        picture {
                            display: block;
                            margin: 0;
                            padding: 0;
                            text-align: center;
                            img {
                                border: 0;
                                max-width: 100%;
                                min-width: 5rem;
                                height: auto;
                                display: inline-block;
                                vertical-align: middle;
                            }
                        }

                        @media only screen and (max-width: 672px) {
                            width: 85px;
                        }
                    }
                    .data-container {
                        line-height: 1.5;
                        color: $white;
                        font-family: $font-family-2;
                        font-weight: $font-weight-semi-bold;
                        font-size: 1.5rem;
                        box-sizing: inherit;
                        margin: 0;
                        padding: 0;
                        width: 100%;
                        text-align: center;
                        .title {
                            line-height: 1.5;
                            font-family: $font-family-2;
                            box-sizing: inherit;
                            margin: 0;
                            padding: 0;
                            text-align: left;
                            color: $white;
                            font-weight: $font-weight-bold;
                            font-size: $font-size-14px !important;
                            text-transform: uppercase;
                        }
                        .text {
                            line-height: 1.5;
                            font-family: $font-family-7;
                            box-sizing: inherit;
                            margin: 0;
                            padding: 0;
                            text-align: left;
                        p {
                            color: $white;
                            font-weight: $font-weight-light;
                            font-size: $font-size-14px;
                        }

                        }
                    }
                }
            }
        }
    }
}
