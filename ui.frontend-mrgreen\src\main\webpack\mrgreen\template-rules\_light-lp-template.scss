.light-lp{
    h2.tctitle, h2 {
        font-family: $font-family-12;
        color: rgb(221, 220, 170);
        margin-block-start: 0.83em;
        margin-block-end: 0.83em;
    }
    ul li::before {
        color: #fff;
    }
    .basic-container {
        p,
        li,
        div {
            font-size: 15px;
            color: #d3d3d3;
        }
    }
    .acq-offer-terms p {
        font-family: $font-family-13;
        color: #DDDCAA;
        font-size: 10px;
        text-align: left;
    }
    .a {
        color: #41b169;
    }
    ol {
        margin-block-start: 1em;
        padding-block-end: 1em;
    }
    .hero-banner-light-lp .custom-banner .cta-button-container .cta-rte{
        max-width: 66%;
        width: 100%;
        @media only screen and (max-width: $sm-grid+1px) {
            max-width: 100%;
        }
    }

    .one-col-desktop-sport {
        .rich-text-component .text h2 {
            font-size: 1.8vw; 

            @media (max-width: 460px) {
                font-size: 2rem;
            }
        }
    }
}