<!--
 |  Copyright 2019 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>holdings888</groupId>
        <artifactId>holdings888</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N                                   -->
    <!-- ====================================================================== -->
    <artifactId>holdings888.all</artifactId>
    <packaging>content-package</packaging>
    <name>holdings888 - All</name>
    <description>All content package for holdings888</description>

    <!-- ====================================================================== -->
    <!-- B U I L D   D E F I N I T I O N                                        -->
    <!-- ====================================================================== -->
    <build>
        <plugins>
            <!-- ====================================================================== -->
            <!-- V A U L T   P A C K A G E   P L U G I N S                              -->
            <!-- ====================================================================== -->
            <plugin>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>filevault-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <group>holdings888</group>
                    <packageType>container</packageType>
                    <!-- skip sub package validation for now as some vendor packages like CIF apps will not pass -->
                    <skipSubPackageValidation>true</skipSubPackageValidation>
                    <embeddeds>
                        <embedded>
                            <groupId>holdings888</groupId>
                            <artifactId>holdings888.ui.apps</artifactId>
                            <type>zip</type>
                            <target>/apps/holdings888-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>holdings888</groupId>
                            <artifactId>holdings888.core</artifactId>
                            <target>/apps/holdings888-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>holdings888</groupId>
                            <artifactId>holdings888.ui.content</artifactId>
                            <type>zip</type>
                            <target>/apps/holdings888-packages/content/install</target>
                        </embedded>
                        <embedded>
                            <groupId>holdings888</groupId>
                            <artifactId>holdings888.ui.config</artifactId>
                            <type>zip</type>
                            <target>/apps/holdings888-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>org.jsoup</groupId>
                            <artifactId>jsoup</artifactId>
                            <target>/apps/holdings888-packages/application/install</target>
                            <type>jar</type>
                        </embedded>
                        <embedded>
                            <groupId>com.adobe.acs</groupId>
                            <artifactId>acs-aem-commons-all</artifactId>
                            <target>/apps/holdings888-vendor-packages/container/install</target>
                            <type>zip</type>
                            <classifier>cloud</classifier>
                            <filter>true</filter>
                            <isAllVersionsFilter>true</isAllVersionsFilter>
                        </embedded>

                        <!-- Groovy Console -->
                        <embedded>
                            <groupId>be.orbinson.aem</groupId>
                            <artifactId>aem-groovy-console-all</artifactId>
                            <type>zip</type>
                            <target>/apps/holdings888-vendor-packages/container/install</target>
                        </embedded>

                        <!-- Netcentric -->
                        <embedded>
                            <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
                            <artifactId>accesscontroltool-package</artifactId>
                            <classifier>cloud</classifier>
                            <type>content-package</type>
                            <target>/apps/holdings888-packages/application/install</target>
                        </embedded>
                        <embedded>
                            <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
                            <artifactId>accesscontroltool-oakindex-package</artifactId>
                            <classifier>cloud</classifier>
                            <type>zip</type>
                            <target>/apps/holdings888-packages/application/install</target>
                        </embedded>
                    </embeddeds>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.day.jcr.vault</groupId>
                <artifactId>content-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <verbose>true</verbose>
                    <failOnError>true</failOnError>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
                <executions>
                    <execution>
                        <id>auto-clean</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.adobe.aem</groupId>
                <artifactId>aemanalyser-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>aem-analyser</id>
                        <goals>
                            <goal>project-analyse</goal>                           
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- ====================================================================== -->
    <!-- P R O F I L E S                                                        -->
    <!-- ====================================================================== -->
    <profiles>
        <profile>
            <id>autoInstallSinglePackage</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.day.jcr.vault</groupId>
                        <artifactId>content-package-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-package</id>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>autoInstallSinglePackagePublish</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.day.jcr.vault</groupId>
                        <artifactId>content-package-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-package-publish</id>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <targetURL>http://${aem.publish.host}:${aem.publish.port}/crx/packmgr/service.jsp</targetURL>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
    </profile>
    </profiles>

    <!-- ====================================================================== -->
    <!-- D E P E N D E N C I E S                                                -->
    <!-- ====================================================================== -->
    <dependencies>
        <dependency>
            <groupId>holdings888</groupId>
            <artifactId>holdings888.ui.apps</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>holdings888</groupId>
            <artifactId>holdings888.ui.content</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>holdings888</groupId>
            <artifactId>holdings888.ui.config</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>
        <dependency>
            <groupId>com.adobe.acs</groupId>
            <artifactId>acs-aem-commons-all</artifactId>
            <version>6.3.2</version>
            <classifier>cloud</classifier>
            <type>zip</type>
        </dependency>
        <!-- Groovy Console -->
        <dependency>
            <groupId>be.orbinson.aem</groupId>
            <artifactId>aem-groovy-console-all</artifactId>
            <version>${groovy.console.version}</version>
            <type>zip</type>
        </dependency>

        <!-- Netcentric ACL Tool  -->
       <dependency>
           <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
           <artifactId>accesscontroltool-package</artifactId>
           <type>content-package</type>
           <classifier>cloud</classifier>
       </dependency>

        <!-- Oak Index for Netcentric ACL Tool -->
       <dependency>
           <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
           <artifactId>accesscontroltool-oakindex-package</artifactId>
           <type>zip</type>
           <classifier>cloud</classifier>
       </dependency>
   </dependencies>
</project>
