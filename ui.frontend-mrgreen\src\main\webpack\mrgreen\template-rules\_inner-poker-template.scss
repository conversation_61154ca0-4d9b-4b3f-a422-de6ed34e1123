.inner-poker-page{

    h1, h2, h3, h4, h5, a{
        color: $white;
        font-weight: $font-weight-semi-bold;
    }

    h1{
        font-size: 32px;
        line-height: normal;
        font-family: $font-family-4;
        margin: 30px 0;
        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 22px;
        }
        @media screen and (max-width: 1700px) {
            padding: 10px 0 15px 0px;
        }
    }
    h2{
        font-size: 30px;
        line-height: normal;
        font-family: $font-family-4;

        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 20px;
        }
    }

    h3{
        font-size: 27px;
        line-height: normal;
        font-family: $font-family-4;

        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 19px;
        }
    }
    h4{
        font-size: 24px;
        line-height: normal;
        font-family: $font-family-4;

        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 18px;
        }
    }

    h5{
        font-size: 18px;
        line-height: 27px;
        font-family: $font-family-8;

        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 12px;
            line-height: 20px;
        }
    }
    h6{
        font-size: 1rem;
        line-height: 1.4;
        margin-bottom: .5rem;
        font-family: $font-family-17;
        font-weight: $font-weight-regular;
    }
    p{
        color: $white;
        font-size: 18px;
        line-height: 27px;
        font-family: $font-family-8;
        font-weight: $font-weight-semi-bold;
        margin-bottom: 1rem;
        @media only screen and (max-width: $sm-grid+1px) {
            font-size: 12px;
            line-height: 20px;
        }
        b{
            font-family: $font-family-18;
        }
    }
    .image-component img{
        margin-bottom: 30px;
    }
    .main-container {
        width: 100%;
        padding: 50px;
        @media only screen and (max-width: $sm-grid+1px) {
            padding: 0 20px 20px 20px;
        }
    }
    .grid-component .column-container.two-columns-66-33 {

        .column:first-child{
            padding: 0;
            @media (min-width: $md +1px) {
                width: 75.66666667%;
            }
        }
        .column:last-child {
            @media (min-width: $md +1px) {
                width: 24.333333%;
            }
        }

        @media only screen and (max-width: $md +1px) {
            display: flex;
            flex-direction: column-reverse;
        }
    }
}