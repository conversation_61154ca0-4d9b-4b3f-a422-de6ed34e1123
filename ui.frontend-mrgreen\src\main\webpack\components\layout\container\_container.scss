.container {
    .black-bg {
        background-color: $black;
        p, span, ul li, ol li {
            color: $white;
            &::before {
                color: $white;
            }
        }
    }
    .dark-bg {
        background-color: $color-background-seo;
        p, span, ul li, ol li {
            color: $white;
            &::before {
                color: $white;
            }
        }
    }
    .dark-bg-non-white-text {
        background-color: $color-background-seo;
        p,
        span,
        ul li, ol li {
            color: unset;
        }
    }
    .white-bg {
        box-shadow: 0 0 1.1rem -1rem rgba(0, 0, 0, 0.75);
        background-color: $white;
        p,
        span,
        ul li, ol li {
            color: $black;
        }
    }
    .emerald-bg {
        background: radial-gradient(circle, $container-emerald-bg, rgba(18, 19, 20, 0.5));
        p,
        span,
        ul li, ol li {
            color: $white;
        }
        ul li:before {
            color: #41b169;
        }
    }
    .container-corner-radius {
        border-radius: 1.2rem;
    }
    .container-items-aligned-center {
        margin: 0 auto;
    }
    .container-full-width-padding-left-right {
        padding: 0 4rem;
    }
    .container-link {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 2;
    }
    .container-wrapper {
        .display-background {
            height: 100%;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
        }

        &.one-col-mobile,
        &.two-col-mobile,
        &.three-col-mobile,
        &.one-col-tablet,
        &.two-col-tablet,
        &.three-col-tablet,
        &.one-col-desktop,
        &.one-col-desktop-50,
        &.two-col-desktop,
        &.three-col-desktop {
            .cmp-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        &.one-col-desktop-50 {
            @media screen and (max-width: $sm-grid) {
                padding: 0rem 1rem;
                .cmp-container {
                    display: block !important;
                }
            }
            .cmp-container {
                align-items: center;
                justify-content: flex-start;
            }
            .cmp-container > div {
                margin: 0 auto;
            }
            &.main-container {
                width: 100%;
            }
        }

        &.one-col-desktop-sport {
            width: 100%;

            @media screen and (min-width: 514px) {
                max-width: 70%;
                padding: 0 0 4rem;
                margin: 0 auto;
                width: 100%;
            }

            .cmp-container {
                padding: 0 1.5rem;

                @media screen and (max-width: 514px) {
                    padding: 0;
                }
            }

        }

        //(MOBILE
        &.one-col-mobile {
            .cmp-container > div {
                width: 100%;
            }
        }
        &.two-col-mobile {
            .cmp-container > div {
                width: 50%;
            }
        }
        &.three-col-mobile {
            .cmp-container > div {
                width: 33.3333%;
            }
        }

        // TABLET
        &.one-col-tablet {
            .cmp-container > div {
                @media (min-width: $sm) {
                    width: 100%;
                }
            }
        }
        &.two-col-tablet {
            .cmp-container > div {
                @media (min-width: $sm) {
                    width: 50%;
                }
            }
        }
        &.three-col-tablet {
            .cmp-container > div {
                @media (min-width: $sm) {
                    width: 3.3333%;
                }
            }
        }

        // DESKTOP
        &.one-col-desktop {
            .cmp-container > div {
                @media (min-width: $md) {
                    width: 100%;
                }
            }
        }
        &.one-col-desktop-50 {
            .cmp-container > div:not(.header-bar-light-lp) {
                @media (min-width: $sm-grid) {
                    width: 50.1%;
                    h1,
                    h2 {
                        text-align: left;
                    }
                }
            }
        }
        &.two-col-desktop {
            .cmp-container > div {
                @media (min-width: $md) {
                    width: 50%;
                }
            }
        }

        &.three-col-desktop {
            .cmp-container > div {
                @media (min-width: $md) {
                    width: 33.3333%;
                }
            }
        }
    }
}
