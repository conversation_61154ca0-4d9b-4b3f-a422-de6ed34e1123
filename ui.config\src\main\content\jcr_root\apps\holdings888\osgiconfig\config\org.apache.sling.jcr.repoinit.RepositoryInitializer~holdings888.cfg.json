{"scripts": ["create path (sling:OrderedFolder) /content/dam/holdings888", "create path (nt:unstructured) /content/dam/holdings888/jcr:content", "create path (nt:unstructured) /content/dam/holdings888/888poker/jcr:content", "create path (nt:unstructured) /content/dam/holdings888/888casino/jcr:content", "create path (nt:unstructured) /content/dam/holdings888/777/jcr:content", "create path (nt:unstructured) /content/dam/holdings888/888sport/jcr:content", "set properties on /content/dam/holdings888/888poker/jcr:content\n set cq:conf{String} to /conf/888poker\n  set jcr:title{String} to \"888poker\" \nend", "set properties on /content/dam/holdings888/888casino/jcr:content\n set cq:conf{String} to /conf/888casino\n  set jcr:title{String} to \"888casino\"\nend", "set properties on /content/dam/holdings888/777/jcr:content\n set cq:conf{String} to /conf/777\n  set jcr:title{String} to \"777\"\nend", "set properties on /content/dam/holdings888/888sport/jcr:content\n set cq:conf{String} to /conf/888sport\n  set jcr:title{String} to \"888sport\"\nend", "create path (sling:OrderedFolder) /content/dam/mrgreen", "create path (nt:unstructured) /content/dam/mrgreen/jcr:content", "set properties on /content/dam/mrgreen/jcr:content\n set cq:conf{String} to /conf/mrgreen\n  set jcr:title{String} to \"mrgreen\"\nend", "create service user account-service-888\n set ACL for account-service-888\n allow jcr:read,crx:replicate on /content \n allow jcr:read on /conf \n allow jcr:write on /content/experience-fragments \nend"]}