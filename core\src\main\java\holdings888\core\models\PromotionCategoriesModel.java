package holdings888.core.models;

import com.day.cq.search.PredicateGroup;
import com.day.cq.search.Query;
import com.day.cq.search.QueryBuilder;
import com.day.cq.search.result.Hit;
import com.day.cq.search.result.SearchResult;
import com.day.cq.wcm.api.Page;
import holdings888.core.bean.AudiencePromotionItem;
import holdings888.core.bean.CasinoPromotionItem;
import holdings888.core.utils.DateUtils;
import holdings888.core.utils.PageUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.ChildResource;
import org.apache.sling.models.annotations.injectorspecific.ScriptVariable;
import org.apache.sling.models.annotations.injectorspecific.SlingObject;

import javax.annotation.PostConstruct;
import javax.jcr.RepositoryException;
import javax.jcr.Session;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.day.cq.wcm.api.constants.NameConstants.NT_PAGE;
import static holdings888.core.utils.Constants.P_LIMIT;

@Slf4j
@Model(adaptables = {SlingHttpServletRequest.class}, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class PromotionCategoriesModel {
    @SlingObject
    private ResourceResolver resourceResolver;

    @ChildResource(name = "listItems")
    private final List<Resource> categoriesList = new ArrayList<>();

    @Getter
    private final Map<String, List<String>> promotionsPathsMap = new HashMap<>();

    @ScriptVariable
    Page currentPage;

    @ChildResource(name = "audience")
    String audience = StringUtils.EMPTY;

    @ChildResource(name = "category")
    String category =  StringUtils.EMPTY;
    @Getter
    private final List<CasinoPromotionItem> promotionList = new ArrayList<>();

    @PostConstruct
    protected void init() throws RepositoryException {
        String path = currentPage.getPath();
        List<Hit> pathOfPromoList = new ArrayList<>();
        if (path.contains("experience-fragments")){
            path = initXF(path, resourceResolver);
            List<String> promotionLobbyCategories = retrievePromotionLobbyCategories(resourceResolver, path);

            if(StringUtils.isNotBlank(audience)) {
                for(String cat : promotionLobbyCategories) {
                    category = cat;
                    pathOfPromoList = getPathsXF(resourceResolver, path);
                    pathOfPromoList = DateUtils.filterPromoListByDate(pathOfPromoList,resourceResolver);
                    createPageList(pathOfPromoList);
                }
            }
        }else {
            List<String> promotionLobbyCategories = retrievePromotionLobbyCategories(resourceResolver, path);
            for(String cat : promotionLobbyCategories) {
                category = cat;
                pathOfPromoList = getPaths(resourceResolver, path);
                createPageList(pathOfPromoList);
            }
        }

    }

    /*
     * It finds Categories for Promotion Lobby page
     *
     * @return the hits list
     */
    private List<String> retrievePromotionLobbyCategories(ResourceResolver resourceResolver, String path) {
        List<String> categoryItems = new ArrayList<>();

        Resource resource = resourceResolver.getResource(path + "/jcr:content/categories");

        if (resource != null) {
            Iterator<Resource> itemsIterator = resource.listChildren();

            while (itemsIterator.hasNext()) {
                Resource item = itemsIterator.next();
                ValueMap properties = item.getValueMap();
                String itemValue = properties.get("category", String.class);

                if (itemValue != null) {
                    categoryItems.add(itemValue);
                }
            }
        }

        return categoryItems;
    }

    /*
     * It finds pages with promotion templates and specific category
     *
     * @return the hits list
     */
    protected List<Hit> getPaths(ResourceResolver resourceResolver, String path) {
        QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", path);
        map.put(P_LIMIT, String.valueOf(-1));
        map.put("1_property", "jcr:content/category");
        map.put("1_property.value", category);
        Query query = queryBuilder.createQuery(PredicateGroup.create(map),
                resourceResolver.adaptTo(Session.class));
        log.debug("PATHS QUERY: '{}'", query.getPredicates());
        SearchResult results = query.getResult();
        return results.getHits();
    }

    /*
     * It finds pages with promotion templates with specific Category & audience
     *
     * @return the hits list
     */
    public List<Hit> getPathsXF(ResourceResolver resourceResolver, String path) {
        QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", path);
        map.put("1_property", "jcr:content/audiences/*/audience");
        map.put("1_property.value", audience);
        map.put("2_property", "jcr:content/category");
        map.put("2_property.value", category);

        map.put(P_LIMIT, String.valueOf(-1));

        Query query = queryBuilder.createQuery(PredicateGroup.create(map),
                resourceResolver.adaptTo(Session.class));
        log.debug("PATHS QUERY: '{}'", query.getPredicates());
        SearchResult results = query.getResult();
        return results.getHits();
    }
    private void createPageList(List<Hit> pathOfPromoList) throws RepositoryException {

        List<CasinoPromotionItem> categoryPromotionList = new ArrayList<>();

        for (Hit hit : pathOfPromoList) {
            Resource pathResource = resourceResolver.getResource(hit.getPath());
            if (pathResource != null) {
                Page promoPage = pathResource.adaptTo(Page.class);
                if (promoPage != null) {
                    CasinoPromotionItem promotionItem = promoPage.getContentResource().adaptTo(CasinoPromotionItem.class);
                    if (promotionItem != null) {
                        setOrder(promotionItem);
                        categoryPromotionList.add(promotionItem);
                    }
                }
            }
        }
        List<String> categoryPagePath = categoryPromotionList.stream()
                .sorted(Comparator.comparing(CasinoPromotionItem::getOrder))
                .map(item -> item.getUrl() + "/jcr:content/root")
                .collect(Collectors.toList());

        promotionsPathsMap.put(category, categoryPagePath);

    }

    private void setOrder( CasinoPromotionItem promotionItem) {
        List<AudiencePromotionItem> audiences = promotionItem.getAudiences();
        if(audiences != null){
            if (audience.isBlank()) {
                promotionItem.setOrder(audiences.get(0).getOrderXf());
            } else {
                for (AudiencePromotionItem aud : audiences) {
                    if (audience.equals(aud.getAudience())) {
                        promotionItem.setOrder(aud.getOrderXf());
                    }
                }
            }
        }
    }

    private String initXF(String path, ResourceResolver resourceResolver) {
        log.debug("[888] - [PromotionCategoriesModel] - doGet");
        path = PageUtils.findPromotionPagePathForXF(path, resourceResolver);
        return path;
    }

    public boolean isSingleCategory() {
        return categoriesList.size() == 1;
    }
}
