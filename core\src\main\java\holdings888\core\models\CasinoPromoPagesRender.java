package holdings888.core.models;

import com.day.cq.search.PredicateGroup;
import com.day.cq.search.Query;
import com.day.cq.search.QueryBuilder;
import com.day.cq.search.result.Hit;
import com.day.cq.search.result.SearchResult;
import com.day.cq.wcm.api.Page;

import holdings888.core.bean.AudiencePromotionItem;
import holdings888.core.bean.CasinoPromotionItem;
import holdings888.core.utils.DateUtils;
import holdings888.core.utils.LinkUtils;
import holdings888.core.utils.PageUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.ChildResource;
import org.apache.sling.models.annotations.injectorspecific.ScriptVariable;
import org.apache.sling.models.annotations.injectorspecific.SlingObject;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.jcr.RepositoryException;
import javax.jcr.Session;
import java.math.BigDecimal;
import java.util.*;

import static holdings888.core.utils.Constants.*;
import static com.day.cq.wcm.api.constants.NameConstants.NT_PAGE;
import static holdings888.core.utils.PageUtils.extractLocalBrand;

/**
 * The type Check promo model retrieves all promotions starting from the page
 * where
 * the component was inserted or from custom path
 */
@Slf4j
@Model(adaptables = {SlingHttpServletRequest.class}, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class CasinoPromoPagesRender {

    @ScriptVariable
    Page currentPage;

    @ChildResource(name = "audience")
    String audience = StringUtils.EMPTY;

    @ChildResource(name = "category")
    String category =  StringUtils.EMPTY;


    @Getter
    @Inject
    private List<String> categories = new ArrayList<>();

    @Getter
    @Inject
    private List<String> audiences = new ArrayList<>();

    @ChildResource(name = "promotions")
    private final List<CasinoPromotionItem> editorialPromotionList = new ArrayList<>();
    @SlingObject
    private ResourceResolver resourceResolver;
    @Inject
    SlingHttpServletRequest slingHttpServletRequest;
    @Getter
    private final List<CasinoPromotionItem> promotionList = new ArrayList<>();

    /**
     * Init method retrieves all the promotions present in the site
     * and puts at the beginning of a list the manual edited promotions
     *
     * @throws RepositoryException the repository exception
     */
    @PostConstruct
    protected void init() throws RepositoryException {
        String path = currentPage.getPath();
        boolean isMRG = StringUtils.contains(extractLocalBrand(path), MRGREEN);
        String lobbyPath = String.valueOf(path);
        List<Hit> pathOfPromoList = new ArrayList<>();
        boolean isXf= false;
        if (!PageUtils.isPromoLobbyPage(currentPage)) {
            lobbyPath = PageUtils.findPromotionPagePath(path, resourceResolver);
        }
        if(path.contains("experience-fragments")){
            isXf= true; // for promo ordering
            if (isMRG) {
                List<String> promotionLobbyPagePaths = PageUtils.findPromotionPagePathsForXF(path, resourceResolver);
                for (String promoPath : promotionLobbyPagePaths) {
                    pathOfPromoList.addAll(getHits(pathOfPromoList, promoPath));
                }
            } else {
                lobbyPath = PageUtils.findPromotionPagePathForXF(path, resourceResolver);
                pathOfPromoList = getHits(pathOfPromoList, lobbyPath);
            }

        } else {
            if (isMRG) {
                List<String> promotionLobbyPagePaths = PageUtils.findPromotionPagePaths(path, resourceResolver);
                for (String promoPath : promotionLobbyPagePaths) {
                    pathOfPromoList.addAll(getPaths(resourceResolver, promoPath));
                }
            } else {
                pathOfPromoList = getPaths(resourceResolver, lobbyPath);
            }
        }
        createPageList(pathOfPromoList,isXf);
        this.promotionList.sort(Comparator.comparingInt(CasinoPromotionItem::getOrder));

    }

    private List<Hit> getHits(List<Hit> pathOfPromoList, String lobbyPath) {
        if(StringUtils.isNotBlank(audience) && StringUtils.isNotBlank(category)) {
            pathOfPromoList = getPathsXF(resourceResolver, lobbyPath);
            pathOfPromoList = DateUtils.filterPromoListByDate(pathOfPromoList,resourceResolver);
        }
        return pathOfPromoList;
    }

    /*
     * It finds pages with promotion templates and specific category
     *
     * @return the hits list
     */
    private List<Hit> getPaths(ResourceResolver resourceResolver, String path) {
        QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", path);
        map.put(P_LIMIT, String.valueOf(-1));
        map.put("1_property", "jcr:content/category");
        map.put("1_property.value", category);
        map.put("2_property", "jcr:content/order");
        map.put("orderby", "2_property.value");

        Query query = queryBuilder.createQuery(PredicateGroup.create(map),
                resourceResolver.adaptTo(Session.class));
        log.debug("PATHS QUERY: '{}'", query.getPredicates());
        SearchResult results = query.getResult();
        return results.getHits();
    }

    /*
     * It finds pages with promotion templates with specific Category & audience
     *
     * @return the hits list
     */
    public List<Hit> getPathsXF(ResourceResolver resourceResolver, String path) {
        QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", path);
        map.put("1_property", "jcr:content/audiences/*/audience");
        map.put("1_property.value", audience);
        map.put("2_property", "jcr:content/category");
        map.put("2_property.value", category);
        map.put("3_property", "jcr:content/audiences/*/orderXf");
        map.put("orderby", "3_property.value");

        map.put(P_LIMIT, String.valueOf(-1));

        Query query = queryBuilder.createQuery(PredicateGroup.create(map),
                resourceResolver.adaptTo(Session.class));
        log.debug("PATHS QUERY: '{}'", query.getPredicates());
        SearchResult results = query.getResult();
        return results.getHits();
    }
    private void createPageList(List<Hit> pathOfPromoList, boolean isXf) throws RepositoryException {

        for (CasinoPromotionItem promo : editorialPromotionList) {
            setLinks(promo);
            promotionList.add(promo);
        }

        for (Hit hit : pathOfPromoList) {
            Resource pathResource = resourceResolver.getResource(hit.getPath());
            if (pathResource != null) {
                Page promoPage = pathResource.adaptTo(Page.class);
                if (promoPage != null) {
                    CasinoPromotionItem promotionItem = promoPage.getContentResource().adaptTo(CasinoPromotionItem.class);
                    if (promotionItem != null) {
                        setLinks(promotionItem);
                        setOrder(isXf, promotionItem);
                        promotionList.add(promotionItem);
                    }
                }
            }
        }
    }

    private void setOrder(boolean isXf, CasinoPromotionItem promotionItem) {
        if(isXf && promotionItem.getAudiences() != null){
            for(AudiencePromotionItem aud:promotionItem.getAudiences()){
                if(audience.equals(aud.getAudience())){
                    promotionItem.setOrder(aud.getOrderXf());
                }
            }
        }
    }

    private void setLinks(CasinoPromotionItem promotionItem) {
        promotionItem.setLink(LinkUtils.initializeLink(promotionItem.getUrl(), null,
                resourceResolver, slingHttpServletRequest));
        promotionItem.setTopLinkUrl(LinkUtils.initializeLink(promotionItem.getTopLink(), null,
                resourceResolver, slingHttpServletRequest));
        promotionItem.setTopCtaLinkUrl(LinkUtils.initializeLink(promotionItem.getTopCtaLink(), null,
                resourceResolver, slingHttpServletRequest));
        if (StringUtils.isNotEmpty(promotionItem.getBottomCtaLink())) {
            promotionItem.setBottomCtaLinkUrl(LinkUtils.initializeLink(promotionItem.getBottomCtaLink(), null,
                    resourceResolver, slingHttpServletRequest));
        }
        if (StringUtils.isNotEmpty(promotionItem.getDisclaimerLink())) {
            promotionItem.setDisclaimerLinkUrl(LinkUtils.initializeLink(promotionItem.getDisclaimerLink(), null,
                    resourceResolver, slingHttpServletRequest));
        }
    }

    public boolean isEmpty() {
        return promotionList.isEmpty();
    }
}
