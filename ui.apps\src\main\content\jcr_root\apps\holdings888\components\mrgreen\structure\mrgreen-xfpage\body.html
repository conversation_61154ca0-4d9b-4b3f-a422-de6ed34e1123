<sly data-sly-use.body="body.js" data-sly-use.templatedContainer="com.day.cq.wcm.foundation.TemplatedContainer"/>
<sly data-sly-test="${!templatedContainer.hasStructureSupport}"
     data-sly-resource="${body.resourcePath @ resourceType='wcm/foundation/components/parsys'}"/>
<div class="container">
    <link href="https://webassets.images4us.com/fonts/fonts.css?v=2.4.617" rel="preload stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <!-- Add Back Top-Section ClientLib XF -->
    <sly data-sly-use.clientlib="core/wcm/components/commons/v1/templates/clientlib.html">
        <sly data-sly-call="${clientlib.css @ categories='holdings888.mrgreen-top-section'}"/>
        <sly data-sly-call="${clientlib.js @ categories='holdings888.mrgreen-top-section', defer=true}"/>
    </sly>
    <sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
    <sly data-sly-call="${clientlibs.fe @ locations='mrgreen-top-section-css,mrgreen-top-section-js,mrgreen-vendors-js'}"/>
    <!-- END ClientLib XF -->

    <sly data-sly-test="${templatedContainer.hasStructureSupport}"
         data-sly-repeat.child="${templatedContainer.structureResources}"
         data-sly-resource="${child.path @ resourceType=child.resourceType, decorationTagName='div'}"/>
</div>
<div data-sly-resource="${'cloudservices' @ resourceType='cq/cloudserviceconfigs/components/servicecomponents'}"
     data-sly-unwrap></div>
<sly data-sly-include="footerlibs.html"/>