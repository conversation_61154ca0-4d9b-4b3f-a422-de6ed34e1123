<!--/* Image dialog with alt text and fetchpriority='high' checkbox
        USAGE:
        1. Add the following to the dialog:
        <image
                jcr:primaryType="nt:unstructured"
                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                sling:resourceType="acs-commons/granite/ui/components/include">
                <parameters
                        jcr:primaryType="nt:unstructured"
                        imageLabel="Image"
                        imageDescription="Image Upload"
                        imagePrefixName="image"
                        imageIsRequired="{Boolean}true"
                        altName="alt"
                        hideIsFetchPriorityHigh="{Boolean}false"
                        isFetchPriorityHighName="isFetchPriorityHigh"/>
        </image>
        2. Refer to /apps/holdings888/clientlibs/clientilib-dialog/clientlib-altText/js/altText.js clientlib
        for other steps to handle the alt text default value auto processing.

        Parameters (optional node) for overriding the default values:
        - imageLabel: Label of the image field
        - imageDescription: Description of the image field
        - imagePrefixName: Prefix for the image field names
        - imageIsRequired: Whether the images are required
        - altName: Name of the alt text field
        - isFetchPriorityHighName: Name of the checkbox for setting fetchpriority='high'
        - hideIsFetchPriorityHigh: Whether to hide the checkbox for setting fetchpriority='high'
*/-->
<jcr:root
        xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        jcr:primaryType="nt:unstructured"
        granite:class="cmp-image__editor-image-with-alt"
        sling:resourceType="granite/ui/components/coral/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <imageUpload
                jcr:primaryType="nt:unstructured"
                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                granite:class="cmp-image__editor-file-upload"
                allowUpload="{Boolean}false"
                autoStart="{Boolean}false"
                class="cq-droptarget"
                fieldLabel="${{imageLabel:Image}}"
                fieldDescription="${{imageDescription:Image Upload}}"
                fileNameParameter="./${{imagePrefixName:image}}Name"
                fileReferenceParameter="./${{imagePrefixName:image}}Reference"
                mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml,image/webp]"
                multiple="{Boolean}false"
                name="./${{imagePrefixName:image}}"
                uploadUrl="${suffix.path}"
                useHTML5="{Boolean}true"
                required="${{(Boolean)imageIsRequired:false}}"/>
        <alt
                granite:class="cmp-image__editor-alt-text"
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                fieldDescription="Textual alternative of the meaning or function of the image, for visually impaired readers."
                fieldLabel="Alt text"
                name="./${{altName:alt}}"
                required="${{(Boolean)imageIsRequired:false}}" />
        <isFetchPriorityHighContainer
                jcr:primaryType="nt:unstructured"
                granite:hidden="${{(Boolean)hideIsFetchPriorityHigh:false}}"
                sling:resourceType="granite/ui/components/coral/foundation/container">
            <items jcr:primaryType="nt:unstructured">
                <isFetchPriorityHigh
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                        text="Attribute fetchpriority='high'"
                        fieldDescription="When checked, add isFetchPriority='high' property and delete loading='lazy'(default) attribute."
                        name="./${{isFetchPriorityHighName:isFetchPriorityHigh}}"
                        granite:hidden="${{(Boolean)hideIsFetchPriorityHigh:false}}"
                        value="{Boolean}true"
                        uncheckedValue="false" />
            </items>
        </isFetchPriorityHighContainer>
    </items>
</jcr:root>