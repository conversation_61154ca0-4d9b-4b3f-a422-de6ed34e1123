.promotions-teaser-component {
    scroll-snap-align: start;
    border-radius: 0.8rem;
    display: flex;
    width: 100%;
    flex: 0 0 40%;
    @media (max-width: $md-max) {
        flex: 0 0 49%;
    }
    @media (max-width: $sm-air) {
        flex: 0 0 48.76%;
    }
    .promotions-teaser-wrapper {
        color: $white;
        font-weight: 600;
        font-size: 1.5rem;
        margin: 0;
        width: 100%;
        box-sizing: border-box;
        flex-direction: column;
        padding: 0.9375rem;
        background-color: #fcf6d5;
        .promotions-teaser {
            &__img {
                line-height: 1.5;
                color: $white;
                box-sizing: inherit;
                border: 0;
                max-width: 100%;
                height: auto;
                display: inline-block;
                vertical-align: middle;
                width: 100%;
                img {
                    width: 100%;
                    height: auto;
                }
            }
            &__bot {
                line-height: 1.5;
                color: $white;
                font-weight: $font-weight-medium;
                font-size: 1.5rem;
                box-sizing: inherit;
                margin: 0;
                width: 100%;
                display: grid;
                grid-auto-rows: 0.1fr 0.1fr 0.1fr;

                &__text {
                    @media screen and (min-width: 1600px) {
                        height: calc(85px + 1rem);
                    }
                    @media screen and (min-width: 1121px) and (max-width: 1599px) {
                        height: calc(88px + 1rem);
                    }
                    @media screen and (min-width: 769px) and (max-width: 1120px) {
                        height: calc(115px + 1rem);
                    }
                    @media screen and (min-width: 769px) and (max-width: 920px) {
                        height: calc(140px + 1rem);
                    }
                    h3 {
                        line-height: 1.5;
                        color: $white;
                        box-sizing: inherit;
                        width: 100%;
                        display: block;
                        padding-right: 0.25em;

                        margin-top: 1rem;
                        color: #bc3554;
                        text-transform: uppercase;
                        font-size: 2rem;
                        font-weight: 600;
                    }
                    p {
                        color: $color-font;
                        font-size: 2rem;
                        line-height: 1.5;
                        font-weight: 400;
                    }
                }
                &__cta-section {
                    line-height: 1.5;
                    color: $white;
                    font-weight: $font-weight-medium;
                    font-size: 1.2rem;
                    box-sizing: inherit;

                    @media (min-width: $sm) {
                        font-size: 1.5rem;
                    }
                    .cta-component {
                        display: flex;
                        gap: 1rem;
                        margin: 1.5rem 0;

                        a {
                            font-size: 15px;
                            min-width: 160px;
                            min-height: 35px;
                            width: 100%;
                            align-items: center;
                            padding: 1.6rem 0;
                            @media (max-width: $md-max) {
                                font-size: 12px;
                                line-height: 12px;
                            }
                            @media (max-width: $sm) {
                                padding: 0;
                                font-size: 1rem;
                                min-width: 120px;
                                min-height: 31px;
                            }
                        }
                    }
                }
            }
        }
        .disclaimer {
            line-height: 1.6;
            font-weight: 200;
            box-sizing: inherit;
            color: #4c1723;
            font-size: 1.9rem;
            font-family: $font-family;
            margin-top: 0.1rem;
            a {
                font-weight: 600;
                font-size: 1rem;
                font-family: $font-family-2;
                box-sizing: inherit;
                line-height: inherit;
                outline: 0;
                box-shadow: none;
                background-color: transparent;
                cursor: pointer;
                color: #fff;
                text-decoration: underline;
                @media (min-width: $sm) {
                    font-size: 1.1rem;
                }
            }
            p {
                padding: 1rem;
                line-height: 1.2;
                font-weight: 400;
                a {
                    color: $red;
                    font-weight: 400;
                }
            }
        }
    }
}
