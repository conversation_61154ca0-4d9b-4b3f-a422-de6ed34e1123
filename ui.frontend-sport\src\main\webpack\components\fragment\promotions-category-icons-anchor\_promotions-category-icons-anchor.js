function handleAnchorClick($pci) {
    const innerLinks = $pci.querySelectorAll(".category-element__innerLink");
    if (innerLinks) {
        innerLinks.forEach(link => {
            link.addEventListener("click", (e) => {
                e.preventDefault();
                link.classList.toggle("active");
                innerLinks.forEach(innerLink => {
                    if (innerLink !== link) {
                        innerLink.classList.remove("active");
                    }
                });
            });
        });
    }

    const ucObserver = new MutationObserver((mutationsList, observer) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                const ucContent = document.querySelector(".uc-content-area");
                // When the uc-content-area is loaded, we can update the anchor component top and handle scrolling
                if (ucContent) {
                    updateAnchorCmpTop();
                    handleScrolling();
                    observer.disconnect();
                    break;
                }
            }
        }
    });

    ucObserver.observe(document.body, { childList: true, subtree: true });

    function updateAnchorCmpTop() {
        const header = document.querySelector(".uc-main-header");
        const anchorCmp = document.querySelector(".promotions-category-icons-anchor-cmp.fixedCategories");
        if (anchorCmp) {
            const resizeObserver = new ResizeObserver(() => {
                let headerHeight = header.clientHeight;
                anchorCmp.style.top = `${headerHeight}px`;
            });

            resizeObserver.observe(header);

            // Initial set
            // offsetTop could be not 0 if there is an extra sticky header
            let initialHeaderHeight = header.clientHeight;
            anchorCmp.style.top = `${initialHeaderHeight}px`;
        }
    }

    function handleScrolling() {
        const linksContent = $pci.querySelectorAll(".promotions-category-grid");
        if (linksContent) {
            let scrollContainer = window;
            if (isNotFullWindowScroll()) {
                scrollContainer = document.querySelector('.uc-content-area');
            }
            scrollContainer.addEventListener("scroll", () => {
                linksContent.forEach(content => {
                    let id = content.id;
                    let link = $pci.querySelector('[href="#' + id + '"]');

                    if (verifyIfElementInViewport(content)) {
                        console.debug("id : " + id + " in viewport");
                        link.classList.add("active");
                    } else {
                        link.classList.remove("active");
                        link.hover = false;
                    }
                });
            }, {passive: true});
        }
    }

    function verifyIfElementInViewport(element) {
        let elementTop = element.offsetTop;
        let elementHeight = element.offsetHeight;
        let scrollContainer = document.documentElement;
        if (isNotFullWindowScroll()) {
            scrollContainer = document.querySelector('.uc-content-area');
        }
        let scrollTop = scrollContainer.scrollTop;

        for (let offsetParent = element.offsetParent; offsetParent; ) {
            elementTop += offsetParent.offsetTop;
            offsetParent = offsetParent.offsetParent;
        }

        return elementTop < scrollTop + window.innerHeight / 2 &&
            elementTop + elementHeight / 2 > scrollTop;
    }

    function isNotFullWindowScroll() {
        let sidebar = document.querySelector('.accountAreaWrapper');
        return window.innerWidth >= 1280 && sidebar;
    }
}


document.querySelectorAll(".promotions-category-container").forEach($pci => {
    handleAnchorClick($pci);
});



// CARDS EQUALIZER
function promotionsCardsEqualize() {
    const categories = ['sport', 'casino', 'racing', 'poker'];
    categories.forEach(category => {
        let classCards = ".promotions-category-grid." + category + " .promotions-card-component .promoCr";
        const cards = document.querySelectorAll(classCards);

        if(cards){
            cards.forEach(card => card.style.height = 'auto');
            for (let i = 0; i < cards.length; i += 2) {
                const card1 = cards[i];
                const card2 = cards[i + 1];

                if (card2) {
                    const maxHeight = Math.max(card1.clientHeight, card2.clientHeight);
                    card1.style.minHeight = maxHeight + "px";
                    card2.style.minHeight = maxHeight + "px";
                }
            }
        }

    });
}

function waitForPageRemovalAndEqualize() {
    const targetId = "page";

    const observer = new MutationObserver((mutationsList, observer) => {
        const pageElement = document.getElementById(targetId);
        if (!pageElement) {
            promotionsCardsEqualize();
            observer.disconnect();
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    if (!document.getElementById(targetId)) {
        promotionsCardsEqualize();
        observer.disconnect();
    }
}


function promotionsCardsResizeListener() {
    const targetElement = document.querySelector('.promotions-carousel');
    if (targetElement) {
        let timeoutId;
        waitForPageRemovalAndEqualize();
        const onResize = () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                promotionsCardsEqualize();
            }, 300);
        };

        window.addEventListener('resize', onResize);
    }
}

promotionsCardsResizeListener();
