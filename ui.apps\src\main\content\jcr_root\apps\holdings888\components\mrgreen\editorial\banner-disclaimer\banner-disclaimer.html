<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${properties.bannerText}" />
<sly data-sly-test.hasContent2="${properties.freeHTML}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}" />

<sly data-mbox-id="${properties.mboxId}">
    <!-- <sly data-sly-test="${disclaimerBanner.disclaimerVisibility || wcmmode.edit}"> -->
    <div
        class="banner-disclaimer-component js-banner-disclaimer-component ${properties.bannerStyle} ${properties.disableSticky ? 'disableSticky-tablet' : ''}"
        data-mbox-id="${properties.mboxId}">
        <sly data-sly-test="${properties.bannerStyle == 'ctaVariation'}">
            <div
                class="cta-container"
                data-sly-test="${properties.ctaLabel ? properties.ctaPosition == 'cta-above' : false}">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.ctaUrl}" />
                <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScriptExtra}" />

                <div class="cta-template ${properties.typeExtra ? properties.typeExtra : 'cta-glow'}">
                    <a
                        href="${ctaLink.relativePublishLink @ context='unsafe'}"
                        onclick="${scriptProcessor.processedScript @ context='unsafe'}"
                        data-sly-attribute.aria-label="${properties.ctaAriaLabel}"
                        data-sly-attribute.target="${properties.ctaNewWindow ? '_blank':''}"
                        data-sly-attribute="${attributes ? attributes : autoList.getAttributes}">
                        <span>${properties.ctaLabel}</span>
                        <sly data-sly-test="${properties.typeExtra == 'cta-blog-grid'}">
                            <span class="blog-arrow right"></span>
                        </sly>
                    </a>
                </div>
            </div>
        </sly>
        <sly data-sly-test="${hasContent}">
            <sly data-sly-use.disclaimerBanner="${'holdings888.core.models.DisclaimerBannerModel'}" />
            <div
                class="disclaimer-container ${(properties.bannerStyle =='promoStyle' || properties.bannerStyle =='disclaimerInnerPromotionPage' || properties.bannerStyle =='ctaVariation') ? 'disclaimer-main-container' : ''}">
                <div
                    class="disclaimer-content"
                    data-sly-test="${hasContent}">
                    ${disclaimerBanner.bannerText @ context='html'}
                </div>
                <div
                    class="disclaimer-content free-html-component"
                    data-sly-test="${hasContent2}">
                    ${properties.freeHTML @ context='unsafe'} ${properties.freeCSS @ context='unsafe'} ${properties.freeJavascript @ context='unsafe'}
                </div>
            </div>
        </sly>
        <sly data-sly-test="${!hasContent}">
            <div
                class="disclaimer-container ${(properties.bannerStyle =='promoStyle' || properties.bannerStyle =='disclaimerInnerPromotionPage' || properties.bannerStyle =='ctaVariation') ? 'disclaimer-main-container' : ''}">
                <div
                    class="disclaimer-content free-html-component"
                    data-sly-test="${hasContent2}">
                    ${properties.freeHTML @ context='unsafe'} ${properties.freeCSS @ context='unsafe'} ${properties.freeJavascript @ context='unsafe'}
                </div>
            </div>
        </sly>
        <sly data-sly-test="${properties.bannerStyle == 'ctaVariation'}">
            <div
                class="cta-container"
                data-sly-test="${properties.ctaLabel ? properties.ctaPosition == 'cta-below' : false}">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.ctaUrl}" />
                <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScriptExtra}" />

                <div class="cta-template ${properties.typeExtra ? properties.typeExtra : 'cta-glow'}">
                    <a
                        href="${ctaLink.relativePublishLink @ context='unsafe'}"
                        onclick="${scriptProcessor.processedScript @ context='unsafe'}"
                        data-sly-attribute.aria-label="${properties.ctaAriaLabel}"
                        data-sly-attribute.target="${properties.ctaNewWindow ? '_blank':''}"
                        data-sly-attribute="${attributes ? attributes : autoList.getAttributes}">
                        <span>${properties.ctaLabel}</span>
                        <sly data-sly-test="${properties.typeExtra == 'cta-blog-grid'}">
                            <span class="blog-arrow right"></span>
                        </sly>
                    </a>
                </div>
            </div>
        </sly>
    </div>
    <!-- </sly> -->
</sly>
