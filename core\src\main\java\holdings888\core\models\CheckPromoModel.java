package holdings888.core.models;

import com.day.cq.search.PredicateGroup;
import com.day.cq.search.Query;
import com.day.cq.search.QueryBuilder;
import com.day.cq.search.result.Hit;
import com.day.cq.search.result.SearchResult;
import com.day.cq.wcm.api.Page;
import holdings888.core.bean.PromotionItem;
import holdings888.core.utils.LinkUtils;
import holdings888.core.utils.DateUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.models.annotations.Default;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.ChildResource;
import org.apache.sling.models.annotations.injectorspecific.SlingObject;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.jcr.Node;
import javax.jcr.RepositoryException;
import javax.jcr.Session;
import java.math.BigDecimal;
import java.util.*;

import static com.adobe.dam.print.ids.StringConstants.TITLE;
import static com.adobe.granite.activitystreams.ObjectTypes.IMAGE;
import static com.day.cq.wcm.api.constants.NameConstants.*;
import static holdings888.core.utils.Constants.*;

/**
 * The type Check promo model retrieves all promotions starting from the page
 * where
 * the component was inserted or from custom path
 */
@Model(adaptables = { SlingHttpServletRequest.class }, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class CheckPromoModel {

    private static final String CAROUSEL = "promotion-carousel";
    private static final String GRID = "promotion-grid";
    private static final String POKER_PROMOTION_PAGE_TEMPLATE = "/conf/888poker/settings/wcm/templates/poker--promotion-page";
    private static final String POKER_HOMEPAGE_TEMPLATE = "poker--homepage";
    private static final String SHOW_ALSO_ON_HOMEPAGE = "showAlsoOnHomepage";
    public static final String SHOW_ON = "showOn";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Inject
    private String path;
    @Inject
    private String resourceType;
    @ValueMapValue
    @Default(values = "3")
    private String numberOfPromo;

    @ChildResource(name = "promotions")
    private List<PromotionItem> editorialPromotionList = new ArrayList<>();
    @SlingObject
    private ResourceResolver resourceResolver;
    @Inject
    SlingHttpServletRequest slingHttpServletRequest;
    @Getter
    private List<PromotionItem> promotionList = new ArrayList<>();
    private String templateName;

    /**
     * Init method retrieves all the promotions present in the site
     * and puts at the beginning of a list the manual edited promotions
     *
     * @throws RepositoryException the repository exception
     */
    @PostConstruct
    protected void init() throws RepositoryException {

        if (StringUtils.isNotEmpty(path)) {
            QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);

            Resource pathResource = resourceResolver.getResource(path);
            Page page = pathResource.adaptTo(Page.class);
            templateName = page.getTemplate().getName();

            List<Hit> pathOfPromoList;
            if (resourceType.contains(CAROUSEL)) {
                pathOfPromoList = getPaths(resourceResolver, queryBuilder, path, CAROUSEL);
                pathOfPromoList = DateUtils.filterPromoListByDate(pathOfPromoList,resourceResolver);
                createPageList(pathOfPromoList);
            } else if (resourceType.contains(GRID)) {
                pathOfPromoList = getPaths(resourceResolver, queryBuilder, path, GRID);
                createPageList(pathOfPromoList);
            }
        }
    }

    /*
     * It finds pages with poker promotion templates
     *
     * @return the hits list
     */
    private List<Hit> getPaths(ResourceResolver resourceResolver, QueryBuilder queryBuilder, String lobbyPage,
            String promoType) {

        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", lobbyPage);
        map.put("1_property", NN_CONTENT + SLASH + NN_TEMPLATE);
        map.put("1_property.value", POKER_PROMOTION_PAGE_TEMPLATE);
        if (templateName.equals(POKER_HOMEPAGE_TEMPLATE)) {
            map.put("3_property", NN_CONTENT + SLASH + SHOW_ALSO_ON_HOMEPAGE);
            map.put("3_property.value", Boolean.TRUE.toString());
        } else if (promoType.equals(CAROUSEL)) {
            map.put("2_property", NN_CONTENT + SLASH + SHOW_ON);
            map.put("2_property.value", CAROUSEL);
        } else if (promoType.equals(GRID)) {
            map.put("2_property", NN_CONTENT + SLASH + SHOW_ON);
            map.put("2_property.value", GRID);
        }
        map.put(P_LIMIT, String.valueOf(-1));

        Query query = queryBuilder.createQuery(PredicateGroup.create(map), resourceResolver.adaptTo(Session.class));
        if (logger.isDebugEnabled()) {
            logger.debug("PATHS QUERY: '{}'", query.getPredicates());
        }
        SearchResult results = query.getResult();

        return results.getHits();
    }

    private void createPageList(List<Hit> pathOfPromoList) throws RepositoryException {

        if (!templateName.equals(HOMEPAGE)) {
            for (PromotionItem promo : editorialPromotionList) {
                promo.setLink(
                        LinkUtils.initializeLink(promo.getUrl(), null, resourceResolver, slingHttpServletRequest));
                promotionList.add(promo);
            }
        }

        for (Hit hit : pathOfPromoList) {
            Resource pathResource = resourceResolver.getResource(hit.getPath());
            if (pathResource != null) {
                Page promoPage = pathResource.adaptTo(Page.class);
                Node promoPageContent = pathResource.adaptTo(Node.class).getNode(NN_CONTENT);

                PromotionItem promotionItem = new PromotionItem();
                promotionItem.setImage(
                        promoPageContent.hasProperty(IMAGE) ? promoPageContent.getProperty(IMAGE).getValue().toString()
                                : "");
                promotionItem.setImageAlt(promoPageContent.hasProperty(IMAGE_ALT)
                        ? promoPageContent.getProperty(IMAGE_ALT).getValue().toString()
                        : "");
                promotionItem.setTitle(
                        promoPageContent.hasProperty(TITLE) ? promoPageContent.getProperty(TITLE).getValue().toString()
                                : "");
                promotionItem.setText(
                        promoPageContent.hasProperty(TEXT) ? promoPageContent.getProperty(TEXT).getValue().toString()
                                : "");
                promotionItem.setTncText(
                        promoPageContent.hasProperty("tncText") ? promoPageContent.getProperty("tncText").getString()
                                : "");
                promotionItem.setInnerLinkLabel(
                        promoPageContent.hasProperty("innerLinkLabel") ? promoPageContent.getProperty("innerLinkLabel").getString()
                                : "");
                promotionItem.setInnerLinkHref(
                        promoPageContent.hasProperty("innerLinkHref") ? promoPageContent.getProperty("innerLinkHref").getString()
                                : "");
                promotionItem.setTextEditor(
                        promoPageContent.hasProperty("textEditor") ? promoPageContent.getProperty("textEditor").getString()
                                : "");
                                                
                promotionItem.setUrl(promoPage.getPath());
                promotionItem.setLink(LinkUtils.initializeLink(promotionItem.getUrl(), null, resourceResolver,
                        slingHttpServletRequest));

                promotionList.add(promotionItem);
            }

            if (Objects.nonNull(numberOfPromo) && Integer.valueOf(numberOfPromo) == promotionList.size()) {
                break;
            }
        }
    }
}
