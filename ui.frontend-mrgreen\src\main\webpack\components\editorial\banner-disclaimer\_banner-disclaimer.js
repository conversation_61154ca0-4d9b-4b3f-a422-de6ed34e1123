import { verifyIfElementInViewport } from '../../editorial/promotions-category-icons-anchor/_promotions-category-icons-anchor.js';

function StickyDisclaimerBan($cmp) {
    const ctaVariation = document.querySelector(".ctaVariation");
    let orbit = document.getElementById("orbit-container");

    function setBottomPosition() {

        if (orbit) {
            let clientNavbarArr = document.querySelectorAll(".hybrid-embedded-nav-menu.cy-hybrid-embedded-nav-menu");
            if (window.innerWidth <= 1279) {
                if (clientNavbarArr.length > 0) {
                    let [ clientNavbar ] = clientNavbarArr;
                    const bottomDistance = clientNavbar.getBoundingClientRect().height;
                    const fixedBanner = $cmp.classList.contains("fixed-banner-disclaimer");
                    if (fixedBanner) {
                        $cmp.style.bottom = bottomDistance + 'px';
                    }
                }
            }
        }
    }

    function setStickyClassPromotionVariation(bannerComponent) {
        const screenWidth = window.innerWidth;
        const scrollPosition = window.scrollY;

        if (screenWidth >= 940) {
            bannerComponent.classList.remove('no-display-banner-disclaimer');
            if (!bannerComponent.classList.contains("fixed-banner-disclaimer")) {
                bannerComponent.classList.add('fixed-banner-disclaimer');
            }
        } 
        else if (scrollPosition > 300 && screenWidth < 940) {
            bannerComponent.classList.remove('fixed-banner-disclaimer');
            bannerComponent.classList.add('no-display-banner-disclaimer');
        }
        else {
            bannerComponent.classList.add('fixed-banner-disclaimer');
            bannerComponent.classList.remove('no-display-banner-disclaimer');
        }
    }

    function setStickyClassCtaVariation(bannerComponent) {
        const screenWidth = window.innerWidth;
        const disableStickyTablet = bannerComponent.classList.contains('disableSticky-tablet');

        window.addEventListener("scroll", function() {
            if (!disableStickyTablet && screenWidth <= 767) {
                const footer = document.querySelector(".section-grid");
                const disclaimerSection = document.querySelector(".section-disclaimer");
                let clientNavbar = document.querySelector(".hybrid-embedded-nav-menu.cy-hybrid-embedded-nav-menu");

                if (footer && verifyIfElementInViewport(footer) || verifyIfElementInViewport(disclaimerSection)) {
                        ctaVariation.style.display = "none";
                } else if(window.innerWidth > 767) {
                    ctaVariation.style.bottom = "0px";
                    ctaVariation.style.display = "block";
                }else {
                        ctaVariation.style.bottom = (window.innerHeight - clientNavbar.offsetTop) + 'px';
                        ctaVariation.style.display = "block";
                    }
            }
        });


        if (!disableStickyTablet && window.innerWidth >= 768) {
            bannerComponent.classList.remove('no-display-banner-disclaimer');
            bannerComponent.classList.add("ctaVariation");
            bannerComponent.classList.remove('ctaVariationSticky');
            bannerComponent.classList.remove('fixed-banner-disclaimer');
            ctaVariation.style.bottom = 0;
        } else {
            if (!disableStickyTablet) {
                setBottomPosition();
                bannerComponent.classList.add("ctaVariationSticky");
                bannerComponent.classList.add('fixed-banner-disclaimer');
                bannerComponent.classList.remove('no-display-banner-disclaimer');
            } else {
                bannerComponent.classList.remove('fixed-banner-disclaimer');
                bannerComponent.classList.remove('no-display-banner-disclaimer');
            }
        }

    }

    let isEditor = !!(window.Granite && window.Granite.author);
    if (isEditor) {
        $cmp.classList.remove('fixed-banner-disclaimer');
        $cmp.classList.remove('no-display-banner-disclaimer');
        return;
    }

    const disclaimerBanner = $cmp.className;
    if (disclaimerBanner.includes("disclaimerInnerPromotionPage")) {
        const previousEl = $cmp.closest(".experiencefragment")?.previousElementSibling;

        if (previousEl) {
            let parentXF = $cmp.closest(".experiencefragment");
            if (window.innerWidth > 1278) {
                if (previousEl.className !== "static-herobanner") {
                    parentXF.classList.add('banner-disclaimer-none');
                } else {
                    parentXF.classList.remove('banner-disclaimer-none');
                }
            }

            if (window.innerWidth <= 1278) {
                if (previousEl.className === "static-herobanner") {
                    parentXF.classList.add('banner-disclaimer-none');
                }

                if (previousEl.className.includes("title") || previousEl.className.includes("complex-title")) {
                    let container = $cmp.querySelector(".disclaimer-container");
                    if (container) {
                        parentXF.classList.remove('banner-disclaimer-none');
                        container.classList.add('disclaimer-container-title');
                    }
                }
            }
        }
    } 
    else if(ctaVariation) {
        setStickyClassCtaVariation($cmp);
    }
    else {
        $cmp.classList.add('fixed-banner-disclaimer');

        if (orbit) {
            if (window.innerWidth >= 1280) {
                let disclaimerMainContainer = $cmp.querySelector(".disclaimer-main-container");
                disclaimerMainContainer.classList.add("disclaimer-orbit-aling");
            }

            setBottomPosition();
        }

        window.addEventListener('scroll', function() {
            setStickyClassPromotionVariation($cmp);
        });
    }
}

function initStickyDisclaimer() {
    document.querySelectorAll('.js-banner-disclaimer-component').forEach($cmp => {
        StickyDisclaimerBan($cmp);
    });
}

setTimeout(function(){
    initStickyDisclaimer();
},250);
window.addEventListener('resize', initStickyDisclaimer);
