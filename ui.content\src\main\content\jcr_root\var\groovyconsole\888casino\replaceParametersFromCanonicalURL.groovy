import javax.jcr.PathNotFoundException
import javax.jcr.Property
import javax.jcr.RepositoryException
import javax.jcr.Session
import org.apache.commons.lang.StringUtils
import com.day.cq.replication.ReplicationStatus
import com.day.cq.replication.Replicator
import com.day.cq.replication.ReplicationActionType

def replicator = getService(Replicator)

def updateProperties(pageRoot, replicator) {

    // check if path exists before processing
    try {
        def pageNode = getNode(pageRoot)
    } catch (PathNotFoundException e) {
        println("ERROR: Path does not exist - $pageRoot")
        return 0
    }

    def pageNode = getNode(pageRoot)
    def modifiedPages = []
    def i = 0

    pageNode.recurse { node ->
        if (StringUtils.equals(node.getProperty('jcr:primaryType').getString(), 'cq:PageContent')) {
            def path = node.getPath()
            println("$i path: '$path'")
            i++
            def propertyName = 'cq:canonicalUrl'
            
            try {
                if (node.hasProperty(propertyName)) {
                    def prop = node.getProperty(propertyName).getString()
                    println("Existing property '$propertyName' value: '$prop'")
                    if (prop.contains("888casino_com_segmented_label_domain")) {
                        println("Property '$propertyName' contains '888casino_com_segmented_label_domain'")
                        
                        def resource = resourceResolver.getResource(path)
                        def status = resource?.adaptTo(ReplicationStatus)
                        
                        if (status && status.isActivated()) {

                            // Set property to empty string
                            node.setProperty(propertyName, "")

                            // Collect the page for replication 
                            def pagePath = path.replace("/jcr:content", "")
                            modifiedPages.add(pagePath)
                            
                        } else {
                            println("Page is not published")
                        }
                    } else {
                        println("Property does not contain '888casino_com_segmented_label_domain'")
                    }
                } else {
                    println("Property '$propertyName' does not exist at: ${node.getPath()}")
                }
            } catch (RepositoryException e) {
                println("Error accessing property '$propertyName' at ${node.getPath()}: ${e.message}")
            }
        }
    }

    if (!modifiedPages.isEmpty()) {
        session.save()
    }

    // bulk replication
    modifiedPages.each { pagePath -> 
        try {
            replicator.replicate(session, ReplicationActionType.ACTIVATE, pagePath)
            println("Replicated page: $pagePath")
        } catch (Exception e) {
            println("Error replicating page $pagePath: ${e.message}")
        }
    }

    return modifiedPages.size()
}

def rootPaths = [
    "/content/888casino/com/en/promotions",
    "/content/888casino/com/es/promotions", 
    "/content/888casino/com/fr/promotions",
    "/content/888casino/com/de/promotions",
    "/content/888casino/com/fi/promotions",
    "/content/888casino/com/en_ca/promotions",
    "/content/888casino/com/en/slots",
    "/content/888casino/com/es/slots", 
    "/content/888casino/com/fr/slots",
    "/content/888casino/com/de/slots",
    "/content/888casino/com/fi/slots",
    "/content/888casino/com/en_ca/slots",
]

def validPaths = []
rootPaths.each { path ->
    try {
        def testNode = getNode(path)
        validPaths.add(path)
    } catch (PathNotFoundException e) {
        println("VALIDATION ERROR: Path does not exist - $path")
    }
}

// Process in chunks to avoid timeout
def chunkSize = 3
for (int j = 0; j < validPaths.size(); j += chunkSize) {
    def chunk = validPaths[j..<Math.min(j + chunkSize, validPaths.size())]

    println("Processing chunk: $chunk")
    chunk.each{ path -> 
        println("Processing path: $path")
        def modifiedPageCount = updateProperties(path, replicator)
        println("Modified pages in $path: $modifiedPageCount")
        println("----------------------------")
    }
}

return
