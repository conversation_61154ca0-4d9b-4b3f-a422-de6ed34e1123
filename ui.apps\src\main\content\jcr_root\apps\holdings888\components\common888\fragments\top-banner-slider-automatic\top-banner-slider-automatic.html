<sly data-sly-use.model="holdings888.core.models.ContentBannerSliderModel"/>
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-test.hasContent="${!model.isEmpty}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<div data-sly-test="${hasContent}" class="top-banner-slider-component" data-mbox-id="${properties.mboxId}">
    <div class="top-banner-slider-container js-top-banner-slider-container"
         data-autoplay="${properties.autoplay}" data-swiper-delay="${properties.delay}"
         data-show-dots="${properties.showDots}" data-type-slider="${properties.slider}" data-always-slide="${properties.alwaysSlide}">
        <div class="swiper">
            <div class="swiper-wrapper">
                <sly data-sly-list.path="${model.paths}">
                    <div class="swiper-slide">
                        <div data-sly-resource="${path @ selectors='content', wcmmode='disabled'}"></div>
                    </div>
                </sly>
            </div>
            <div class="swiper-buttons">
                <div class="swiper-button-next">
                    <span class="next">
                        <svg width="1.8em" height="1.8em" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="sc-fzoydu kUYJSY" style="display: block;">
                             <g transform="rotate(180) translate(-24 -24)">
                                 <path fill="currentColor" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                             </g>
                        </svg>
                    </span>
                </div>
                <div class="swiper-button-prev">
                    <span class="prev">
                        <svg width="1.8em" height="1.8em" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="sc-fzoydu hmTUa" style="display: block;">
                            <g>
                                <path fill="currentColor" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                            </g>
                        </svg>
                    </span>
                </div>
            </div>

        </div>
        <div class="swiper-pagination"></div>
        <div class="disclaimer-container"></div>
    </div>
</div>

<sly data-sly-test="${wcmmode.edit && !model.isPathContentBannerSliderPageSet}" data-sly-unwrap>
    <p style="color: orange; text-align: center">Content Banner slider page is not set correctly at the page properties</p>
</sly>

<sly data-sly-include="clientlib.html" />
