.hero-banner-light-lp-v2 {
  .custom-banner {
    background-size: contain;
    background-repeat: no-repeat;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    padding-top: calc(9 / 16 * 75% + 5vw);
    position: relative;
    &.backgroundCover {
      background-size: cover;
      &.backgroundContain {
        @media screen and (max-width: 910px) {
          background-size: contain;
        }
      }
    }
    .above-cta-div {
      display: block;
      position: absolute;
      left: 15%;
      top: 15%;
      width: 100%;

      &.clickable {
        cursor: pointer;
        @media screen and (max-width: 910px) {
          height: 100%;
          z-index: 2;
          left: 5%;
        }
      }
    }
    .above-cta-image {
      display: block;
      width: 31%;
    }

    .above-cta-rich-text {
      width: 30%;
      position: absolute;
      bottom: 39%;
      left: 15%;

      @media screen and (max-width: $sm-grid) and (orientation: landscape) {
        width: 50%;
      }

      &.clickable {
        width: 100%;
        cursor: pointer;
      }
    }

    .above-cta-rich-text-desktop {
      width: 30%;
      position: absolute;
      bottom: 39%;
      left: 15%;

      @media screen and (min-width: $sm-grid) {
        width: 30%;
        display: block;
      }

      &.clickable {
        width: 100%;
        cursor: pointer;
      }
    }

    .above-cta-rich-text-mobile {
      &.clickable {
        cursor: pointer;
      }
    }

    .offerClickable {
      width: 100%;
    }

    .offerContainer {
      width: 35%;
      position: absolute;
      top: 6vw;
      left: 15%;
      padding: 0px 25px;
      border-left: 5px solid $color-link;
      @media screen and (min-width: 911px) and (max-width: 1080px) {
        top: 11vw;
      }

      .offer-type {
        color: white;
        font-weight: 500;
        letter-spacing: 1.3px;
        font-size: 1.3vw;
        font-family: $font-family;
        margin: 0;
        margin-top: 15px;
        @media (orientation: portrait) and (max-width: 910px) {
          font-size: 4vw;
          margin: 0;
          font-weight: 500;
        }
      }

      .offerPrice {
        display: flex;
        gap: 8px;
        align-items: center;

        .offer-amount {
          font-size: 6vw;
          margin: 0;
          font-weight: bold;
          line-height: 1;
          color: #7df700;
          letter-spacing: -2px;
        }

        .offer-amount-text {
          font-weight: 800;
          text-transform: uppercase;
          font-size: 2.3vw;
          line-height: 1;
          translate: 0;
          letter-spacing: -1px;
          color: #e9e9e9;
          margin: 0;
        }
      }

      .subtitle {
        padding: 5px 10px;
        background-color: #141212;
        display: inline-block;
        @media only screen and (max-width: 600px) and (orientation: portrait) {
          width: 65% !important;
        }
        &.deactivateCover {
          background-color: transparent;
        }

        p {
          font-size: 2.2vw;
          color: white;
          font-weight: 800;
          letter-spacing: 1px;
          margin: 0;
          line-height: 1;
        }
      }

      .offer-text {
        color: white;
        font-size: 1.28vw;
        letter-spacing: 1px;
        font-weight: 500;
        margin: 9px 0px;
        font-family: $font-family;
        padding: 0px 1.5px;
        margin-top: 10px;
      }
    }

    .cta-button-container {
      &.max-width-40 {
        @media screen and (orientation: landscape) {
          max-width: 40%;
        }
      }
      bottom: 27%;
      left: 15%;
      height: 4.5vw;
      position: absolute;

      &.mobile-dark-background {
        @media screen and (orientation: portrait) and (max-width: 566px) {
          background-color: $hero-banner-cta-background-color-dark;
        }
      }

      .cta-comp {
        display: flex;
        .cta-component {
          display: flex;
          justify-content: start;
          align-items: center;
          a {
            @media screen and (orientation: landscape) {
              font-size: 1.6vw;
            }
          }
          @media screen and (max-width: 460px) {
            .cta-fullwidth-size {
              a {
                font-size: 5vw;
                border-radius: 8vw;
              }
            }
          }
          .cta-wide-size {
            height: auto;
            width: 100%;
            a {
              font-size: 1.6vw;
              align-items: center;
              min-width: 28vw;
            }
          }
        }
      }

      .additional-images {
        display: flex;
        justify-content: center;
        margin-top: 1rem;
        &.marginImage {
          @media screen and (max-width: 910px), (orientation: portait) {
            margin-top: 20%;
          }
        }

        @media screen and (max-width: $sm) and (orientation: landscape) {
          margin-top: 0 !important;
        }

        img {
          object-fit: contain;
          max-height: 5vw;
          width: 28vw;
          padding: 3% 3% 0 3%;
          &:hover {
            cursor: pointer;
          }
        }
      }

      .cta-rte {
        text-align: center;
        padding-top: 1rem;
        width: 28vw;

        .text p {
          font-size: 0.7vw;
        }
      }

      .three-elements-container {
        padding-top: 1rem;
        width: 140%;
        max-width: 70vw;
        display: flex;

        @media screen and (min-width: 540px) and (max-width: 768px) {
          padding-top: 0;
          margin-bottom: -1rem;
        }

        @media screen and (max-width: 325px) {
          padding-top: 0;
        }

        .cmp-three-elm-component {
          @media screen and (max-width: 325px) {
            transform: translateX(-15%);
          }
          .cmp-three-elm {
            @media screen and (min-width: 911px) and (max-width: 1080px) {
              padding-bottom: 1.1rem;
            }
            @media screen and (max-width: 768px) {
              margin-bottom: 0;
            }

            @media screen and (max-width: 425px) {
              margin-bottom: 1rem;
            }

            @media screen and (max-width: 375px) {
              margin-bottom: 0;
            }

            .cmp-three-elm__step {
              width: auto;
              max-width: unset;
            }
          }
        }

        &:has(.with-title) {
          .cmp-three-elm-component {
            flex-direction: column;

            &__title {
              font-size: 1.24vw;
              font-weight: bold;
              padding-left: 1.6vw;
              margin: 3.8vw 0 1vw 0;
            }
          }
        }
      }

      .rte-threel {
        display: flex;
        flex-direction: column-reverse;

        .three-elements-container {
          padding-top: 1rem;
          width: 140%;
          max-width: 70vw;
        }

        .rte-container {
          padding-top: 1em;

          @media screen and (min-width: 911px) and (orientation: landscape) {
            &.rte-container.rte-container {
              // for specificity increase
              width: 70%;
            }
          }

          .text.text.text p {
            // for specificity increase
            @media screen and (max-width: 460px) {
              font-size: 11px;
            }
          }

          .text p {
            font-size: 0.7vw;
            line-height: normal;
            font-family: $font-family-4;
          }
        }
      }

      .and-rich-text {
        display: flex;
        flex-direction: row;
        gap: 10vw;

        .adim-rte-container {
          padding-right: 3rem;

          .rich-text-component {
            .text {
              p {
                font-size: 0.9vw;
                line-height: 1.1vw;
              }
            }
          }
        }
      }
    }

    &.cta-offer {
      .cta-button-container {
        height: fit-content;
        bottom: 0%;
        @media screen and (min-width: 911px) and (max-width: 1080px) {
          bottom: 5%;
          @media screen and (orientation: landscape) {
            bottom: 10%;
          }
        }
        @media screen and (orientation: landscape) {
          @media screen and (min-width: 1300px) {
            bottom: 15%;
          }
          bottom: 7%;
        }
        @media screen and (min-width: 640px) and (max-width: 768px) and (max-height: 740px) and (orientation: landscape) {
          &:not(:has(.cta-rte, .and-rich-text, .additional-images)) {
            bottom: -20%;
          }
        }
        @media screen and (min-width: 570px) and (max-width: 640px) and (max-height: 740px) and (orientation: landscape) {
          bottom: -30%;
        }

        @media screen and (min-width: 911px) and (max-width: 1080px) {
          &:has(.rte-threel) {
            bottom: -6%;
          }

          &:has(.cta-rte) {
            bottom: 5%;
          }
        }

        .rte-threel .rte-container {
          width: auto;

          .text p {
            font-size: 0.8vw;
          }
        }
      }
      height: auto;
    }
  }
  .rte-container {
    @media screen and (min-width: 910px) and (max-width: 1280px) {
      padding-top: 0.5rem !important;
    }
  }

  @media (max-width: 910px) and (orientation: portrait) {
    .custom-banner {
      padding-top: calc(10 / 9 * 95%);
      .above-cta-image {
        display: none;
      }
      .above-cta-rich-text {
        bottom: 35%;
        left: 5%;
        width: 45%;
      }
      .above-cta-rich-text,
      .and-rich-text .adim-rte-container {
        width: 80%;
      }

      .offerContainer {
        top: 32vw;
        bottom: unset;
        left: 0;
        width: 100%;
        border-left: 2vw solid $color-link;
        padding: 0.2rem 1.5rem;

        .offerPrice {
          flex-direction: row;
          align-items: center;
          gap: 5px;

          .offer-amount {
            font-size: 14vw;
            letter-spacing: 1.2px;
          }

          .offer-amount-text {
            font-size: 6vw;
            translate: 0;
          }
        }

        .subtitle {
          background-color: #7df700;
          width: 60%;
          margin-top: 6px;
          &.deactivateCover {
            background-color: transparent;
            p {
              color: $white !important;
            }
          }

          p {
            font-size: 6vw;
            color: #000;
          }
        }

        .offer-text {
          font-size: 16px;
          width: 50%;
        }
      }

      .cta-button-container {
        height: fit-content;
        position: unset;
        padding: 0 3vw;
        width: 100%;
        display: flex;
        flex-direction: column;
        .cta-comp {
          display: block;
          .cta-component {
            .cta-wide-size {
              width: 100%;
              padding: 1rem 3rem;
              a {
                aspect-ratio: 70/9;
                font-size: 4vw;
              }
            }
          }
        }
        .three-elements-container {
          width: 90%;
          margin: auto;
          .cmp-three-elm {
            .cmp-three-elm__step {
              .cmp-three-elm__step-no {
                font-size: 8vw;
              }
              .cmp-three-elm__step-content {
                .cmp-three-elm__step-title p {
                  font-size: 2.4vw;
                  line-height: 2vw;
                }
                .cmp-three-elm__step-text p {
                  font-size: 1.8vw;
                }
              }
            }
          }
        }
        .cta-rte {
          padding-top: 1rem;
          text-align: center;
          width: 100%;

          .text p {
            font-size: 1.7vw;
            padding: 0.5rem 2rem;
          }
        }
        .additional-images img {
          max-height: 14vw;
          width: 75vw;
          padding: 0;
        }
        .rte-threel {
          align-items: center;
          .rte-container {
            padding-top: 1vw;
            .text p {
              font-size: 1.7vw;
            }
          }
        }
        .and-rich-text {
          flex-direction: column;
          gap: 0vw;
          .adim-rte-container {
            display: flex;
            padding: 0rem 2rem;
            margin: auto;
            width: 100%;

            .rich-text-component .text p {
              font-family: $font-family-4;
              font-size: 1.1rem !important;
              line-height: 3.8vw !important;
            }
          }
          .rich-text-component .text p {
            font-size: 1.5vw !important;
            line-height: 1.8vw !important;
          }
        }
      }

      &.cta-offer {
        .cta-button-container {
          position: unset;
          height: fit-content;
          padding: 0 3vw;

          .three-elements-container {
            width: 90%;
            margin: unset;
            max-width: unset;
          }
          .rte-threel {
            align-items: flex-start;
            .rte-container {
              width: 95%;

              .text p {
                font-size: 2.1vw;
              }
            }
          }
        }
      }
      height: auto;
    }
  }

  @media (max-width: 499px) {
    .custom-banner {
      .above-cta-rich-text {
        width: 80%;
      }

      .offerContainer {
        .offer-type {
          margin: 0;
          font-weight: $font-weight-medium;
        }
        .subtitle {
          width: 60%;
          margin-top: 6px;
          p {
            font-size: 4.5vw;
            @media (orientation: portrait) {
              font-size: 6vw;
            }
          }
        }
        .offer-text {
          margin-bottom: 0;
        }
      }

      .cta-button-container {
        bottom: initial;

        .three-elements-container {
          width: 90%;
          margin: auto;
          display: flex;

          &:has(.with-title) {
            justify-content: space-evenly;

            .cmp-three-elm-component {
              flex-direction: column;

              &__title {
                font-size: 4.3vw;
                font-weight: bold;
                text-align: center;
                padding-left: 0;
                margin: 2vw 0;
              }

              .cmp-three-elm {
                .cmp-three-elm__step {
                  width: auto;
                  max-width: unset;
                  border-left: 0.05vw solid #7cf700;

                  .cmp-three-elm__step-no {
                    font-size: 8vw;
                  }
                  .cmp-three-elm__step-content {
                    .cmp-three-elm__step-title p {
                      font-size: 3.8vw !important;
                    }
                    .cmp-three-elm__step-text p {
                      font-size: 1.8vw;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .cmp-container .header-bar .header-bar-light-lp-components__img img {
    width: 60% !important;
  }
  .header-bar {
    padding-top: 20px !important;
  }
  .cmp-container
    .hero-banner-light-lp-v2
    .custom-banner
    .cta-button-container
    .three-elements-container {
    max-width: 76vw;
  }
  @media (max-width: 460px) {
    .header-bar {
      padding-top: 20px !important;
    }
  }

  @media (max-width: 330px) {
    .header-bar {
      padding-top: 10px !important;
    }
  }

  .header-bar-light-lp-components__img {
    max-width: 32% !important;
  }

  @media (max-width: 1080px) {
    .header-bar .header-bar-light-lp-components__img {
      max-width: 34% !important;
    }
  }
  @media (max-width: 1080px) and (orientation: landscape) {
  }
  @media (max-width: 581px) {
    .header-bar .header-bar-light-lp-components__img img {
      width: 35vw;
    }

    .header-bar-light-lp-components__img {
      align-items: center;
    }
  }

  .cursor-auto picture img {
    width: 8vw !important;
  }

  @media (max-width: 581px) {
    .cursor-auto picture img {
      width: 20vw !important;
    }
  }

  .cmp-container .hero-banner-light-lp-v2 .custom-banner .above-cta-rich-text {
    bottom: 50%;
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      bottom: 42% !important;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      bottom: 40% !important;
    }
  }

  @media (max-width: 1080px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      width: 36% !important;
    }
  }

  @media (max-width: 932px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      width: 52% !important;
    }
  }

  @media (max-width: 581px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      width: 80% !important;
    }
  }

  @media (max-width: 460px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      left: 2% !important;
      width: 56% !important;
    }
  }

  .rich-text-component p {
    margin-bottom: 0 !important;
    line-height: 1.2 !important;
  }

  .offer-text-big,
  .offer-text-big span {
    font-size: 3vw;
  }

  @media (max-width: 932px) {
    .offer-text-big,
    .offer-text-big span {
      font-size: 27px !important;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .offer-text-big,
    .offer-text-big span {
      font-size: 20px !important;
    }
  }

  @media (max-width: 430px) {
    .offer-text-big,
    .offer-text-big span {
      font-size: 22px !important;
    }
  }

  @media (max-width: 321px) {
    .offer-text-big,
    .offer-text-big span {
      font-size: 16px !important;
    }
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .offer-text-big.mobile-b,
    .offer-text-big.mobile-b span {
      font-size: 25px !important;
    }
  }

  @media (max-width: 430px) {
    .offer-text-big.mobile-b,
    .offer-text-big.mobile-b span {
      font-size: 27px !important;
    }
  }

  @media (max-width: 321px) {
    .offer-text-big.mobile-b,
    .offer-text-big.mobile-b span {
      font-size: 20px !important;
    }
  }

  .offer-text-small,
  .offer-text-small span {
    font-size: 1.5vw;
  }

  @media (max-width: 932px) {
    .offer-text-small,
    .offer-text-small span {
      font-size: 20px !important;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .offer-text-small,
    .offer-text-small span {
      font-size: 10px !important;
    }
  }

  @media (max-width: 325px) {
    .offer-text-small,
    .offer-text-small span {
      font-size: 13px !important;
    }
  }

  .custom-banner {
    background-color: #121212;
  }

  @media (orientation: landscape) and (max-width: 940px) {
    .backgroundContain .cta-button-container {
      bottom: 12%;
    }
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .backgroundContain .cta-button-container {
      bottom: 2% !important;
      z-index: 1;
    }
  }

  @media (max-width: 430px) {
    .backgroundContain .cta-button-container {
      bottom: 10% !important;
    }
  }

  @media (max-width: 330px) {
    .backgroundContain .cta-button-container {
      bottom: 13% !important;
    }
  }

  @media (orientation: landscape) and (max-width: 940px) {
    .hero-banner-light-lp-v2 .three-elements-container {
      max-width: 95vw !important;
    }
  }

  @media (max-width: 430px) {
    .hero-banner-light-lp-v2 .three-elements-container {
      max-width: 82vw !important;
    }
  }

  @media (max-width: 330px) {
    .hero-banner-light-lp-v2 .three-elements-container {
      max-width: 72vw !important;
    }
  }

  @media (max-width: 430px) {
    .hero-banner-light-lp-v2
      .three-elements-container
      .cmp-three-elm
      .cmp-three-elm__step:first-child {
      border-left: unset !important;
    }
    .hero-banner-light-lp-v2 .three-elements-container .cmp-three-elm__step {
      padding: 3px !important;
    }

    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel
      .rte-container {
      width: 70vw !important;
    }
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .cta-comp {
      position: absolute;
      bottom: 80px;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .cta-comp {
      position: absolute;
      bottom: 65px;
    }
  }

  @media (orientation: landscape) and (max-width: 940px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel {
      position: absolute;
      bottom: 55px;
    }
    .cmp-container .header-bar .header-bar-light-lp-components__img img {
      max-width: 19vw;
    }
    .offer-text-big,
    .offer-text-big span {
      font-size: 21px !important;
    }
    .offer-text-small,
    .offer-text-small span {
      font-size: 15px !important;
    }
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      bottom: 42%;
    }
    .cmp-container .font-size-15 {
      font-size: 13px;
    }
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel {
      position: absolute;
      bottom: 8px;
    }
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .cta-button-container {
      height: 0.5vw;
    }
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .cta-comp {
      position: absolute;
      bottom: 7vw;
    }
    .cmp-container .font-size-12 {
      font-size: 7pt;
    }
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel {
      position: absolute;
      bottom: 40px;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel {
      position: absolute;
      bottom: 30px;
    }
  }

  @media (orientation: landscape) and (max-width: 670px) {
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel
      .rte-container
      .text
      p
      span,
    .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel
      .rte-container
      .text
      p
      a
      span {
      font-size: 10px !important;
    }
  }

  @media (orientation: landscape) and (max-width: 570px) {
    .cta-template.cta-wide-size a {
      padding: 0.6rem 0 !important;
    }
  }

  @media (max-width: 940px) and (min-width: 904px) {
    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .above-cta-rich-text {
      bottom: 40%;
    }

    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .cta-comp {
      bottom: 68px;
    }

    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner.backgroundContain
      .cta-button-container
      .rte-threel {
      bottom: 23px;
    }

    .cmp-container
      .hero-banner-light-lp-v2
      .custom-banner
      .cta-button-container
      .three-elements-container {
      padding-top: unset;
    }
  }

  @media (max-width: 910px) {
    .above-cta-rich-text-desktop {
      display: none !important;
    }

    .above-cta-rich-text-mobile {
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: absolute;
      top: 15%;
      width: 50%;
      left: 10%;
      height: 50%;
    }

    .custom-banner {
      padding-bottom: 10% !important;
    }

    .header-bar .header-bar-light-lp-components__img {
      max-width: 50% !important;
    }
  }

  @media (min-width: 910px) {
    .above-cta-rich-text-desktop {
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      height: 50%;
      position: absolute;
      top: 13%;
    }

    .above-cta-rich-text-mobile {
      display: none;
    }
  }

  @media screen and (max-width: 1280px) and (min-width: 911px) {
    .rte-container {
      width: 28vw !important;
    }

    .custom-banner {
      height: 80vw;
      background-size: cover;
      background-position: -38vw 0px !important;
    }
  }

  @media (max-width: 800px) {
    .hero-banner-light-lp-v2
      .custom-banner
      .cta-button-container
      .three-elements-container
      .cmp-three-elm
      .cmp-three-elm__step
      .cmp-three-elm__step-content
      .cmp-three-elm__step-text
      p {
      font-size: 2.5vw !important;
    }
  }

  @media (max-width: 763px) {
    .above-cta-rich-text {
      height: 50% ;
      display: flex;
      align-items: center;
      width: 50%;
    }
  }

  @media (min-width: 768px) and (max-width: 910px) {
    .custom-banner {
      padding-top: calc(10 / 9 * 85%);
    }
  }

  .offer-w {
    &-30 {
      width: 30% !important;
    }
    &-35 {
      width: 35% !important;
    }

    &-40 {
      width: 40% !important;
    }

    &-45 {
      width: 45% !important;
    }

    &-50 {
      width: 50% !important;
    }
  }
}
