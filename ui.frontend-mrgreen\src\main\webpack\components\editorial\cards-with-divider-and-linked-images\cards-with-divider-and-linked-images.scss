.cards-with-divider-and-linked-images-cmp {
    display: block;
    @media (min-width: $md) {
        padding: 22.5px;
        display: flex;
        font-size: 15px;
        position: relative;
    }
    .secondCard {
        max-width: 100%;
        padding-bottom: 2rem;
        @media (min-width: $md) {
            padding: 0 10px;
            clear: both;
            max-width: 49%;
        }
        .title-component {
            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                text-transform: uppercase;
                background: transparent;
                line-height: inherit;
                padding: 20px 0 10px;
                margin: 0;
                color: #015536;
                font-weight: 700;
                font-family: $font-family-2;
            }
        }
        .rich-text-compnent {
            p {
                font-weight: 500;
                line-height: 17px;
                color: #313233;
            }
        }
        .images-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
            .image-link {
                img {
                    width: 120px;
                    min-width: 120px;
                    display: inline-block;
                    height: auto;
                    text-decoration: none !important;
                }
            }
        }
    }
    .firstCard {
        max-width: 100%;
        padding-bottom: 2rem;
        @media (min-width: $md) {
            padding: 0 10px;
            clear: both;
            max-width: 49%;
        }
        .title-component {
            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                text-transform: uppercase;
                background: transparent;
                line-height: inherit;
                padding: 20px 0 10px;
                margin: 0;
                color: #015536;
                font-weight: 700;
                font-family: $font-family-2;
            }
        }
        .rich-text-compnent {
            p {
                font-weight: 500;
                line-height: 17px;
                color: #313233;
            }
        }
        .images-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
            .image-link {
                img {
                    width: 120px;
                    min-width: 120px;
                    display: inline-block;
                    height: auto;
                    text-decoration: none !important;
                }
            }
        }
    }
    .horizontal-divider {
        @media (min-width: $md) {
            width: 5px;
            display: block;
            background-color: #ebedef;
            margin: 25px auto;
        }
    }
}


.aem-AuthorLayer-Edit .cards-with-divider-and-linked-images {
    .secondCard{max-height: 100%;}
    .horizontal-divider{height: unset;}
    .firstCard{ max-height: 100%; height: unset;}
}