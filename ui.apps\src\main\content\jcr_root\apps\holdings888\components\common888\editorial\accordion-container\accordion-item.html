<template data-sly-template.accordion="${@ resourcePath, title, iconPath, content, panelType}">
    <sly data-sly-use.accordionContent="${'holdings888.core.models.RichTextImp' @ text=content}"/>
    <sly data-sly-set.accordionHeader="${['accordion-header', resourcePath] @ join='-'}"/>
    <sly data-sly-set.accordionPanel="${['accordion-panel', resourcePath] @ join='-'}"/>

    <sly data-sly-test="${title}">
        <button onclick="handleAccordionClick(this)" class="accordion-tab" tabindex="0" role="button" id="${accordionHeader}" aria-controls="${accordionPanel}" aria-expanded="false">
            <div class="accordion-tab-head d-flex justify-content-between align-items-center gap-2">
                <sly data-sly-test="${iconPath}">
                    <img class="accordion-left-icon" aria-hidden="true" src="${iconPath}" loading="lazy" alt="icon"/>
                </sly>
                <div class="accordion-title">${title}</div>
                <div class="accordion-icon"></div>
            </div>
        </button>
        <section class="accordion-panel ${panelType}" id="${accordionPanel}" aria-labelledby="${accordionHeader}">
            ${accordionContent.text @ context='html'}
        </section>

    </sly>
    

</template>


