.two-part-banner {
    @media (min-width: $md) {
        display: flex;
    }
    .img-bg-mobile {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: auto;
        @media (min-width: $md) {
            display: none;
        }
    }
    .leftHalf {
        color: #ccddaa !important;
        font-family: $font-family-12;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        .content {
            @media (min-width: $md) {
                position: absolute;
                top: 40%;
                left: 25%;
            }
        }
        @media (min-width: $md) {
            position: relative;
            height: auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .img-bg {
            display: none;
            @media (min-width: $md) {
                aspect-ratio: 476 / 537;
                height: auto;
                width: 100%;
            }
        }
    }
    .rightHalf {
        color: #ccddaa !important;
        font-family: $font-family-12;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        .content {
            @media (min-width: $md) {
                position: absolute;
                top: 40%;
                left: 25%;
            }
        }
        @media (min-width: $md) {
            position: relative;
            height: auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .img-bg {
            display: none;
            @media (min-width: $md) {
                aspect-ratio: 476 / 537;
                height: auto;
                width: 100%;
            }
        }
    }
}
