.promotions-image-and-text-overlay-component {
  position: relative;
  overflow: hidden;
  width: 100%;

  .main-image-and-text-overlay {
    position: relative;

    .promotion-img {
      @extend .d-block;
      @extend .w-100;
      @extend .h-100;
    }

    .inner-link {
      display: none;
      @media (max-width: $md) {
        display: inline-block;
        font-size: 11px;        
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .wrapper-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .wrapper {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .bottom-text {
        width: 100%;
        position: absolute;
        bottom: 0;
        transform: translateY(50%);
        transition: all 0.3s ease-in-out;
        background-color: $black;
        opacity: 0.9;

        .wrapper-text {
          color: $white;
          line-height: 1.3em;
          height: 6.25rem;

          .main-text {
            transition: all 0.3s ease-in-out;
            max-height: 3.125rem;
            height: 3.125rem;
            display: table-cell;
            vertical-align: middle;
            padding: 0 1rem;

            &.tnc-extended {
              font-size: 14px;
              height: 3.125rem;
            }
          }

          .line {
            width: calc(100% - 2rem);
            height: 0.063rem;
            background: $white;
            margin: 0 auto;
          }

          .hovering-text {
            max-height: 3.125rem;
            height: 3.125rem;
            vertical-align: middle;
            padding: 0 1rem;
            display: table-cell;

            &.tnc-extended {
              font-size: 14px;
              padding-top: 0.3rem;
            }

            .two-lines-max {
              text-overflow: ellipsis;
              overflow: hidden;
              // Addition lines for 2 line or multiline ellipsis
              display: -webkit-box !important;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              white-space: normal;
            }

            .pc-tnc {
              padding: 0.1rem 0;
              * {
                font-size: 11px;
                line-height: 1em;
              }
            }
            .pc-tnc__carousel {
              padding: 0.1rem 0;
              * {
                font-size: 11px;                
              }
            }
          }
        }
      }
    }

    @media (min-width: $md) {
      &:hover {
        .bottom-text {
          transform: translateY(0);
          transition: all 0.3s ease-in-out;
          background-color: $light-blue;
          opacity: 0.9;

          .wrapper-text {
            line-height: 1.3em;

            &.tnc-extended {
              margin-bottom: 0.4rem;
              max-height: 10rem;
              height: fit-content;
              transition: height 0.3s ease-in-out;
            }

            .line {
              width: calc(100% - 2rem);
              height: 0.063rem;
              background: $white;
              margin: 0 auto;
            }

            .hovering-text {
              transition: all 0.3s ease-in-out;
              .two-lines-max {
                text-overflow: ellipsis;
                overflow: hidden;
                // Addition lines for 2 line or multiline ellipsis
                display: -webkit-box !important;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                white-space: normal;
              }
            }
          }
        }
      }
    }
  }
}
