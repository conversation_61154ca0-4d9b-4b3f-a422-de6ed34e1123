.complex-title-component {
  display: flex;
  justify-content: space-between;
  padding: .6rem 1rem;
  
  margin: 0 0 2rem;
  width: 100%;

  .mrgreen-title-white-color {
    color: $white;
  }
  .mrgreen-title-black-color{
    color: $black;
  }

  span {
    display: flex;
  }

  h1, h2, h3, h4, h5, h6{
    flex-direction: column;
  }
  
  &:has(.mrgreen-green-gradient-bg) {
    background: linear-gradient(90deg,#05372c,hsla(0,0%,100%,0) 75%);
    border-left: 2px solid #232323;

    @media (max-width: 460px) {
      background: linear-gradient(90deg,#05372c,hsla(0,0%,100%,0) 100%);
    }
  }
  &:has(.mrgreen-green-bg) {
    background: $green-7;
  }
  .mrgreen-green-no-gradient-bg{
    background: none;
  }
    
  h1.normal-mrgreen-title, h2.normal-mrgreen-title, h3.normal-mrgreen-title, h4.normal-mrgreen-title, h5.normal-mrgreen-title, h6.normal-mrgreen-title {
    box-sizing: inherit;
    text-transform: capitalize;
    display: inline-flex;
    position: relative;
    letter-spacing: .2rem;
    color: white;
    gap: 2px;
  }

  .title-icon{
    width: auto;
    height: 3.3rem;
    margin: 0 0.2rem 0.5rem 0;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    top: 0.4rem;
    position: relative;
  }
  .default-font {
    font-family: $font-family-10;
    &.demibold, &.bold, &.black {
      font-family: $font-family-10;
    }
  }

  .title-font-variant-2 {
    font-family: $font-family-11;
    &.demibold, &.bold, &.black {
      font-family: $font-family-11;
    }
  }


  .fs-23{
    font-size: 2.3rem;
  }
  .fs-18{
    font-size: 1.8rem;
  }
  .fs-16{
    font-size: 1.6rem;
  }
  .fs-16-12{
    font-size: 1.6rem;
    @media (max-width: $sm){
      font-size: 1.2rem;
    }
  }
  .fs-15{
    font-size: 1.5rem;
  }
}