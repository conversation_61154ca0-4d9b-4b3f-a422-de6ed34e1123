<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html" />
<sly data-sly-use.model="holdings888.core.models.ContentBannerSliderModel" />
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${!model.isEmpty}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<div data-sly-test="${hasContent}" class="content-banner-slider-component ${properties.paddingTop} ${properties.paddingBottom}" data-mbox-id="${properties.mboxId}">
    <div class="content-banner-slider-container js-content-banner-slider-container" data-swiper-autoplay="${properties.delay}" 
    data-show-dots="${properties.showDots}" data-type-slider="${properties.slider}">
        <div class="swiper">
            <div class="swiper-wrapper">
                <sly  data-sly-list.path="${model.paths}">
                    <div class="swiper-slide">
                        <div data-sly-resource="${@path=path, selectors='content', wcmmode='disabled'}"></div>
                    </div>
                </sly> 
            </div>
            <div class="swiper-pagination"></div>
        </div>
    </div>
</div>

<sly data-sly-test="${wcmmode.edit && !model.isPathContentBannerSliderPageSet}" data-sly-unwrap>
    <p style="color: orange; text-align: center">Content Banner slider page is not set correctly at the page properties</p>
</sly>

<sly data-sly-call="${clientlib.css @ categories='holdings888.swiper'}"/>
<sly data-sly-call="${clientlib.all @ categories='holdings888.casino-content-banner-slider'}" />

<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-call="${clientlibs.fe @ locations='casino-content-banner-slider'}"/>