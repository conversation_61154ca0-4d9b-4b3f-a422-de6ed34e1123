import Swiper, { Navigation, Pagination } from "swiper";

function GridSlider($cmp) {
    let selectors = {
        swiper: ".swiper",
        slide: ".swiper-slide",
    };

    let $swiper = $cmp.querySelector(selectors.swiper);
    let hasZigzagSlide = $swiper.querySelector(".swiper-slide.zigzag-slide") !== null;

    initSwiperXF(hasZigzagSlide);

    function initSwiperXF(isZigzag) {
        const defaultBreakpoints = {
            0: {
                enabled: true,
                slidesPerView: 1,
                slidesPerGroup: 1,
                pagination: {
                    enabled: true,
                    clickable: true,
                },
            },
            481: {
                enabled: true,
                slidesPerView: 3,
            },
            769: {
                enabled: true,
            },
            1186: {
                enabled: false,
            }
        };

        const zigzagBreakpoints = {
            0: {
                enabled: true,
                slidesPerView: 1,
                slidesPerGroup: 1,
                pagination: {
                    enabled: true,
                    clickable: true,
                },
            },
            481: {
                enabled: false,
                slidesPerView: 6,
            },
        };

        new Swiper($swiper, {
            modules: [Navigation, Pagination],
            updateOnWindowResize: true,
            slidesPerView: 'auto',
            navigation: {
                nextEl: ".arrows .swiper-button-next",
                prevEl: ".arrows .swiper-button-prev",
            },
            pagination: {
                el: ".swiper-pagination",
                enabled: false,
                clickable: false,
                type: "bullets",
            },
            breakpoints: isZigzag ? zigzagBreakpoints : defaultBreakpoints,
        });
    }
}

function sliderInit() {
    document.querySelectorAll(".grid-slider-component").forEach(function ($cmp) {
        GridSlider($cmp);
    });
}

sliderInit();
