<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"
     data-sly-call="${template.placeholder @ isEmpty=!properties.text}"/>

<sly data-sly-use.titleCustom="${'holdings888.core.models.TitleTextImp'}"/>

<div class="complex-title-container ${properties.paddingTop} ${properties.paddingBottom}">
    <div id="${properties.id}" data-sly-test="${titleCustom.text}" class="complex-title-component" data-mbox-id="${properties.mboxId}">
        <h1 data-sly-element="${properties.type}"
            class ="d-flex justify-content-start align-items-center gap-2 ${properties.headingStyle} ${properties.styleVariant} ${properties.textAlignment} ${properties.fontColor}  ${properties.backgroundColor} ${properties.fontFamily} ${properties.fontWeight}">
            <img class="title-icon" data-sly-test="${properties.iconPath}" aria-hidden="true" src="${properties.iconPath}" alt="title icon" title="title icon" loading="lazy"/>
            <span class="highlight-text" data-sly-test="${properties.highlight && !properties.iconPath}">${properties.highlight}</span>
            ${titleCustom.text @ context='html'}
        </h1>
    </div>
</div>
