package holdings888.core.models;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.jcr.*;

import com.day.cq.search.PredicateGroup;
import com.day.cq.search.Query;
import com.day.cq.search.QueryBuilder;
import com.day.cq.search.result.Hit;
import com.day.cq.search.result.SearchResult;
import com.day.cq.wcm.api.Page;
import holdings888.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.NonExistingResource;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.ChildResource;
import org.apache.sling.models.annotations.injectorspecific.ScriptVariable;
import org.apache.sling.models.annotations.injectorspecific.SlingObject;

import lombok.Getter;

import static com.day.cq.wcm.api.constants.NameConstants.NT_PAGE;
import static holdings888.core.utils.Constants.P_LIMIT;

@Slf4j
@Model(adaptables = { SlingHttpServletRequest.class }, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class ContentBannerSliderModel {

    @SlingObject
    private ResourceResolver resourceResolver;

    @ChildResource(name = "audience")
    String audience = StringUtils.EMPTY;

    @ScriptVariable
    Page currentPage;

    @Getter
    private final List<String> paths = new ArrayList<>();

    @Getter
    public boolean isPathContentBannerSliderPageSet = false;

    @PostConstruct
    protected void init() {
        String path = currentPage.getPath();
        List<Hit> pathOfContentBannerList;

        if (path.contains("experience-fragments")) {
            path = initXF(path, resourceResolver);
            if (Objects.nonNull(path) && StringUtils.isNotBlank(audience)) {
                pathOfContentBannerList = getPathsXF(resourceResolver, path);
                pathOfContentBannerList = DateUtils.filterPromoListByDate(pathOfContentBannerList, resourceResolver);
                if (CollectionUtils.isNotEmpty(pathOfContentBannerList)) {
                    pathOfContentBannerList.stream()
                            .map(hit -> {
                                try {
                                    return hit.getPath();
                                } catch (RepositoryException e) {
                                    e.printStackTrace();
                                }
                                return null;
                            })
                            .filter(StringUtils::isNotEmpty)
                            .map(pathI -> resourceResolver.resolve(pathI+ "/jcr:content/root/container"))
                            .filter(resource -> !(resource instanceof NonExistingResource))
                            .forEach(resource -> paths.add(resource.getPath()));
                }

            }
        }

    }

    private String initXF(String path, ResourceResolver resourceResolver) {
        log.debug("[888] - [ContentBannerSliderModel] - doGet");

        Resource resource = resourceResolver.getResource(path);
        String pathContentBannerSliderPage= StringUtils.EMPTY;
        if (Objects.nonNull(resource)) {
            Page page = resource.adaptTo(Page.class);
            pathContentBannerSliderPage = page.getProperties().get("pathContentBannerSliderPage", String.class);
        }
        if (StringUtils.isNotBlank(pathContentBannerSliderPage)) {
            isPathContentBannerSliderPageSet = true;
            path = pathContentBannerSliderPage;
        } else {
            path = null;
        }
        return path;

    }
    /*
     * It finds pages with Content banner templates with specific audience
     *
     * @return the hits list
     */
    public List<Hit> getPathsXF(ResourceResolver resourceResolver, String path) {
        QueryBuilder queryBuilder = resourceResolver.adaptTo(QueryBuilder.class);
        Map<String, String> map = new HashMap<>(BigDecimal.ZERO.intValue());

        map.put("type", NT_PAGE);
        map.put("path", path);
        map.put("path.self", String.valueOf(false));
        map.put("1_property", "jcr:content/audiences/*/audience");
        map.put("1_property.value", audience);
        map.put("2_property", "jcr:content/audiences/*/orderXf");
        map.put("orderby", "2_property.value");

        map.put(P_LIMIT, String.valueOf(-1));

        Query query = queryBuilder.createQuery(PredicateGroup.create(map),
                resourceResolver.adaptTo(Session.class));
        log.debug("PATHS QUERY: '{}'", query.getPredicates());
        SearchResult results = query.getResult();
        return reorderSlider(results);
    }

    private List<Hit> reorderSlider(SearchResult results) {
        List<Hit> hits = results.getHits();
        return hits.stream()
                .sorted((hit1, hit2) -> {
                    try {
                        String orderXf1 = getOrderXfForAudience(hit1.getNode(), audience);
                        String orderXf2 = getOrderXfForAudience(hit2.getNode(), audience);

                        Integer num1 = Integer.parseInt(orderXf1);
                        Integer num2 = Integer.parseInt(orderXf2);
                        return num1.compareTo(num2);
                    } catch (Exception e) {
                        e.printStackTrace();
                        return 0;
                    }
                })
                .collect(Collectors.toList());
    }

    private String getOrderXfForAudience(Node node, String targetAudience) throws RepositoryException {
        if (node.hasNode("jcr:content/audiences")) {
            Node audiencesNode = node.getNode("jcr:content/audiences");
            NodeIterator iterator = audiencesNode.getNodes();

            while (iterator.hasNext()) {
                Node child = iterator.nextNode();
                if (child.hasProperty("audience")) {
                    Property audienceProp = child.getProperty("audience");
                    if (targetAudience.equals(audienceProp.getString()) && child.hasProperty("orderXf")) {
                        return child.getProperty("orderXf").getString();
                    }
                }
            }
        }
        return null;
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(paths);
    }
}


