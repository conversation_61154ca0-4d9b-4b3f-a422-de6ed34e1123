(() => {
    const lazyBackgrounds = Array.from(document.querySelectorAll(".container-banner"));
    if (!lazyBackgrounds.length) return;

    const updateBackground = (element) => {
        const bgImageDesktop = element.getAttribute('data-background-desktop');
        const bgImageMobile = element.getAttribute('data-background-mobile');
        const isDesktop = window.innerWidth >= 769;
        const bgImage = isDesktop ? bgImageDesktop : bgImageMobile;
        const currentBg = element.style.backgroundImage;
        const newBg = bgImage ? `url(${bgImage})` : '';

        if (currentBg !== newBg) {
            element.style.backgroundImage = newBg;
        }
    };

    const observeBackgrounds = () => {
        const observer = new IntersectionObserver((entries, self) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateBackground(entry.target);
                    self.unobserve(entry.target);
                }
            });
        });

        lazyBackgrounds.forEach(el => observer.observe(el));
    };

    let resizeTimeout;
    const onResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            lazyBackgrounds.forEach(updateBackground);
        }, 150);
    };

    if ('IntersectionObserver' in window) {
        observeBackgrounds();
    } else {
        lazyBackgrounds.forEach(updateBackground);
    }

    window.addEventListener('resize', onResize);
})();