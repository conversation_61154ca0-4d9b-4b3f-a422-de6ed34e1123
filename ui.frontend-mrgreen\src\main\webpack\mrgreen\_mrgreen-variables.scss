$font-family: <PERSON>ser<PERSON>, verdana, sans-serif;
$font-family-1: <PERSON><PERSON><PERSON>, 'Sentinel SSm A', 'Sentinel SSm B', 'Helvetica Neue', Helvetica, Arial, serif;

$font-family-2: '<PERSON><PERSON>ratBold', verdana, sans-serif;
$font-family-3: 'MontserratBlack', verdana, sans-serif;
$font-family-4: 'MontserratSemiBold', verdana, sans-serif;
$font-family-5: 'MontserratExtraBold', verdana, sans-serif;
$font-family-6: 'MontserratRegular', verdana, sans-serif;
$font-family-7: 'MontserratLight', verdana, sans-serif;
$font-family-8: 'MontserratThin', verdana, sans-serif;
$font-family-9: 'MontserratMedium', verdana, sans-serif;
$font-family-10: 'Montserrat', 'verdana', 'sans-serif';
$font-family-11: '888-Orbit', 'verdana', 'sans-serif';
$font-family-12: 'JostRegular', Montserrat, verdana, sans-serif;
$font-family-13: 'Jost<PERSON>ight', 'verdana', 'sans-serif';
$font-family-13-a: 'JostBold', 'verdana', 'sans-serif';
$font-family-14: 'defaultExtraCondensed', 'verdana', 'sans-serif';
$font-family-15: '888demibold', 'verdana', 'sans-serif';
$font-family-16: '888ultra', 'verdana', 'sans-serif';
$font-family-17: '888', 'verdana', 'sans-serif';
$font-family-18: '888bold', 'verdana', 'sans-serif';
$font-family-19: '888ExtraCondensed', 'verdana', 'sans-serif';
$font-family-20: '888medium', 'verdana', 'sans-serif';
$font-family-21: Arial, Helvetica, sans-serif;
$font-family-21: 'BookmanJFProItalic', 'verdana', 'sans-serif';

$font-size: 10px; //this define value REM: 10px = 1rem
$font-size-root: 16px;
$font-size-mobile: 14px;
$line-height: 1.5;
$line-height-2: 1.6;
$line-height-3: 1.2;
$line-height-4: 1.4;
$line-height-normal: 1;
$line-height-double: 2;
$line-height-custom: 1.4285;

//== Color
$white: #ffffff;
$black: #000000;
$dark-grey: #1c1c1c;
$light-grey: #f4f4f4;
$light-green: #7cf700;
$light-blue: #007cf7;
$warm-blue: #00c0ff;
$light-blue-hover: #7fbdfb;
$yellow: #ffdb01;
$gray: #969696;
$gray2: #717171;
$gray3: #171717;
$light: #969696;
$lighter-grey: #b3b3b3;
$gray-border: #e1e1e1;
$green: #7ff800;
$orange: #fc6200;
$gold: #dddcaa;
$white-smoke: #e9e9e9;
$nero: #141212;
$dark-shade-gray: #434343;

//MrGreen Color
$green: #18723c;
$green-1: #17bb6d;
$green-2: #15a360;
$green-3: #41b169;
$green-4: #249752;
$green-5: #7eeaa9;
$green-6: #015536;
$green-7: #05372c;
$green-8: #043429;
$green-9: #2f8755;
$dark-green-awards: #18201d;
$dark-green-2: #015536;
$dark-green: #05372c;
$orange: #e78553;
$dark-1: #313233;
$dark-2: #262627;
$white-1: #ebedef;
$light-gray: #ebedef;
$black-1: #212121;
$yellow: #f1c141;
$dark-3: #121212;
$raisin-black: #232323;
$dark-4: #ffffff00;
$light-grey-2: #8e8e9a;
$sidenav-background: linear-gradient(180deg, rgb(1, 91, 58) 0%, rgb(5, 55, 54) 100%);
$sidenav-background-color: #111;

// Normal mode
$color-background: #f9f9f9;
$color-container-bg: #f9f9f9;
$color-container-bg-dark: #262627;
$color-background-seo: #262627;
$color-container-gradient: #282828;
$color-font: #202020;
$color-link: $green-2;
$color-link-hover: $green-2;
$color-foreground: #202020;
$color-font-footer: #969696;
$color-font-footer-light: #d2d2d2;
$color-bullet-point: $green-2;
$primary-color: $green-4;
$color-divider: #717171;

//Vip three stars divider
$vip-three-stars-divider: #a2a5aa;

// Anchor Menu Coloring
$anchor-menu-divider: $color-divider;
$anchor-menu-color: #05372c;

// Round image background
$round-image-background: #dbdbdb;

//CTA color
$cta-primary-bg: $green-7;
$cta-primary-bg-v2: $green-6;
$cta-primary-bg-hover: $white;
$cta-primary-border-color: $green-7;
$cta-primary-border-color-v2: $green-6;
$cta-primary-text: $white;
$cta-primary-hover-text: $green-7;
$cta-primary-hover-text-v2: $green-6;
$cta-primary-glow: #fce403;

$cta-outline-bg: transparent;
$cta-outline-bg-hover: $green-6;
$cta-outline-border-color: $green-6;
$cta-outline-text: $green-6;
$cta-outline-hover-text: $white;

$cta-outline-w-bg: transparent;
$cta-outline-w-bg-hover: $white;
$cta-outline-w-border-color: $white;
$cta-outline-w-text: $white;
$cta-outline-w-hover-text: #121314;

$cta-outline-v1-bg: transparent;
$cta-outline-v1-bg-hover: #ffffff1a;
$cta-outline-v1-border-color: $green-3;
$cta-outline-v1-text: $green-3;
$cta-outline-v1-hover-text: $green-3;
$cta-outline-v1-focus-outline-color: #7cf70000;
$cta-outline-v1-bg-active: #00000000;
$cta-outline-v1-border-color-active: #2a7243ff;

$cta-secondary-bg: transparent;
$cta-secondary-bg-hover: $green-7;
$cta-secondary-border-color: $green-7;
$cta-secondary-text: $green-7;
$cta-secondary-hover-text: $white;

$cta-secondary-v1-bg: #fce403;
$cta-secondary-v1-bg-hover: $black;
$cta-secondary-v1-border-color: #fce403;
$cta-secondary-v1-text: $black;
$cta-secondary-v1-hover-text: #fce403;

$cta-secondary-v2-bg: #41b169;
$cta-secondary-v2-bg-hover: #41b169;
$cta-secondary-v2-border-color: #41b169;
$cta-secondary-v2-text: $black;
$cta-secondary-v2-hover-text: $white;

$cta-secondary-v3-bg: $dark-green-2;
$cta-secondary-v3-bg-hover: $white;
$cta-secondary-v3-border-color: $white;
$cta-secondary-v3-text: $white;
$cta-secondary-v3-hover-text: $dark-green-2;

$cta-gold-bg: linear-gradient(90deg, $yellow 0%, #f9f19a 48.96%, $yellow 100%);
$cta-gold-bg-hover: linear-gradient(90deg, #f7dc92ff 0%, #fefcecff 48.96%, #f7dc92ff 100%);
$cta-gold-border-color: transparent;
$cta-gold-text: $dark-green;
$cta-gold-hover-text: $dark-green;

$cta-gold-bg-v1: $cta-gold-bg;
$cta-gold-bg-hover-v1: $dark-green;
$cta-gold-border-color-v1: transparent;
$cta-gold-text-v1: $cta-gold-text;
$cta-gold-hover-text-v1: $yellow;

$cta-gold-bg-v2: radial-gradient(circle, #f9f1c9 50%, #faf08c 100%);
$cta-gold-bg-hover-v2: $cta-gold-bg-v2;

$cta-gold-border-color-v3: #f1c141;

// Accordion
$accordion-container: #dadada;

//Component specific colors
$back-to-top-bg: #909090;
$divider-color: #9bb0ab;
$breadcrumb-color: #cfcfcf;
$carousel-bullet-bg: #d1d2d4;
$carousel-bullet-border: #b3b3b6;
$image-caption-text: #a3a3a3;
$image-caption-bg: #f7f7f7;
$separator-color: #ccc;
$past-events-color: #949599;
$dark-promotion: #3e3e3e;
$mobile-menu-icon-bg: #f2f2f2;
$social-share-close-btn: #666666;
$social-share-input-bg: #eeeeee;
$social-share-input-placeholder: #aaaaaa;
$social-share-input-required: #0080ff;
$social-share-btn-gradient: #45a2ff;
$social-share-container-border: #1d4fae;
$steps-primary: #7df700;
$article-slider-border: #353535;
$container-emerald-bg: #355e45;

//Border Radius
$border-radius: 5rem; //50px
// Font-size

$font-size-1: 3.8rem; //38px
$font-size-2: 3.6rem; //36px
$font-size-3: 1.6rem; //16px
$font-size-4: 2.24rem; //22.4px
$font-size-5: 1.2rem; //12px
$font-size-6: 1.1rem; //11px
$font-size-7: 1.44rem; //14.4px
$font-size-8: 4.2rem; //42px
$font-size-9: 3rem; //30px
$font-size-10: 2.5rem; //25px
$font-size-11: 2.1rem; //21px
$font-size-12: 1.65rem; //16.5px
$font-size-13: 2.24rem; //22.4px
$font-size-14: 1.28rem; //12.8px
$font-size-15: 3.2rem; //32px
$font-size-16: 1.5rem; //15px
$font-size-17: 2.3rem; //23px
$font-size-18: 1rem; //10px
$font-size-19: 1.8rem; //18px
$font-size-20: 1.7rem; //17px
$font-size-14px: 14px;

// Font-Weights
$font-weight-thinner: 100;
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;
$font-weight-bold: 700;
$font-weight-medium-bold: 800;
$font-weight-extra-bold: 900;
$font-weight-black: 900;

// Heading
$font-size-h1: 40px;
$font-size-h1-mobile: 32px;
$line-height-h1: 1.24;
$font-weight-h1: $font-weight-normal;

$font-size-h2: 36px;
$font-size-h2-mobile: 26px;
$line-height-h2: 1.24;
$font-weight-h2: $font-weight-normal;

$font-size-h3: 18px;
$font-size-h3-mobile: 18px;
$line-height-h3: 1.24;
$font-weight-h3: $font-weight-normal;

$font-size-h4: 14px;
$font-size-h4-mobile: 14px;
$line-height-h4: 1.24;
$font-weight-h4: $font-weight-normal;

$font-size-h5: 11.62px;
$font-size-h5-mobile: 11.62px;
$line-height-h5: 1.24;
$font-weight-h5: $font-weight-normal;

$font-size-h6: 9.38px;
$font-size-h6-mobile: 9.38px;
$line-height-h6: $line-height-custom;
$font-weight-h6: $font-weight-bold;

//Mobile Icon (Navbar)
$mobile-icon-cmp-height: 6.8rem;

// Accessibility tools
$accessibility-line-height-1: 3.36rem; // 33.6px
$accessibility-line-height-2: 2rem; // 20px
$accessibility-fonts-small: 12px;
$accessibility-fonts-medium: 16px;
$accessibility-fonts-large: 20px;
$zoom-small-h1: 27px;
$zoom-small-h2: 20.4px;
$zoom-medium-h1: 36px;
$zoom-medium-h2: 27.2px;
$zoom-large-h1: 45px;
$zoom-large-h2: 34px;

//Footer
$background-color-footer: #212627;
$color-font-footer: #71716f;
$color-font-footer-light: $light;
$font-size-footer: 2rem; //20px
$font-size-footer-license: $font-size-5;
$font-size-internal-footer-title: 2.4rem;
$font-size-footer-paragraph: $font-size-5;
$footer-separator: $color-divider;
$footer-grid-icon-border: #3f3e3e;
$footer-banner-background: #00161b;

// GRID
$grid-width: 100%; // 100%
$grid-divider-color: #ffffffaa;
$gutter-vertical: 2rem; //20px
$gutter-horizontal: 2rem; // 20px

//Simplebar
$simplebar-dark-track-color: #ebebeb;
$simplebar-dark-scrollbar-color: #d9d9d9;
$simplebar-light-track-color: rgba(256, 256, 256, 0.2);
$simplebar-light-scrollbar-color: rgba(256, 256, 256, 0.8);

// Options
//
// Quickly modify global styling by enabling or disabling optional features.
$enable-grid-classes: true;
$enable-negative-margins: false;

// mixins media-breakpoint-up
$xs: 0; // = > is MOBILE FIRST
$sm: 640px; // = > TABLET
$sm-max: calc(640px - 1px); // TABLET: SPECIAL For @media (max-width: $sm-max)
$sm-max-landscape: 844px;
$sm-air: 820px; // = > TABLET AIR
$md: 1025px; // = > DESKTOP: For @media (min-width: $md)
$md-blog: 915px; //Custom media query
$md-max: calc(1025px - 1px); // > DESKTOP: SPECIAL For @media (max-width: $md-max)
$lg: 1200px; // DESKTOP BIG SIZE
$lg-max: calc(1200px - 1px); // > DESKTOP: SPECIAL For @media (max-width: $lg-max)
$height-mobile: 44rem; //440px
$height-tablet: 84rem; //840px
$height-desktop: 85rem; //850px

// Orbit client breakpoints
$sm-grid: 768px; // = > TABLET
$md-grid: 1280px; // = > DESKTOP
$lg-grid: 1480px; // = > DESKTOP BIG SIZE

$grid-breakpoints: (
    xs: 0,
    sm: 640px,
    //MOBILE
    md: 1024px,
    //TABLET
    lg: 1200px,
    //DESKTOP
);
// scss-docs-start container-max-widths
$container-max-widths: (
    sm: 576px,
    //MOBILE
    md: 1024px,
    //TABLET
    lg: 1140px,
    //DESKTOP
);

// scss-docs-end grid-breakpoints
@include _assert-ascending($grid-breakpoints, '$grid-breakpoints');
@include _assert-starts-at-zero($grid-breakpoints, '$grid-breakpoints');

$linear-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
$linear-gradient-light-grey: linear-gradient(180deg, rgba(244, 244, 244, 0) 65%, rgba(244, 244, 244, 0.97) 99%, rgba(244, 244, 244, 1) 100%);

$text-shadow: #fff 1px 1px 0, 2px 2px 0 #4c1723, 2px -2px 0 #4c1723, -2px 2px 0 #4c1723, -2px -2px 0 #4c1723, 2px 0px 0 #4c1723, 0px 2px 0 #4c1723, -2px 0px 0 #4c1723,
    0px -2px 0 #4c1723, 1px 0px 1px #4c1723, 0px 1px 1px #4c1723, 2px 1px 1px #4c1723, 1px 2px 1px #4c1723, 3px 2px 1px #4c1723, 2px 3px 1px #4c1723, 4px 3px 1px #4c1723,
    3px 4px 1px #4c1723, 5px 4px 1px #4c1723, 4px 5px 1px #4c1723, 6px 5px 1px #4c1723, 5px 6px 1px #4c1723, 7px 6px 1px #4c1723, 2px 2px 2px rgba(255, 190, 11, 0);
$text-shadow-2: 1px 2px 3px #51afaf;
$text-shadow-3: 5px 5px 5px #000;
