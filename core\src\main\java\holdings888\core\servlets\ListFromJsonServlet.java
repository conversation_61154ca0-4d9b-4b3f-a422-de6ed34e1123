package holdings888.core.servlets;

import com.adobe.acs.commons.wcm.datasources.DataSourceBuilder;
import com.adobe.acs.commons.wcm.datasources.DataSourceOption;
import com.adobe.cq.dam.cfm.ContentElement;
import com.adobe.cq.dam.cfm.ContentFragment;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;

import javax.servlet.Servlet;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Servlet that gets a list/map of options from a JSON stored in a Content Fragment under /content/dam/[holdings888 | mrgreen]/[local brand | locale]/jsons/[CFName]
 * Add node to a dialog:
 * <selection
 *     jcr:primaryType="nt:unstructured"
 *     sling:resourceType="granite/ui/components/coral/foundation/form/select"
 *     fieldLabel="Select element"
 *     name="./[selectionFieldName]">
 *        <datasource
 *           jcr:primaryType="nt:unstructured"
 *           source="[nameOfTheCF]"
 *           sourceType="[map]" // "map" (text -> value) or "list" (text -> text) in resulting dropdown from key:value pairs in JSON
 *           sling:resourceType="/apps/getListFromJson"/>
 *  </selection>
 *  CF should be created from Json CF model, containing a JSON with key:value pairs
 */
@Slf4j
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Get List From Json Servlet",
        "sling.servlet.resourceTypes=" + "/apps/getListFromJson"
})
public class ListFromJsonServlet extends SlingSafeMethodsServlet {

    private static final String ELEMENT_NAME = "json";
    private static final String DAM_ROOT = "/content/dam/";
    private static final String JSONS_FOLDER = "/jsons/";
    private static final String DATASOURCE_NODE_NAME = "datasource";
    private static final String CF_NAME_PARAMETER = "source";
    private static final String SOURCE_TYPE_PARAMETER = "sourceType";
    private static final String SOURCE_TYPE_MAP = "map";
    private static final String SOURCE_TYPE_LIST = "list";
    @Reference
    transient DataSourceBuilder dataSourceBuilder;

    @Override
    protected void doGet(SlingHttpServletRequest request, SlingHttpServletResponse response) {
        ResourceResolver resourceResolver = request.getResourceResolver();
        Resource currentResource = request.getResource();
        String CFName = Optional.ofNullable(Objects.requireNonNull(currentResource.getChild(DATASOURCE_NODE_NAME))
                .getValueMap().get(CF_NAME_PARAMETER, String.class)).orElse(StringUtils.EMPTY);
        String sourceType = Optional.ofNullable(Objects.requireNonNull(currentResource.getChild(DATASOURCE_NODE_NAME))
                .getValueMap().get(SOURCE_TYPE_PARAMETER, String.class)).orElse(SOURCE_TYPE_MAP);
        String localBrand = extractLocalBrand(request);
        String CFPath = DAM_ROOT + ( StringUtils.contains(localBrand, "mrgreen") ? "" : "holdings888/" )  + localBrand + JSONS_FOLDER + CFName;
        ContentFragment contentFragment = resourceResolver.resolve(CFPath).adaptTo(ContentFragment.class);
        ContentElement cfEl = Optional.ofNullable(contentFragment).map(cf -> cf.getElement(ELEMENT_NAME)).orElse(null);
        String value;
        List<DataSourceOption> options = new ArrayList<>();
        if (Objects.nonNull(cfEl)) {
            value = Optional.ofNullable(cfEl.getContent()).orElse(StringUtils.EMPTY);
            if (StringUtils.isNotEmpty(value)) {
                options = getDataSourceOptionList(value, options, CFPath, sourceType);
            }
        } else {
            log.error("Content Element is null at {}", CFPath);
            options.add(new DataSourceOption("ERROR: No CF at " + CFPath, StringUtils.EMPTY));
        }
        dataSourceBuilder.addDataSource(request, options);
    }

    private static String extractLocalBrand(SlingHttpServletRequest request) {
        String suffix = Optional.ofNullable(request.getHeader(HttpHeaders.REFERER)).orElse(StringUtils.EMPTY);
        String regex = "/content/(experience-fragments/)?([^/]*/[^/]*/[^/]*)/";
        Matcher matcher = Pattern.compile(regex).matcher(suffix);
        return matcher.find() ? matcher.group(2) : StringUtils.EMPTY;
    }

    private static List<DataSourceOption> getDataSourceOptionList(String value, List<DataSourceOption> options, String CFPath, String sourceType) {
        try {
            Map<String, String> dropDownMap = new Gson().fromJson(value, new TypeToken<Map<String, Object>>() {}.getType());
            switch (sourceType) {
                case SOURCE_TYPE_MAP:
                    options = dropDownMap.entrySet().stream()
                            .map(entry -> new DataSourceOption(entry.getKey(), entry.getValue()))
                            .collect(Collectors.toList());
                    break;
                case SOURCE_TYPE_LIST:
                    options = dropDownMap.keySet().stream()
                            .map(s -> new DataSourceOption(s, s))
                            .collect(Collectors.toList());
                    break;
                default:
                    log.error("Unknown source type: {}", sourceType);
                    throw new IllegalArgumentException("Unknown source type: " + sourceType);
            }
        } catch (Exception e) {
            log.error("Error while parsing JSON from CF at {}", CFPath);
            options.add(new DataSourceOption("ERROR: " + e.getMessage(), StringUtils.EMPTY));
        }
        return options;
    }
}
