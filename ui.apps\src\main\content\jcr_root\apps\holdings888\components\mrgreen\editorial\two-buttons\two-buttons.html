<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html" />
<sly data-sly-test.hasContent="${properties.imageLeftReference || properties.imageRightReference}" />
<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />


<sly data-sly-test="${hasContent}">
    <div class="two-buttons-component ${properties.flexAlignment} ${properties.paddingTop} ${properties.paddingBottom}">
        <div class="two-buttons__left">
            <sly data-sly-call="${link.simpleImage @
                properties=properties,
                linkUrlName='imageLeftUrl',
                imagePath=properties.imageLeftReference,
                imageAlt=properties.imageLeftAlt
                }"/>
        </div>

        <div class="two-buttons__right">
            <sly data-sly-call="${link.simpleImage @
                properties=properties,
                linkUrlName='imageRightUrl',
                imagePath=properties.imageRightReference,
                imageAlt=properties.imageRightAlt
                }"/>
        </div>
    </div>
</sly>