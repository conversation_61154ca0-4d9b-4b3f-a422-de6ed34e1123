window.handleAccordionClick = (tab) => {
   tab.classList.toggle("active");
        let panel = tab.nextElementSibling;

        if (panel.style.display == "block") {
            tab.ariaExpanded = "false";
            panel.style.display = "none";
            panel.classList.remove("open");
        } else {
           tab.ariaExpanded = "true";
            panel.style.display = "block";
            panel.classList.add("open");
        }
}

