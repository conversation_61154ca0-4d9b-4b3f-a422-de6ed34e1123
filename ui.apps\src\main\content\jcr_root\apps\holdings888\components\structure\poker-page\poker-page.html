<!DOCTYPE HTML>
<html data-sly-use.pageModel="holdings888.core.models.PageModel"
      data-sly-use.version="holdings888.core.models.CodeBaseVersioningModel"
      data-sly-test.page="${pageModel.page}"
      lang="${page.language == 'es' ? 'es-419' : page.language}"
      data-sly-use.pwa="com.adobe.cq.wcm.core.components.models.PWA"
      data-sly-use.head="head.html"
      data-sly-use.script="script.html"
      data-sly-use.footer="footer.html"
      data-sly-use.redirect="redirect.html"
      data-sly-use.bodySkipToMainContent="body.skiptomaincontent.html">
    <head>
        <sly data-sly-call="${head.head @ pageModel = pageModel, pwa = pwa, version = version}"></sly>
    </head>
    <body class="${page.cssClassNames}"
          id="${page.id}"
          data-cmp-link-accessibility-enabled
          data-cmp-link-accessibility-text="${'opens in a new tab' @ i18n}"
          data-cmp-data-layer-enabled="${page.data ? true : false}">

        <sly data-sly-test="${pageModel.externalJsCssFooterTop}">
            <! -- External 888 CSS Body Top -->
            ${pageModel.externalJsCssFooterTop @ context='unsafe'}
            <! -- / External 888 CSS Body Top -->
        </sly>

        <sly data-sly-call="${script.body @ pageModel = pageModel}"></sly>

                <sly data-sly-test.isRedirectPage="${page.redirectTarget && (wcmmode.edit || wcmmode.preview)}"
                     data-sly-call="${redirect.redirect @ redirectTarget = page.redirectTarget}"></sly>

                <sly data-sly-test="${!isRedirectPage}">
                    <sly data-sly-call="${bodySkipToMainContent.bodySkipToMainContent @ page = pageModel.page}"></sly>
                    <sly data-sly-include="body.html">
                    </sly>
                    <sly data-sly-call="${footer.footer @ page = pageModel.page, pwa = pwa}"></sly>
                </sly>

        <sly data-sly-test="${pageModel.externalJsCssFooterBottom}">
            <! -- External 888 CSS JS Body Bottom -->
            ${pageModel.externalJsCssFooterBottom @ context='unsafe'}
            <! -- / External 888 CSS Body Bottom -->
        </sly>

    </body>
</html>
