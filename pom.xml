<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
 |  Copyright 2015 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>holdings888</groupId>
    <artifactId>holdings888</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>holdings888</name>
    <description>holdings888</description>
    <modules>
        <module>all</module>
        <module>core</module>
        <module>ui.apps</module>
        <module>ui.apps.structure</module>
        <module>ui.config</module>
        <module>ui.content</module>
        <module>it.tests</module>
        <module>dispatcher</module>
        <module>ui.tests</module>
    </modules>
    <properties>
        <aem.host>localhost</aem.host>
        <aem.port>4502</aem.port>
        <aem.publish.host>localhost</aem.publish.host>
        <aem.publish.port>4503</aem.publish.port>
        <sling.user>admin</sling.user>
        <sling.password>admin</sling.password>
        <vault.user>admin</vault.user>
        <vault.password>admin</vault.password>
        <frontend-maven-plugin.version>1.12.0</frontend-maven-plugin.version>
        <core.wcm.components.version>2.23.4</core.wcm.components.version>
        <bnd.version>6.4.0</bnd.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <aem.sdk.api>2025.5.21005.20250522T173058Z-250500</aem.sdk.api>
        <aemanalyser.version>1.6.6</aemanalyser.version>
        <componentGroupName>holdings888</componentGroupName>
        <groovy.console.version>19.0.8</groovy.console.version>
        <netcentric.actool>3.0.4</netcentric.actool>
    </properties>
    <build>
      <resources>
        <resource>
            <directory>src/main/resources</directory>
        </resource>
    </resources>
        <plugins>
            <!-- Maven Release Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <scmCommentPrefix>[maven-scm] :</scmCommentPrefix>
                    <preparationGoals>clean install</preparationGoals>
                    <goals>install</goals>
                    <releaseProfiles>release</releaseProfiles>
                </configuration>
            </plugin>
            <!-- Maven Source Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <inherited>true</inherited>
            </plugin>
            <!-- Maven Enforcer Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.3.9,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <message>Maven must be executed with a Java 21 JRE or higher.</message>
                                    <version>21</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.3.0</version>
                    <configuration>
                        <tarLongFileMode>posix</tarLongFileMode>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.github.eirslett</groupId>
                    <artifactId>frontend-maven-plugin</artifactId>
                    <version>${frontend-maven-plugin.version}</version>
                    <configuration>
                        <nodeVersion>v12.22.7</nodeVersion>
                        <npmVersion>6.14.0</npmVersion>
                    </configuration>
                    <executions>
                        <execution>
                            <id>install node and npm</id>
                            <goals>
                                <goal>install-node-and-npm</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>npm install</id>
                            <goals>
                                <goal>npm</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- Maven Jar Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <!-- Maven Clean Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <!-- BND Maven Plugin -->
                <plugin>
                    <groupId>biz.aQute.bnd</groupId>
                    <artifactId>bnd-maven-plugin</artifactId>
                    <version>${bnd.version}</version>
                    <executions>
                        <execution>
                            <id>bnd-process</id>
                            <goals>
                                <goal>bnd-process</goal>
                            </goals>
                            <configuration>
                                <bnd>
                                    <![CDATA[
Bundle-Category: ${componentGroupName}

# export all versioned packages except for conditional ones (https://github.com/bndtools/bnd/issues/3721#issuecomment-579026778)
-exportcontents: ${removeall;${packages;VERSIONED};${packages;CONDITIONAL}}

# reproducible builds (https://github.com/bndtools/bnd/issues/3521)
-noextraheaders: true
-snapshot: SNAPSHOT

Bundle-DocURL:
-plugin org.apache.sling.caconfig.bndplugin.ConfigurationClassScannerPlugin
-plugin org.apache.sling.bnd.models.ModelsScannerPlugin
                                ]]>
                                </bnd>
                            </configuration>
                        </execution>
                    </executions>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.sling</groupId>
                            <artifactId>org.apache.sling.caconfig.bnd-plugin</artifactId>
                            <version>1.0.2</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.sling</groupId>
                            <artifactId>org.apache.sling.bnd.models</artifactId>
                            <version>1.0.0</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.sling</groupId>
                            <artifactId>scriptingbundle-maven-plugin</artifactId>
                            <version>0.5.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>biz.aQute.bnd</groupId>
                    <artifactId>bnd-baseline-maven-plugin</artifactId>
                    <version>${bnd.version}</version>
                </plugin>
                <!-- Maven Resources Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <!-- Maven Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                </plugin>
                <!-- Maven Installer Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <!-- Maven Surefire Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.1</version>
                    <configuration>
                        <useSystemClassLoader>false</useSystemClassLoader>
                    </configuration>
                </plugin>
                <!-- Maven Failsafe Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>2.22.1</version>
                </plugin>
                <!-- Maven Deploy Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <!-- Apache Sling Plugin -->
                <plugin>
                    <groupId>org.apache.sling</groupId>
                    <artifactId>sling-maven-plugin</artifactId>
                    <version>2.4.0</version>
                    <configuration>
                        <slingUrl>http://${aem.host}:${aem.port}/system/console</slingUrl>
                        <deploymentMethod>WebConsole</deploymentMethod>
                    </configuration>
                </plugin>
                <!-- HTL Maven Plugin -->
                <plugin>
                    <groupId>org.apache.sling</groupId>
                    <artifactId>htl-maven-plugin</artifactId>
                    <version>2.0.2-1.4.0</version>
                    <configuration>
                        <failOnWarnings>false</failOnWarnings>
                    </configuration>
                </plugin>
                <!-- Jackrabbit FileVault Package Plugin -->
                <plugin>
                    <groupId>org.apache.jackrabbit</groupId>
                    <artifactId>filevault-package-maven-plugin</artifactId>
                    <extensions>true</extensions>
                    <version>1.3.2</version>
                    <configuration>
                        <filterSource>src/main/content/META-INF/vault/filter.xml</filterSource>
                        <validatorsSettings>
                            <jackrabbit-nodetypes>
                                <options>
                                    <!-- use the nodetypes and namespaces from the aem-nodetypes.jar provided in the plugin dependencies -->
                                    <cnds>tccl:aem.cnd</cnds>
                                </options>
                            </jackrabbit-nodetypes>
                            <jackrabbit-packagetype>
                                <options>
                                    <immutableRootNodeNames>apps,libs,oak:index</immutableRootNodeNames>
                                </options>
                            </jackrabbit-packagetype>
                        </validatorsSettings>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>biz.netcentric.aem</groupId>
                            <artifactId>aem-nodetypes</artifactId>
                            <version>6.5.7.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <!-- AEM Analyser Plugin -->
                <plugin>
                    <groupId>com.adobe.aem</groupId>
                    <artifactId>aemanalyser-maven-plugin</artifactId>
                    <version>${aemanalyser.version}</version>
                    <extensions>true</extensions>
                </plugin>
                <!-- Content Package Plugin -->
                <plugin>
                    <groupId>com.day.jcr.vault</groupId>
                    <artifactId>content-package-maven-plugin</artifactId>
                    <version>1.0.2</version>
                    <configuration>
                        <targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
                        <failOnError>true</failOnError>
                        <userId>${vault.user}</userId>
                        <password>${vault.password}</password>
                    </configuration>
                </plugin>
                <!-- Maven Enforcer Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <!-- Maven Dependency Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <!-- Build Helper Maven Plugin -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <!--This plugin's configuration is used to store Eclipse
                            m2e settings only. It has no influence on the Maven build itself. -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-enforcer-plugin</artifactId>
                                        <versionRange>[1.0.0,)</versionRange>
                                        <goals>
                                            <goal>enforce</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.apache.maven.plugins
                                        </groupId>
                                        <artifactId>
                                            maven-dependency-plugin
                                        </artifactId>
                                        <versionRange>
                                            [2.2,)
                                        </versionRange>
                                        <goals>
                                            <goal>copy-dependencies</goal>
                                            <goal>unpack</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.codehaus.mojo
                                        </groupId>
                                        <artifactId>
                                            build-helper-maven-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.5,)
                                        </versionRange>
                                        <goals>
                                            <goal>
                                                reserve-network-port
                                            </goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <!-- Development profile: install only the bundle -->
        <profile>
            <id>autoInstallBundle</id>
            <!--
                      To enable this feature for a bundle, the sling-maven-plugin
                      (without configuration) needs to be included:

                      <plugin><groupId>org.apache.sling</groupId><artifactId>sling-maven-plugin</artifactId></plugin>
                  -->
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.sling</groupId>
                            <artifactId>sling-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>install-bundle</id>
                                    <goals>
                                        <goal>install</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>autoInstallPackage</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.jackrabbit</groupId>
                            <artifactId>filevault-package-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>create-package</id>
                                    <goals>
                                        <goal>package</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>com.day.jcr.vault</groupId>
                            <artifactId>content-package-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>install-package</id>
                                    <goals>
                                        <goal>install</goal>
                                    </goals>
                                    <configuration>
                                        <targetURL>http://${aem.host}:${aem.port}/crx/packmgr/service.jsp</targetURL>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>autoInstallPackagePublish</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.jackrabbit</groupId>
                            <artifactId>filevault-package-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>create-package</id>
                                    <goals>
                                        <goal>package</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>com.day.jcr.vault</groupId>
                            <artifactId>content-package-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>install-package-publish</id>
                                    <goals>
                                        <goal>install</goal>
                                    </goals>
                                    <configuration>
                                        <targetURL>
                                            http://${aem.publish.host}:${aem.publish.port}/crx/packmgr/service.jsp
                                        </targetURL>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
    <!-- ====================================================================== -->
    <!-- D E P E N D E N C I E S -->
    <!-- ====================================================================== -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.adobe.aem</groupId>
                <artifactId>aem-sdk-api</artifactId>
                <version>${aem.sdk.api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.adobe.cq</groupId>
                <artifactId>core.wcm.components.core</artifactId>
                <version>${core.wcm.components.version}</version>
            </dependency>
            <!-- Testing -->
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.8.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>4.1.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>4.1.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit-addons</groupId>
                <artifactId>junit-addons</artifactId>
                <version>1.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.wcm</groupId>
                <artifactId>io.wcm.testing.aem-mock.junit5</artifactId>
                <version>5.4.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.sling</groupId>
                <artifactId>org.apache.sling.testing.caconfig-mock-plugin</artifactId>
                <version>1.5.2</version>
            </dependency>
            <dependency>
                <groupId>com.adobe.cq</groupId>
                <artifactId>core.wcm.components.testing.aem-mock-plugin</artifactId>
                <version>${core.wcm.components.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>uk.org.lidalia</groupId>
                <artifactId>slf4j-test</artifactId>
                <version>1.2.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.12.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.15.3</version>
            </dependency>
            <!--Netcentric ACL Tool -->
            <dependency>
                <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
                <artifactId>accesscontroltool-package</artifactId>
                <classifier>cloud</classifier>
                <type>content-package</type>
                <version>${netcentric.actool}</version>
            </dependency>
            <!--Oak Index for netcentric ACL Tool -->
            <dependency>
                <groupId>biz.netcentric.cq.tools.accesscontroltool</groupId>
                <artifactId>accesscontroltool-oakindex-package</artifactId>
                <classifier>cloud</classifier>
                <type>zip</type>
                <version>${netcentric.actool}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
