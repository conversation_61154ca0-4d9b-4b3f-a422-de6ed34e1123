<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-call="${clientlibs.fe @ locations='casino-promotions-top-games-carousel-css,casino-promotions-top-games-carousel-js'}"/>
<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='games'}"/>

<sly data-sly-use.clientlib="core/wcm/components/commons/v1/templates/clientlib.html">
    <sly data-sly-call="${clientlib.css @ categories='holdings888.swiper'}"/>
    <sly data-sly-call="${clientlib.css @ categories='holdings888.casino-promotions-top-games-carousel'}"/>
    <sly data-sly-call="${clientlib.js @ categories='holdings888.casino-promotions-top-games-carousel', defer=true}"/>
</sly>

<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='listItems'}" />
<sly data-sly-use.additionalListItems="${'holdings888/utils/multifield.js' @ multifieldName='additionalListItems'}" />
<sly data-sly-test.hasContent="${properties.ctaLabel}" />
<sly data-sly-test.notShowTitle="${properties.notShowTitle}" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}" />
<div class="top-games-slider-container ${properties.variation == 'variation1' ? 'variation1' : (properties.variation == 'variation2' ? 'variation2' : 'variation3')}" data-jp-currency="${properties.apiCurrencyJp}" data-jp-brand="${properties.apiBrandJp}"
     data-jp-currency-sign="${properties.currencySignJp}" data-jp-currency-after-amount="${properties.currencyAfterAmount}">
  <div class="title" data-sly-test="${notShowTitle}">
        <div class="slider-container-title">
            <sly data-sly-resource="${'title' @ resourceType='holdings888/components/seven/editorial/title'}"/>
        </div>
    </div>
    <div class="jp-slider-controls" data-sly-test="${properties.variation != 'variation1'}">
        <span class="prev-btn">
                <svg width="1.8em" height="1.8em" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="sc-fzoydu hmTUa" style="display: block;">
                        <g>
                                <path fill="currentColor" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                        </g>
                </svg>
        </span>
        <span class="next-btn">
                <svg width="1.8em" height="1.8em" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" class="sc-fzoydu kUYJSY" style="display: block;">
                        <g transform="rotate(180) translate(-24 -24)">
                                <path fill="currentColor" d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                        </g>
                </svg>
        </span>
    </div>
    <div class="top-games-slider">
        <div class="swiper-wrapper">
            <sly data-sly-list="${additionalListItems}">
                <div class="swiper-slide additional-games ">
                    <div class="image-container img-${properties.imageSize}">
                        <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=item.properties.scriptUrl}" />
                        <sly data-sly-use.additionalGameIcon="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=item.properties.additionalGameIcon}"/>
                        <sly data-sly-set.additionalGameIconJp="${additionalGameIcon.renditions['webp'].path || additionalGameIcon.renditions['original'].path}"/>

                        <img
                            src="${additionalGameIconJp}"
                            loading="lazy"
                            alt="${item.properties.additionalGameIconAlt}"
                            onclick="${scriptProcessor.processedScript @ context='unsafe'}" />
                    </div>
                    <div class="game-info">
                        <sly data-sly-test="${item.properties.additionalGameName}">
                            <span class="game-name">${item.properties.additionalGameName}</span>
                        </sly>
                    </div>
                </div>
            </sly>
        </div>
    </div>
    <div
        class="cta-container"
        data-sly-test="${hasContent}">
        <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.ctaUrl}" />

        <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.ctaScriptExtra}" />

        <div class="cta-template ${properties.typeExtra ? properties.typeExtra : 'cta-glow'}">
            <a
                href="${ctaLink.relativePublishLink @ context='unsafe'}"
                onclick="${scriptProcessor.processedScript @ context='unsafe'}"
                data-sly-attribute.aria-label="${properties.ctaAriaLabel}"
                data-sly-attribute.target="${properties.ctaNewWindow ? '_blank':''}"
                data-sly-attribute="${attributes ? attributes : autoList.getAttributes}">
                <span>${properties.ctaLabel}</span>
                <sly data-sly-test="${properties.typeExtra == 'cta-blog-grid'}">
                    <span class="blog-arrow right"></span>
                </sly>
            </a>
        </div>
    </div>
</div>

<div
    class="game-configurations"
    data-sly-list="${listItems}"
    style="display: none">
    <sly data-sly-use.scriptProcessorApi="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=item.properties.destinationUrl}" />
    <sly data-sly-use.gameIcon="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=item.properties.gameIconReference}"/>
    <sly data-sly-set.gameIconJp="${gameIcon.renditions['webp'].path || gameIcon.renditions['original'].path}"/>
    <div
        class="game-configuration"
        data-game-id="${item.properties.gameId}"
        data-icon="${gameIconJp}"
        data-icon-alt="${item.properties.gameIconAlt}"
        data-destination-url="${scriptProcessorApi.processedScript @ context='unsafe'}"></div>
</div>
