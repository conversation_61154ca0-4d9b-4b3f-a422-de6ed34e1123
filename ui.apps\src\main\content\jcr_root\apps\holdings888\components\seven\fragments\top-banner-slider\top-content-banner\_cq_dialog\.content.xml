<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Content Banner Configuration"
    extraClientlibs="[holdings888.richtext,core.wcm.components.contentfragment.v1.dialog,888poker.components.author.editor,holdings888.dialog.altText, holdings888.dialog.SAGenerator,888casino.components.author.editor,,rte.dialog.rich-text]"
    sling:resourceType="cq/gui/components/authoring/dialog">
    <content
        jcr:primaryType="nt:unstructured"
        granite:class="cmp-image__editor"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <bannerConfigurationTab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Configuration"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <image
                                jcr:primaryType="nt:unstructured"
                                jcr:title="Image"
                                sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <imagePC
                                            jcr:primaryType="nt:unstructured"
                                            path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                imagePrefixName="imagePC"
                                                imageIsRequired="{Boolean}true"
                                                altName="imageAlt"/>
                                    </imagePC>
                                    <imageTablet
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                        allowUpload="{Boolean}false"
                                        autoStart="{Boolean}false"
                                        class="cq-droptarget"
                                        fieldLabel="Tablet Asset"
                                        fileReferenceParameter="./imageTabletPath"
                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                        multiple="{Boolean}false"
                                        name="./imageFileReference"
                                        uploadUrl="${suffix.path}"
                                        useHTML5="{Boolean}true"
                                        required="{Boolean}true" />
                                    <imageMobile
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                        allowUpload="{Boolean}false"
                                        autoStart="{Boolean}false"
                                        class="cq-droptarget"
                                        fieldLabel="Mobile Asset"
                                        fileReferenceParameter="./imageMobilePath"
                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                        multiple="{Boolean}false"
                                        name="./imageFileReference"
                                        required="{Boolean}true"
                                        uploadUrl="${suffix.path}"
                                        useHTML5="{Boolean}true" />
                                </items>
                            </image>
                        </items>
                    </bannerConfigurationTab>
                    <ctaTab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="CTA configuration"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <headingType
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                level="{Long}3"
                                text="Primary CTA" />
                            <type
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                emptyOption="{Boolean}false"
                                multiple="{Boolean}false"
                                fieldLabel="CTA Style"
                                name="./type">
                                <items
                                    jcr:primaryType="nt:unstructured">
                                    <glow
                                        jcr:primaryType="nt:unstructured"
                                        text="CTA Glow (default)"
                                        selected="{Boolean}true"
                                        value="cta-glow" />
                                    <secondary
                                        jcr:primaryType="nt:unstructured"
                                        text="CTA Secondary"
                                        value="cta-secondary" />
                                    <noglow
                                        jcr:primaryType="nt:unstructured"
                                        text="CTA NO Glow"
                                        value="cta-no-glow" />   
                                </items>
                            </type>
                            <fontWeight
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                emptyOption="{Boolean}false"
                                fieldLabel="Font Weight"
                                multiple="{Boolean}false"
                                name="./fontSize">
                                <items
                                    jcr:primaryType="nt:unstructured">
                                    <bold
                                        jcr:primaryType="nt:unstructured"
                                        text="Bold (default)"
                                        selected="{Boolean}true"
                                        value="bold" />
                                    <regular
                                        jcr:primaryType="nt:unstructured"
                                        text="Regular"
                                        value="regular" />
                                </items>
                            </fontWeight>

                            <cta
                                    jcr:primaryType="nt:unstructured"
                                    path="holdings888/components/common888/dialog-include/cta-link"
                                    sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        fieldsetTitle="CTA Link"
                                        linkUrlName="url"
                                        urlIsRequired="{Boolean}false"
                                        linkLabelName="label"
                                        labelIsRequired="{Boolean}false"
                                        linkTitleName="linkTitleAttr"
                                        hideScript="{Boolean}false"
                                        scriptName="ctaScript"
                                />
                            </cta>
                            <secondaryCTA
                                    jcr:primaryType="nt:unstructured"
                                    namespace="secondaryCTA"
                                    path="holdings888/components/common888/dialog-include/cta-link"
                                    sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        fieldsetTitle="Secondary CTA url"
                                        urlIsRequired="{Boolean}false"
                                        hideLabel="{Boolean}true"
                                        labelIsRequired="{Boolean}false"
                                        hideScript="{Boolean}false"
                                        scriptName="ctaScript"
                                />
                            </secondaryCTA>
                        </items>
                    </ctaTab>
                    <contentTab
                        granite:class="cmp-contentfragment__editor"
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Content configuration"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <bannerTitle
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldDescription="This Banner Title will not display, use for attribute."
                                fieldLabel="Banner title"
                                name="./bannerTitle" />
                            <textForDesktop
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="acs-commons/granite/ui/components/include"
                                    path="holdings888/components/seven/dialog-include/rich-text">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        textName="textForDesktop"
                                        textLabel="Banner Offer Text For Desktop"/>
                            </textForDesktop>
                            <textForTablet
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="acs-commons/granite/ui/components/include"
                                    path="holdings888/components/seven/dialog-include/rich-text">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        textName="textForTablet"
                                        textLabel="Override Banner Offer Text For Tablet"/>
                            </textForTablet>
                            <textForMobile
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="acs-commons/granite/ui/components/include"
                                    path="holdings888/components/seven/dialog-include/rich-text">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        textName="textForMobile"
                                        textLabel="Override Banner Offer Text For Mobile"/>
                            </textForMobile>
                            <promoCode
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Promocode"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <promoCodeLink
                                            jcr:primaryType="nt:unstructured"
                                            namespace="promoCode"
                                            path="holdings888/components/common888/dialog-include/cta-link"
                                            sling:resourceType="acs-commons/granite/ui/components/include">
                                        <parameters
                                                jcr:primaryType="nt:unstructured"
                                                fieldsetTitle="Promo code link"
                                                linkUrlName="promoCodeLink"
                                                urlIsRequired="{Boolean}false"
                                                linkLabelName="promoCodeText"
                                                labelIsRequired="{Boolean}false"
                                                hideScript="{Boolean}false"
                                                scriptName="promoCodeScript"
                                        />
                                    </promoCodeLink>
                                    <promoLabel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                        fieldLabel="Promo code Label"
                                        name="./promoCode/promoLabel" />
                                </items>
                            </promoCode>
                            <disclaimer
                                    jcr:primaryType="nt:unstructured"
                                    jcr:title="Disclaimer"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                <items jcr:primaryType="nt:unstructured">
                                    <disclaimerSource
                                        granite:class="cq-dialog-dropdown-showhide"
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                        fieldLabel="Select source of disclaimer text"
                                        name="./disclaimerSource">
                                        <items jcr:primaryType="nt:unstructured">
                                            <default
                                                jcr:primaryType="nt:unstructured"
                                                selected="{Boolean}true"
                                                text="Free Text"
                                                value="freetext" />
                                            <icons
                                                jcr:primaryType="nt:unstructured"
                                                text="Content Fragment"
                                                value="cf" />
                                        </items>
                                        <granite:data
                                            jcr:primaryType="nt:unstructured"
                                            cq-dialog-dropdown-showhide-target=".disclaimerSource-showhide-target" />
                                    </disclaimerSource>
                                    <freetext
                                        granite:class="disclaimerSource-showhide-target"
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <disclaimer
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="acs-commons/granite/ui/components/include"
                                                    path="holdings888/components/seven/dialog-include/rich-text">
                                                <parameters
                                                        jcr:primaryType="nt:unstructured"
                                                        textName="disclaimer"
                                                        textLabel="Disclaimer"/>
                                            </disclaimer>
                                        </items>
                                        <granite:data
                                            jcr:primaryType="nt:unstructured"
                                            showhidetargetvalue="freetext" />
                                    </freetext>
                                    <cf
                                        granite:class="disclaimerSource-showhide-target"
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <fragmentPath
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="dam/cfm/components/cfpicker"
                                                emptyText="Enter or select Content Fragment"
                                                fieldDescription="Path to the Content Fragment to display."
                                                fieldLabel="Disclaimer from Content Fragment"
                                                name="./fragmentPath"
                                                pickerTitle="Select Content Fragment"
                                                rootPath="/content/dam/holdings888"/>
                                            <displayMode
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/radiogroup"
                                                deleteHint="{Boolean}false"
                                                renderHidden="{Boolean}true"
                                                fieldDescription="Single Text Element mode allows selection of one multiline text element and enables paragraph control options. Multiple Elements mode allows selection of one or more elements of the Content Fragment."
                                                fieldLabel="Display Mode"
                                                value="singleText"
                                                name="./displayMode"
                                                vertical="{Boolean}false">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    display-mode-radio-group="{Boolean}true"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <singleText
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Single Text Element"
                                                        checked="{Boolean}true"
                                                        value="singleText"/>
                                                    <multi
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Multiple Elements"
                                                        value="multi"/>
                                                </items>
                                            </displayMode>
                                            <elementNames
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <singleText
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                        emptyText="Select"
                                                        fieldDescription="Specify which element to display."
                                                        fieldLabel="Element *"
                                                        name="./elementNames">
                                                        <datasource
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="core/wcm/components/contentfragment/v1/datasource/elements"
                                                            componentPath="${requestPathInfo.suffix}"
                                                            fragmentPath="${param.fragmentPath}" />
                                                        <granite:rendercondition
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="core/wcm/components/contentfragment/v1/renderconditions/elementnames"
                                                            componentPath="${empty requestPathInfo.suffix ? param.componentPath : requestPathInfo.suffix}"
                                                            displayMode="singleText" />
                                                        <granite:data
                                                            jcr:primaryType="nt:unstructured"
                                                            single-text-selector="{Boolean}true" />
                                                    </singleText>
                                                </items>
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    element-names-container="{Boolean}true"
                                                    field-path="${requestPathInfo.resourcePath}" />
                                            </elementNames>
                                            <variationName
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                fieldDescription="Specify which variation to display"
                                                fieldLabel="Variation *"
                                                name="./variationName">
                                                <datasource
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="core/wcm/components/contentfragment/v1/datasource/variations"
                                                    componentPath="${requestPathInfo.suffix}"
                                                    fragmentPath="${param.fragmentPath}" />
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    field-path="${requestPathInfo.resourcePath}" />
                                            </variationName>
                                            <paragraphControls
                                                jcr:primaryType="nt:unstructured"
                                                jcr:title="Paragraph Control"
                                                granite:hidden="{Boolean}true"
                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                margin="{Boolean}true">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <paragraphControls
                                                        granite:class="cmp-contentfragment__editor-paragraph-controls"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                                                        <granite:data
                                                            jcr:primaryType="nt:unstructured"
                                                            field-path="${requestPathInfo.resourcePath}"/>
                                                        <items jcr:primaryType="nt:unstructured">
                                                            <content
                                                                jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                <items jcr:primaryType="nt:unstructured">
                                                                    <paragraphScope
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/radiogroup"
                                                                        deleteHint="{Boolean}false"
                                                                        fieldLabel="Paragraphs"
                                                                        name="./paragraphScope"
                                                                        vertical="{Boolean}false">
                                                                        <items jcr:primaryType="nt:unstructured">
                                                                            <all
                                                                                jcr:primaryType="nt:unstructured"
                                                                                checked="{Boolean}true"
                                                                                text="All"
                                                                                value="all"/>
                                                                            <range
                                                                                jcr:primaryType="nt:unstructured"
                                                                                text="Range"
                                                                                value="range"/>
                                                                        </items>
                                                                    </paragraphScope>
                                                                    <paragraphRange
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldDescription="Specify ranges of paragraphs which should be displayed, separated by ';'. For instance '1; 3-5; 7; 9-*' to include the 1st and 7th paragraphs, the 3rd to 5th ones, and from the 9th to the end."
                                                                        fieldLabel="\0"
                                                                        name="./paragraphRange"
                                                                        validation="cfm.paragraphRange"/>
                                                                    <paragraphHeadings
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                                        name="./paragraphHeadings"
                                                                        text="Handle headings as paragraphs"
                                                                        value="true"/>
                                                                </items>
                                                            </content>
                                                        </items>
                                                    </paragraphControls>
                                                </items>
                                            </paragraphControls>
                                        </items>
                                        <granite:data
                                            jcr:primaryType="nt:unstructured"
                                            showhidetargetvalue="cf" />
                                    </cf>
                                    <disclaimerDarkBgr
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                            text="Dark disclaimer background"
                                            fieldDescription="When checked, disclaimer background will be dark."
                                            name="./disclaimerDarkBgr"
                                            value="{Boolean}true"
                                            uncheckedValue="false" />
                                </items>
                            </disclaimer>
                        </items>
                    </contentTab>
                    <automationTab
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Automation configurations"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <bannerWrapperAttributeList
                                jcr:primaryType="nt:unstructured"
                                path="holdings888/components/common888/dialog-include/attribute-list"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                    jcr:primaryType="nt:unstructured"
                                    labelName="Attributes for Banner Wrapper"
                                    attributeNodeName="bannerWrapperAttributeList"
                                />
                            </bannerWrapperAttributeList>
                            <ctaAttributeList
                                jcr:primaryType="nt:unstructured"
                                path="holdings888/components/common888/dialog-include/attribute-list"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                    jcr:primaryType="nt:unstructured"
                                    labelName="Attributes for CTA"
                                    attributeNodeName="ctaAttributeList"
                                />
                            </ctaAttributeList>
                            <secondaryCtaAttributeList
                                jcr:primaryType="nt:unstructured"
                                path="holdings888/components/common888/dialog-include/attribute-list"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                    jcr:primaryType="nt:unstructured"
                                    labelName="Attributes for Secondary CTA"
                                    attributeNodeName="secondaryCtaAttributeList"
                                />
                            </secondaryCtaAttributeList>
                            <promocodeAttributeList
                                jcr:primaryType="nt:unstructured"
                                path="holdings888/components/common888/dialog-include/attribute-list"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                    jcr:primaryType="nt:unstructured"
                                    labelName="Attributes for Promocode"
                                    attributeNodeName="promocodeAttributeList"
                                />
                            </promocodeAttributeList>
                        </items>
                    </automationTab>
                    <targetConfig
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/include"
                            path="holdings888/components/dialog-include/target-config"/>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>