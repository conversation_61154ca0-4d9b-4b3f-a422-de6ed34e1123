
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.bannerlink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='bannerImageNode'}" />
<sly data-sly-test.hasContent="${properties.defaultImageReference || properties.bannerImageReference || properties.imageTextLink || bannerLink}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html" />

<!-- Get the start and end dates -->
<sly data-sly-set.startDate="${properties.explorerBannerStartDate.timeInMillis}" />
<sly data-sly-set.endDate="${properties.explorerBannerEndDate.timeInMillis}" />

<!-- Default and banner images -->
<sly data-sly-set.defaultImageReference="${properties.defaultImageReference}" />
<sly data-sly-set.bannerImageReference="${properties.bannerImageReference}" />

<!-- Determine which image to use based on date conditions -->
<sly data-sly-set.imagePath="${defaultImageReference}" /> <!-- Default Image Path-->
<sly data-sly-set.bannerImagePath="${bannerImageReference}" /> <!-- Banner Image Path -->

<sly data-sly-use.bannerScriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.explorerBannerScript}" />

<sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.defaultBannerScript}" />

<sly data-sly-test="${!properties.imageSizingOptions || properties.imageSizingOptions == 'PecentageImageSizing'}"> 

    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        class="image-component img-${properties.imageSize} ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''}">

        <div
            class="image-container ${properties.enableModal && !properties.imageTextLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.imageTextLink}" />
            <sly data-sly-use.bannerTextLink="${'holdings888.core.models.LinkModel' @ urlToProcess=bannerLink.properties.bannerImageTextLink}" />
            <sly data-sly-test="${properties.bannerImageReference}">
                <sly data-sly-test="${startDate && endDate}">
                    <!-- Return the div with data for front-end modification -->
                    <div id="banner-data-container"
                    data-start-date="${startDate}"
                    data-end-date="${endDate}"></div>
                </sly>
            </sly>

            <!-- Default image -->
            <div id="default-banner__container">
                <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />

                <a
                    onclick="${scriptProcessor.processedScript @ context='unsafe'}"  
                    data-sly-test="${properties.imageTextLink}"
                    href="${textLink.relativePublishLink}"
                    data-sly-attribute.target="${properties.target}"
                    data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                    data-sly-attribute.rel="${properties.rel}"
                    data-sly-attribute.title="${properties.linkTitleAttr}"
                    data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                    data-sly-unwrap="${!textLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </a>
                <sly data-sly-test="${!properties.imageTextLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </sly>
            </div>

            <!-- Explorer banner -->
            <sly data-sly-test="${startDate && endDate}">
                <div id="explorer-banner__container" style="display: none;">
                    <sly data-sly-test.bannerEnableDownloadHeading="${bannerLink.properties.linkDownload == 'enableDownload'}" />
                        <a
                            onclick="${bannerScriptProcessor.processedScript @ context='unsafe'}"  
                            data-sly-test="${bannerLink.properties.bannerImageTextLink}"
                            href="${bannerTextLink.relativePublishLink}"
                            data-sly-attribute.target="${bannerLink.properties.target}"
                            data-sly-attribute.referrerpolicy="${bannerLink.properties.referrerpolicy}"
                            data-sly-attribute.rel="${bannerLink.properties.rel}"
                            data-sly-attribute.title="${bannerLink.properties.linkTitleAttr}"
                            data-sly-attribute.download="${bannerEnableDownloadHeading ? (bannerLink.properties.linkFilename ? bannerLink.properties.linkFilename : true ) : false }"
                            data-sly-unwrap="${!bannerTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=bannerImagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </a>
                        <sly data-sly-test="${!properties.imageTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </sly>

                </div>
            </sly>

        </div>

        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>

        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, cssClassNames='modal-image js-modal-image explorer-banner-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>

<sly data-sly-test="${properties.imageSizingOptions == 'FixedMinMaxWidth'}">
    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        max-width="${properties.maxWidthFixed}"
        min-width="${properties.minWidth}"
        class="image-component ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''}">
        <div
            class="image-container ${properties.enableModal && !properties.imageTextLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.imageTextLink}" />
            <sly data-sly-use.bannerTextLink="${'holdings888.core.models.LinkModel' @ urlToProcess=bannerLink.properties.bannerImageTextLink}" />
            <sly data-sly-test="${properties.bannerImageReference}">
                <sly data-sly-test="${startDate && endDate}">
                    <!-- Return the div with data for front-end modification -->
                    <div id="banner-data-container"
                    data-start-date="${startDate}"
                    data-end-date="${endDate}"></div>
                </sly>
            </sly>

            <!-- Default image -->
            <div id="default-banner__container">
                <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />

                <a
                    onclick="${scriptProcessor.processedScript @ context='unsafe'}"  
                    data-sly-test="${properties.imageTextLink}"
                    href="${textLink.relativePublishLink}"
                    data-sly-attribute.target="${properties.target}"
                    data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                    data-sly-attribute.rel="${properties.rel}"
                    data-sly-attribute.title="${properties.linkTitleAttr}"
                    data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                    data-sly-unwrap="${!textLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </a>
                <sly data-sly-test="${!properties.imageTextLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </sly>
            </div>

            <!-- Explorer banner -->
            <sly data-sly-test="${startDate && endDate}">
                <div id="explorer-banner__container" style="display: none;">
                    <sly data-sly-test.bannerEnableDownloadHeading="${bannerLink.properties.linkDownload == 'enableDownload'}" />
                        <a
                            onclick="${bannerScriptProcessor.processedScript @ context='unsafe'}"  
                            data-sly-test="${bannerLink.properties.bannerImageTextLink}"
                            href="${bannerTextLink.relativePublishLink}"
                            data-sly-attribute.target="${bannerLink.properties.target}"
                            data-sly-attribute.referrerpolicy="${bannerLink.properties.referrerpolicy}"
                            data-sly-attribute.rel="${bannerLink.properties.rel}"
                            data-sly-attribute.title="${bannerLink.properties.linkTitleAttr}"
                            data-sly-attribute.download="${bannerEnableDownloadHeading ? (bannerLink.properties.linkFilename ? bannerLink.properties.linkFilename : true ) : false }"
                            data-sly-unwrap="${!bannerTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=bannerImagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </a>
                        <sly data-sly-test="${!properties.imageTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </sly>

                </div>
            </sly>

        </div>
        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>


        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, cssClassNames='modal-image js-modal-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>

<sly data-sly-test="${properties.imageSizingOptions == 'FixedMaxWidthAndPercentageWidth'}">
    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        max-width="${properties.maxWidth[1]}"
        width="${properties.width}"
        class="image-component ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''}">
        <div
            class="image-container ${properties.enableModal && !properties.imageTextLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.imageTextLink}" />
            <sly data-sly-use.bannerTextLink="${'holdings888.core.models.LinkModel' @ urlToProcess=bannerLink.properties.bannerImageTextLink}" />
            <sly data-sly-test="${properties.bannerImageReference}">
                <sly data-sly-test="${startDate && endDate}">
                    <!-- Return the div with data for front-end modification -->
                    <div id="banner-data-container"
                    data-start-date="${startDate}"
                    data-end-date="${endDate}"></div>
                </sly>
            </sly>

            <!-- Default image -->
            <div id="default-banner__container">
                <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />

                <a
                    onclick="${scriptProcessor.processedScript @ context='unsafe'}"  
                    data-sly-test="${properties.imageTextLink}"
                    href="${textLink.relativePublishLink}"
                    data-sly-attribute.target="${properties.target}"
                    data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                    data-sly-attribute.rel="${properties.rel}"
                    data-sly-attribute.title="${properties.linkTitleAttr}"
                    data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                    data-sly-unwrap="${!textLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </a>
                <sly data-sly-test="${!properties.imageTextLink}">
                    <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, loading=properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                </sly>
            </div>

            <!-- Explorer banner -->
            <sly data-sly-test="${startDate && endDate}">
                <div id="explorer-banner__container" style="display: none;">
                    <sly data-sly-test.bannerEnableDownloadHeading="${bannerLink.properties.linkDownload == 'enableDownload'}" />
                        <a
                            onclick="${bannerScriptProcessor.processedScript @ context='unsafe'}"  
                            data-sly-test="${bannerLink.properties.bannerImageTextLink}"
                            href="${bannerTextLink.relativePublishLink}"
                            data-sly-attribute.target="${bannerLink.properties.target}"
                            data-sly-attribute.referrerpolicy="${bannerLink.properties.referrerpolicy}"
                            data-sly-attribute.rel="${bannerLink.properties.rel}"
                            data-sly-attribute.title="${bannerLink.properties.linkTitleAttr}"
                            data-sly-attribute.download="${bannerEnableDownloadHeading ? (bannerLink.properties.linkFilename ? bannerLink.properties.linkFilename : true ) : false }"
                            data-sly-unwrap="${!bannerTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=bannerImagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </a>
                        <sly data-sly-test="${!properties.imageTextLink}">
                            <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=bannerLink.properties.imageAlt, loading=bannerLink.properties.isFetchPriorityHigh, cssClassNames='explorer-banner-image'}" />
                        </sly>

                </div>
            </sly>

        </div>
        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>

        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=imagePath, alt=properties.imageAlt, cssClassNames='modal-image js-modal-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>

<script>
    
  function compareDatesThenToggle() {
    try {
          const container = document.querySelector("#banner-data-container");
          const defaultImageContainer = document.querySelector("#default-banner__container");
          const bannerImageContainer = document.querySelector("#explorer-banner__container");

          const startDate = new Date(
              +container?.getAttribute("data-start-date"),
          ).getTime();
          const endDate = new Date(
              +container?.getAttribute("data-end-date"),
          ).getTime();
          const dateToday = new Date().getTime();
          const bannerImageReference = container?.getAttribute(
              "data-banner-image-reference",
          );

          if (dateToday >= startDate && dateToday <= endDate) {
              if (defaultImageContainer) defaultImageContainer.style.display = "none";
              if (bannerImageContainer) bannerImageContainer.style.display = "block";
          } else {
              if (defaultImageContainer) defaultImageContainer.style.display = "block";
          }
    } catch (error) {
          console.error("Error in compareDatesThenToggle: ", error);
    }
  }

  compareDatesThenToggle();


</script>
