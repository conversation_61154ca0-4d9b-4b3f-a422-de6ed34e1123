<template data-sly-template.head="${ @ pageModel, pwa, version}"
          data-sly-test.page="${pageModel.page}"
          data-sly-use.headlibRenderer="headlibs.html"
          data-sly-use.headResources="head.resources.html"
          data-sly-use.headLinks="head.links.html"
          data-sly-use.script="script.html">
    <meta charset="UTF-8">
    <title>${properties.pageTitle || page.title @ context='html'}</title>
    <meta data-sly-test="${inheritedPageProperties['cq:robotsTags']}" name="robots" content="${inheritedPageProperties['cq:robotsTags'] @ join=', '}">
    <meta data-sly-test.keywords="${page.keywords}" name="keywords" content="${keywords}"/>
    <meta data-sly-test.description="${page.description || properties['jcr:description']}" name="description" content="${description}"/>
    <meta data-sly-test.templateName="${page.templateName}" name="template" content="${templateName}"/>
    <meta data-sly-test.frontendVersion="${version.frontendVersion}" name="frontendVersion" content="${frontendVersion}"/>
    <meta data-sly-test.backendVersion="${version.backendVersion}" name="backendVersion" content="${backendVersion}"/>
    <meta data-sly-test.contentVersion="${version.contentVersion}" name="contentVersion" content="${contentVersion}"/>
    <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1, maximum-scale=1 user-scalable=0, minimal-ui">

    <meta data-sly-test.ogTitle="${properties.ogTitle}" property="og:title" content="${ogTitle}"/>
    <meta data-sly-test.ogUrl="${properties.ogUrl}" property="og:url" content="${ogUrl}"/>
    <meta data-sly-test.ogType="${properties.ogType}" property="og:type" content="${ogType}"/>
    <meta data-sly-test.ogImage="${properties.ogImage}" property="og:image" content="${pageModel.getbaseURL}${ogImage}"/>
    <meta data-sly-test.ogDescription="${properties.ogDescription}" property="og:description" content="${ogDescription}"/>
    <meta data-sly-test.ogUpdatedTime="${properties.ogUpdatedTime}" property="og:updated_time" content="${'YYYY-MM-dd HH:mm' @ format=properties.ogUpdatedTime}"/>
    
    <sly data-sly-test.additionalMetaData="${pageModel.additionalMetaData}" data-sly-list.additionalMetaData="${additionalMetaData}">
        <meta name="${additionalMetaData.metaKey}" content="${additionalMetaData.metaValue}">
    </sly>

    <meta data-sly-test="${inheritedPageProperties.client}" name="${inheritedPageProperties.client}" content="true">

    <sly data-sly-test.HttpEquivMetaData="${pageModel.HttpEquivMetaData}" data-sly-list.HttpEquivMetaData="${HttpEquivMetaData}">
        <meta http-equiv="${HttpEquivMetaData.metaEquiv}" content="${HttpEquivMetaData.metaContent}">
    </sly>
    <sly data-sly-test="${pwa.enabled}">
        <link rel="manifest" href="${pwa.manifestPath}" crossorigin="use-credentials"/>
        <meta name="theme-color" content="${pwa.themeColor}"/>
        <link rel="apple-touch-icon" href="${pwa.iconPath}"/>
        <sly data-sly-use.clientlib="core/wcm/components/commons/v1/templates/clientlib.html"
             data-sly-call="${clientlib.css @ categories='core.wcm.components.page.v2.pwa'}"></sly>
        <meta name="cq:sw_path" content="${pwa.serviceWorkerPath @ context ='text'}"/>
    </sly>

    <sly data-sly-test="${pageModel.externalJsCssHeadTop}">
        <! -- External CSS JS Head Top -->
        ${pageModel.externalJsCssHeadTop @ context='unsafe'}
        <! -- / External CSS JS Head Top -->
    </sly>

    <sly data-sly-call="${headLinks.headLinks @ pageModel = pageModel}"></sly>
    <sly data-sly-call="${headlibRenderer.headlibs @
                                page                      = page,
                                designPath                = page.designPath,
                                staticDesignPath          = page.staticDesignPath,
                                clientLibCategories       = page.clientLibCategories,
                                clientLibCategoriesJsHead = page.clientLibCategoriesJsHead,
                                hasCloudconfigSupport     = page.hasCloudconfigSupport}"></sly>

    <sly data-sly-call="${script.head @ pageModel = pageModel}"></sly>
    <link data-sly-test="${pageModel.favicon}" rel="shortcut icon" href="${pageModel.favicon}">
    <sly data-sly-test="${pageModel.externalJsCssHeadBottom}">
        <! -- External CSS JS Head Bottom -->
        ${pageModel.externalJsCssHeadBottom @ context='unsafe'}
        <! -- / External CSS JS Head Bottom -->
    </sly>
</template>
