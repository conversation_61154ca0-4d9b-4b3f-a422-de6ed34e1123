.breadcrumb {

  .breadcrumb-component {

    [dir="rtl"] & {
      .list {
        display: flex;
        direction: rtl;
      }
    }

    .list {
      margin: 0;
      padding: 0;
      display: flex;
      flex-wrap: wrap;

      .item, .item .link {
        font-size: $font-size;
        font-weight: $font-weight-bold;
        letter-spacing: 0.25rem;
        text-transform: uppercase;
        color: $black;
        font-family: $font-family;
        line-height: 1.5rem;

        &.hoverable {
          cursor: pointer;
        }
      }

      .item {
        display: inline;

        &:before {
          content: '>';
          color: $black;
          cursor: auto;
          font-weight: $font-weight-light;
          font-size: $font-size-18;
          letter-spacing: 0.25rem;
          line-height: 1.5rem;
        }

        .link {
          text-decoration: none;
          margin-right: 8px
        }

        span {
          margin: 0;
        }

        &:first-child {
          span {
            margin-left: 0;
          }

          &:before {
            content:'';
          }
        }
      }
    }

    &.inner-poker-breadcrumb {
      .main-container {
        padding: 0;
      }

      .list {
        .item, .item .link {
          font-size: 1.8rem;
          font-weight: $font-weight-bold;
          letter-spacing: 0.25rem;
          text-transform: uppercase;
          color: $white;
          font-family: $font-family-8;
          line-height: 2.7rem;

          @media (max-width: 768px) {
            font-size: 1.2rem;
            line-height: 2rem;
          }
        }

        .item {
          &:before {
            content: '>';
            color: $white;
            cursor: auto;
            font-weight: $font-weight-light;
            font-size: 1.8rem;
            letter-spacing: 0.25rem;
            line-height: 2.7rem;

            @media (max-width: 768px) {
              font-size: 1.2rem;
              line-height: 2rem;
            }
          }

          &:first-child {
            span {
              margin-left: 0;
            }
  
            &:before {
              content:'';
            }
          }
        } 
      }

    }
  }
}

.inner-poker-breadcrumb__height {
  min-height: 2.6rem;
}

.inner-poker-breadcrumb__padding {
  padding: 1rem 1rem 0 0 !important;
}