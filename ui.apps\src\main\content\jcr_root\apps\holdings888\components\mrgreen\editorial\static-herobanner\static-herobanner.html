<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${properties.imageLinkDesktopReference}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.parentLink="${'holdings888.core.models.LinkModel' @ urlToProcess=currentPage.parent.path}" />

<sly data-sly-test="${hasContent}">
    <div
        class="static-herobanner-component ${properties.paddingImg ? 'img-padding' : ' '}"
        data-mbox-id="${properties.mboxId}">
        <div
            class="herobanner-media ${properties.sameDeskMob ? 'same-img-desk-mob' : ' '} ${properties.increaseImageHeight ? 'increase-image-height' : ' '} ${properties.increaseImageHeight200 ? 'increase-image-height-200' : ' '} ${wcmmode.edit ? 'edit-mode' : ''}"
            data-type="${properties.mediaSelect == 'video' ? 'video' : 'image'}">
            <sly data-sly-use.processorDesktopVideo="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.videoDesktopPreviewImageReference}" />
            <sly data-sly-set.posterDesktop="${processorDesktopVideo.renditions['webp'].path || processorDesktopVideo.renditions['original'].path}" />
            <sly data-sly-use.processorMobileVideo="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.videoMobilePreviewImageReference}" />
            <sly data-sly-set.posterMobile="${processorMobileVideo.renditions['webp'].path || processorMobileVideo.renditions['original'].path}" />
            <sly data-sly-set.dimensionDesktop="${processorDesktopVideo.renditions['webp'] || processorDesktopVideo.renditions['original']}" />
            <sly data-sly-set.dimensionMobile="${processorMobileVideo.renditions['webp']|| processorMobileVideo.renditions['original']}" />

            <div
                class="herobanner-video"
                data-sly-test="${properties.mediaSelect == 'video'}"
                data-video-desktop="${properties.videoDesktop}"
                data-video-mobile="${properties.videoMobile}"
                data-video-alt-desktop="${properties.desktopAltVideo}"
                data-poster-desktop="${posterDesktop}"
                data-poster-mobile="${posterMobile}"
                data-dimension-desktop-height="${dimensionDesktop.height}"
                data-dimension-desktop-width="${dimensionDesktop.width}"
                data-dimension-mobile-height="${dimensionMobile.height}"
                data-dimension-mobile-width="${dimensionMobile.width}">
                <video
                    aria-describedby="${properties.desktopAltVideo}"
                    poster="${posterDesktop}"
                    autoplay
                    loop
                    muted
                    playsinline
                    width=""
                    height=""></video>
            </div>

            <sly data-sly-use.processorDesktopImage="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageLinkDesktopReference}" />
            <sly data-sly-set.webpDesktop="${processorDesktopImage.renditions['webp'] || processorDesktopImage.renditions['original']}" />
            <sly data-sly-use.processorMobileImage="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageLinkMobileReference}" />
            <sly data-sly-set.webpMobile="${processorMobileImage.renditions['webp'] || processorMobileImage.renditions['original']}" />

            <div
                class="herobanner-image ${properties.roundCorners ? 'img-round-corners' : ''}"
                data-sly-test="${properties.mediaSelect == 'image' && webpDesktop.path}"
                data-image-desktop="${webpDesktop.path}"
                data-image-mobile="${webpMobile.path}"
                data-image-alt-desktop="${properties.desktopAltImage}"
                data-sly-set.loading="${properties.isFetchPriorityHigh}">
                <img
                    src="${webpDesktop.path}"
                    loading="${!loading ? 'lazy' : ''}"
                    fetchpriority="${loading ? 'high' : ''}"
                    alt="${properties.desktopAltImage}" />
            </div>
        </div>
    </div>
</sly>
<sly data-sly-test="${hasContent}">
    <sly data-sly-test.hasImage="${webpDesktop.path}" />
    <sly data-sly-call="${template.placeholder @ isEmpty=!hasImage}" />
</sly>
<sly data-sly-test="${properties.enableBackToPrevious}">
    <span class="back-to-previous-component">
        <button
            class="promotion-page__back-to-btn ${properties.sameDeskMob ? 'same-img-desk-mob' : ' '}"
            aria-label="${properties.label}">
            <a href="${parentLink.relativePublishLink}">
                <svg
                    viewBox="0 0 24 24"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg">
                    <g>
                        <path
                            fill="currentColor"
                            d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                    </g>
                </svg>
            </a>
        </button>
    </span>
</sly>
