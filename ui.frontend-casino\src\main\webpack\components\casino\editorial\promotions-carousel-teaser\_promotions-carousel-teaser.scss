.promotions-teaser-component {
    scroll-snap-align: start;
    border: 0.1rem solid $casino-promotion-teaser-border;
    background: $black;
    border-radius: 0.8rem;
    display: flex;
    width: 100%;
    flex: 0 0 40%;
    @media (max-width: $md-max) {
        flex: 0 0 49%;
    }
    @media (max-width: $sm-air) {
        flex: 0 0 48.76%;
    }
    .promotions-teaser-wrapper {
        height: fit-content;
        color: $white;
        font-weight: 600;
        font-size: 1.5rem;
        margin: 0;
        width: 100%;
        box-sizing: border-box;
        flex-direction: column;
        .promotions-teaser {
            &__top {
                height: 14.3%;
                line-height: 1.5;
                color: #fff;
                font-weight: $font-weight-medium;
                font-size: 1.5rem;
                box-sizing: inherit;
                margin: 0;
                padding: 1em;
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 0.8rem;
                &__link {
                    box-sizing: inherit;
                    line-height: inherit;
                    outline: 0;
                    box-shadow: none;
                    background-color: transparent;
                    cursor: pointer;
                    color: $casino-promotion-top-link-color;
                    font-size: 12px;
                    font-weight: $font-weight-light;
                    text-decoration: underline;
                }
                &__title {
                    font-style: normal;
                    line-height: 1.2em;
                    display: flex;
                    align-items: center;
                    max-width: 80%;
                    min-height: 3rem;
                    margin: 0;
                    padding: 0;
                    font-size: 13px;
                    text-transform: uppercase;
                    border: none;
                    color: $white;
                    font-weight: $font-weight-medium;
                    @media (min-width: $sm) {
                        font-size: 12px;
                        font-weight: $font-weight-medium;
                    }
                }
            }
            &__img {
                line-height: 1.5;
                color: $white;
                box-sizing: inherit;
                border: 0;
                max-width: 100%;
                height: auto;
                display: inline-block;
                vertical-align: middle;
                width: 100%;
                img {
                    width: 100%;
                    height: auto;
                }
            }
            &__bot {
                line-height: 1.5;
                color: $white;
                font-weight: $font-weight-medium;
                font-size: 1.5rem;
                box-sizing: inherit;
                margin: 0;
                width: 100%;
                padding: 1em;
                display: flex;
                justify-content: space-between;
                align-items: start;
                &__text {
                    line-height: 2.4rem;
                    color: $white;
                    font-weight: 600;
                    box-sizing: inherit;
                    width: 65%;
                    display: block;
                    font-size: $font-size-16;
                    padding-right: 0.25em;
                    @media (max-width: 1024px) {
                        font-size: 1.2rem;
                        line-height: 1.44rem;
                        font-weight: 100;
                    }
                }
                &__cta-section {
                    line-height: 1.5;
                    color: $white;
                    font-weight: $font-weight-medium;
                    font-size: 1.2rem;
                    box-sizing: inherit;
                    @media (min-width: $sm) {
                        font-size: 1.5rem;
                    }
                    .cta-component {
                        display: flex;
                        flex-direction: column;
                        gap: 0.5em;
                        padding-top: 0;
                        a {
                            font-size: 15px;
                            min-width: 160px;
                            min-height: 35px;
                            width: 100%;
                            align-items: center;
                            padding: 0;
                            letter-spacing: normal;
                            font-family: $font-family-2;
                            @media (max-width: $md-max) {
                                font-size: 12px;
                                line-height: 12px;
                            }
                        }
                        .cta-primary {
                            a {
                                font-weight: $font-weight-bold;
                            }
                        }
                        .cta-secondary {
                            a {
                                font-weight: $font-weight-medium-bold;
                            }
                        }
                    }
                }
            }
        }
        .disclaimer {
            line-height: 1.5;
            font-weight: 600;
            box-sizing: inherit;
            color: #888;
            margin-bottom: 1em;
            padding: 0 1em;
            font-size: 1rem;
            font-family: $font-family-2;
            margin: 0 0 1.5em;
            margin-top: 0;
            @media (min-width: $sm) {
                font-size: 1.1rem;
            }
            a {
                font-weight: 600;
                font-size: 1rem;
                font-family: $font-family-2;
                box-sizing: inherit;
                line-height: inherit;
                outline: 0;
                box-shadow: none;
                background-color: transparent;
                cursor: pointer;
                color: #fff;
                text-decoration: underline;
                @media (min-width: $sm) {
                    font-size: 1.1rem;
                }
            }
        }
    }
}
