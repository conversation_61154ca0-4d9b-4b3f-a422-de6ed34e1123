<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.cta="holdings888/components/common888/htl-templates/cta-template.html" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.autoList="${'holdings888.core.models.AutomationListModel'}" />

<div
    class="content-banner-homepage ${properties.paddingTop} ${properties.paddingBottom}"
    data-mbox-id="${properties.mboxId}">
    <sly data-sly-use.imageDesktop="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageDesktopPathReference}"/>
    <sly data-sly-set.imageDesktopbackground="${imageDesktop.renditions['webp'].path || imageDesktop.renditions['original'].path}"/>
    <div
        class="image-pc"
        style="background-image: url('${imageDesktopbackground @ context='unsafe'}')"></div>
    <sly data-sly-use.imageTablet="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageTabletPathReference}"/>
    <sly data-sly-set.imageTabletbackground="${imageTablet.renditions['webp'].path || imageTablet.renditions['original'].path}"/>
    <div
        class="image-tablet"
        style="background-image: url('${imageTabletbackground @ context='unsafe'}')"></div>
    <sly data-sly-use.imageMobile="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageMobilePathReference}"/>
    <sly data-sly-set.imageMobilebackground="${imageMobile.renditions['webp'].path || imageMobile.renditions['original'].path}"/>
    <div
        class="image-mobile"
        style="background-image: url('${imageMobilebackground @ context='unsafe'}')"></div>
    <div class="content-banner-homepage-wrapper">
        <div class="text-container">
            <div
                class="content-info"
                data-contentbannertitle="${properties.bannerTitle}">
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text=properties.textForDesktop}" />
                <div class="rich-text display-pc">${richText.text @ context='html'}</div>
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text=properties.textForTablet ? properties.textForTablet : properties.textForDesktop}" />
                <div class="rich-text display-tablet">${richText.text @ context='html'}</div>
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text=properties.textForMobile ? properties.textForMobile : properties.textForDesktop}" />
                <div class="rich-text display-mobile">${richText.text @ context='html'}</div>
            </div>
            <div
                onmousedown="return false;"
                onselectstart="return false;"
                data-sly-test="${properties.url && properties.label}"
                class="cta-component d-flex ${properties.flexAlignment} ${properties.marginTopCTA} ${properties.marginBottomCTA}">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess= properties.url}" />
                <sly
                    data-sly-call="${cta.default @
                                    label       = properties.label,
                                    fontWeight  = properties.fontSize,
                                    ariaLabel   = properties.ariaLabel,
                                    url         = ctaLink.relativePublishLink,
                                    newWindow   = properties.newWindow,
                                    ctaType     = properties.type,
                                    ctaScript   = properties.ctaScript,
                                    ctaSize    = properties.ctaSize,
                                    ctaAddsCut  = ctaLink.addsCut}" />

            </div>
        </div>
    </div>
    <sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html" />
    <sly data-sly-call="${clientlibs.fe @ locations='casino-content-banner-homepage-css'}" />
</div>
<sly data-sly-call="${clientlibs.fe @ locations='casino-content-banner-homepage-js'}" />
