import Swiper, {Navigation} from 'swiper';


function ArticleSliderXF($cmp) {

    let selectors = {
        swiper: '.swiper-xf',
        slide: '.swiper-slide'
    };

    let $swiper = $cmp.querySelector(selectors.swiper);
    let $slides = $swiper.querySelectorAll(selectors.slide);

    initSwiperXF();

    function initSwiperXF() {
        let numOfSlides = $slides.length;

        new Swiper(".swiper-xf", {
                modules: [Navigation],
                centerInsufficientSlides: true,
                navigation: {
                    nextEl: '.swiper-button-next-xf',
                    prevEl: '.swiper-button-prev-xf',
                },
                breakpoints: {
                    320: {
                        enabled: numOfSlides > 1 ? true : false,
                        loop: false,
                        slidesPerView: 1,
                        loopedSlides: 999,
                        loopFillGroupWithBlank: false,
                        centeredSlides: true,
                        spaceBetween: 32
                    },
                    725: {
                        enabled: numOfSlides > 2 ? true : false,
                        loop: false,
                        slidesPerView: 3,
                        loopedSlides: 999,
                        loopFillGroupWithBlank: false,
                        spaceBetween: 32
                    },
                    1100: {
                        enabled: numOfSlides > 3 ? true : false,
                        loop: false,
                        slidesPerView: 5,
                        loopedSlides: 999,
                        loopFillGroupWithBlank: false,
                        spaceBetween: 32
                    }
                }
            });
    }
}

function sliderInitXF(){
    document.querySelectorAll('.article-slider-component-xf').forEach(function ($cmp) {
        ArticleSliderXF($cmp);
    });
}

sliderInitXF();
