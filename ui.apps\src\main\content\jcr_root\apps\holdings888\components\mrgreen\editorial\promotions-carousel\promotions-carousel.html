<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.promotionModel="${'holdings888.core.models.CasinoPromoPagesRender'}" />
<sly data-sly-test.hasContent="${!promotionModel.isEmpty}" />
<sly data-sly-test.hasCategory="${properties.category}" />
<sly data-sly-test.hasMboxId="${properties.mboxId}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasCategory && !hasMboxId}" />
<sly data-sly-set.categoryLeft="moveCarouselLeft('${properties.category}')" />
<sly data-sly-set.categoryRight="moveCarouselRight('${properties.category}')" />
<sly data-sly-use.idUtil="${'holdings888.core.models.CasinoIdUtils' @ category=properties.category}" />
<sly data-sly-set.modifiedId="${idUtil.getId}" />
<sly data-sly-include="clientlibs-css.html" />
    <div
        data-mbox-id="${properties.mboxId}"
        style="display: contents">
        <span data-sly-test="${wcmmode.edit && hasMboxId}"> MboxID: ${properties.mboxId}</span>
        <div
            data-sly-test="${hasContent && hasCategory}"
            class="promotion-carousel-component ${properties.category}"
            id="${modifiedId}">
            <div class="promotion-carousel-container ${properties.paddingTop} ${properties.paddingBottom}">
                <div class="promotions-carousel-top">
                    <div class="promotions-carousel-title">
                        <h2>${properties.category}</h2>
                    </div>
                    <div
                        class="arrows"
                        id="arrows">
                        <div
                            class="arrow-left"
                            id="arrow-left"
                            onclick="${categoryLeft @ context='unsafe'}">
                            <svg
                                width="1.8em"
                                height="1.8em"
                                viewBox="0 0 30 30"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                                class="sc-fzoant iQBAoJ"
                                style="display: block">
                                <g>
                                    <path
                                        fill="currentColor"
                                        d="M12.3076 8.56933C12.7946 8.08231 13.5842 8.08231 14.0713 8.56933L19.6565 14.1546C20.1435 14.6416 20.1435 15.4312 19.6565 15.9182C19.6459 15.9288 19.6352 15.9392 19.6244 15.9493L14.07 21.5036C13.583 21.9907 12.7934 21.9907 12.3064 21.5036C11.8194 21.0166 11.8194 20.227 12.3064 19.74L17.0105 15.0359L12.3076 10.333C11.8206 9.84594 11.8206 9.05634 12.3076 8.56933Z"></path>
                                </g>
                            </svg>
                        </div>
                        <div
                            class="arrow-right"
                            id="arrow-right"
                            onclick="${categoryRight @ context='unsafe'}">
                            <svg
                                width="1.8em"
                                height="1.8em"
                                viewBox="0 0 30 30"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                                class="sc-fzoant iQBAoJ"
                                style="display: block">
                                <g>
                                    <path
                                        fill="currentColor"
                                        d="M12.3076 8.56933C12.7946 8.08231 13.5842 8.08231 14.0713 8.56933L19.6565 14.1546C20.1435 14.6416 20.1435 15.4312 19.6565 15.9182C19.6459 15.9288 19.6352 15.9392 19.6244 15.9493L14.07 21.5036C13.583 21.9907 12.7934 21.9907 12.3064 21.5036C11.8194 21.0166 11.8194 20.227 12.3064 19.74L17.0105 15.0359L12.3076 10.333C11.8206 9.84594 11.8206 9.05634 12.3076 8.56933Z"></path>
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="swiper">
                    <div
                        class="swiper-wrapper"
                        id="swiper-wrapper-${properties.category}">
                        <sly data-sly-list.promotion="${promotionModel.promotionList}">
                            <sly data-sly-test="${promotion.category == properties.category}">
                                <sly data-sly-use.componentTemplate="promotions-teaser-template.html" />
                                <sly
                                    data-sly-call="${componentTemplate.default @ 
                                TopText = promotion.topTitle, 
                                topLinkText=promotion.topLinkText, 
                                topLink=promotion.topLinkUrl.relativePublishLink,
                                imagePath=promotion.imageRef, 
                                imageAlt=promotion.imageAlt, 
                                bottomText=promotion.bottomText, 
                                topCtaText=promotion.topCtaLinkText, 
                                topCtaLink=promotion.topCtaLink, 
                                topCtaScript=promotion.topCtaScript,
                                bottomCtaGlowing=promotion.bottomCtaGlowing,
                                bottomCtaText=promotion.bottomCtaLinkText,
                                bottomCtaLink=promotion.bottomCtaLink, 
                                bottomCtaScript=promotion.bottomCtaScript, 
                                link=promotion.link.relativePublishLink, 
                                category=promotion.category,
                                mboxId=promotion.mboxId,
                                bottomLinkText=promotion.bottomLinkText,
                                disclaimerLink=promotion.disclaimerLinkUrl.relativePublishLink,
                                extraText=promotion.extraText,
                                additionalTextOptions=promotion.isBefore,
                                freeRichText=promotion.text}" />
                            </sly>
                        </sly>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
        <sly data-sly-include="clientlibs-js.html" />
        <script>
            (function () {
                let $originalCarouselPosition = 0;
                let $maxDist = 0;

                window.moveCarouselLeft = function (category) {
                    let carouselName = "promotion-carousel-component ".concat(category);
                    carouselName.replace(".", " ");
                    const wrapperElements = document.getElementsByClassName(carouselName);
                    if ($originalCarouselPosition < 0) {
                        const offSet = window.matchMedia('screen and (max-width: 1024px)').matches ? 99.1 : 81.1;
                        $originalCarouselPosition += offSet;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    }
                };

                window.moveCarouselRight = function (category) {
                    const wrapperElements = document.getElementsByClassName("promotion-carousel-component " + category);
                    const promotionElements = document
                        .getElementsByClassName("promotion-carousel-component " + category)[0]
                        .getElementsByClassName("promotions-teaser-component").length;
                    $maxDist = (promotionElements - 2) * -40;
                    if ($originalCarouselPosition > $maxDist) {
                        const offSet = window.matchMedia('screen and (max-width: 1024px)').matches ? 99.1 : 81.1;
                        $originalCarouselPosition -= offSet;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    } else {
                        $originalCarouselPosition = 0;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    }
                };
            })();
        </script>
    </div>

