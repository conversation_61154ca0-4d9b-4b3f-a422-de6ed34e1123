<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
          jcr:primaryType="nt:unstructured"
          jcr:title="Banner Image"
          sling:resourceType="granite/ui/components/coral/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <columns
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                margin="{Boolean}true">
            <items jcr:primaryType="nt:unstructured">
                <column
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="granite/ui/components/coral/foundation/container">
                    <items
                            jcr:primaryType="nt:unstructured">

                        <file
                                jcr:primaryType="nt:unstructured"
                                path="/apps/holdings888/components/common888/dialog-include/image-upload-with-alt"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        imagePrefixName="bannerImage"
                                        imageIsRequired="{Boolean}false"
                                        imageLabel="Banner Image"
                                        altName="bannerImageAlt" />
                        </file>
                        <bannerTextLink
                                jcr:primaryType="nt:unstructured"
                                namespace="bannerImageNode"
                                path="/apps/holdings888/components/common888/dialog-include/link"
                                sling:resourceType="acs-commons/granite/ui/components/include">
                                <parameters
                                        jcr:primaryType="nt:unstructured"
                                        fieldsetTitle="Link (Optional, when filled, disabled Modal.)"
                                        linkUrlName="bannerImageTextLink"
                                        urlIsRequired="{Boolean}false"
                                        hideLabel="{Boolean}true"
                                        hideScript="{Boolean}true"
                                />
                        </bannerTextLink>

                            <explanation
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                    level="{Long}4"
                                    text="Dates usage: To activate the teaser - set any valid dates range for now.
                                    To de-activate the teaser - set both dates in the past.
                                    If you want to schedule the teaser activation - set the future dates range.
                                    NOTE: DONT change teaser's category without de-activating it with previous category! " />
                            <startDate
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                    displayTimezoneMessage="{Boolean}true"
                                    granite:id="startDateExportToTarget"
                                    displayedFormat="DD-MM-YYYY HH:mm Z"
                                    valueformat="DD/MM/YYYY HH:mm Z"
                                    fieldLabel="Start Date"
                                    name="./explorerBannerStartDate"
                                    type="datetime"
                                    validation="dates.notchanged">
                                    <granite:data
                                            jcr:primaryType="nt:unstructured"
                                            allowBulkEdit="{Boolean}true"
                                            cq-msm-lockable="startDate" />
                            </startDate>
                            <endDate
                                    cq:showOnCreate="{Boolean}true"
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/datepicker"
                                    displayTimezoneMessage="{Boolean}true"
                                    granite:id="endDateExportToTarget"
                                    displayedFormat="DD-MM-YYYY HH:mm Z"
                                    valueformat="DD/MM/YYYY HH:mm Z"
                                    fieldLabel="End Date"
                                    name="./explorerBannerEndDate"
                                    type="datetime"
                                    validation="dates.notchanged">
                                    <granite:data
                                            jcr:primaryType="nt:unstructured"
                                            allowBulkEdit="{Boolean}true"
                                            cq-msm-lockable="endDate" />
                            </endDate>
                            <explorerBannerScript
                                jcr:description="The JS Script that will be execute on click"
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                fieldLabel="Custom Script"
                                name="./explorerBannerScript"
                                resize="vertical"/>
                            <exportToTargetWrapper
                                    jcr:primaryType="nt:unstructured"
                                    granite:id="exportToTargetWrapper"
                                    sling:resourceType="granite/ui/components/coral/foundation/container">
                                    <items
                                            jcr:primaryType="nt:unstructured">
                                            <exportToTarget
                                                    granite:id="explorerBannerExportToTargetWrapper"
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/anchorbutton"
                                                    text="Export to Target scheduled"
                                                    variant="primary"
                                                    size="L"
                                                    icon="arrowUp"
                                                    x-cq-linkchecker="skip"/>
                                    </items>
                            </exportToTargetWrapper>
                    </items>
                </column>
            </items>
        </columns>
    </items>
</jcr:root>
