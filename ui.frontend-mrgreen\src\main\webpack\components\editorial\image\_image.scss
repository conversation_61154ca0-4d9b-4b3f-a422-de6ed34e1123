.image-component {
  margin: 0 auto;

  img {
    width: 100%;
    height: auto;
    display: block;

    transition: all;
    transition-duration: .3s;
    transition-timing-function: ease-out;
  }

  .image-round {
    img {
      display: block;
      margin: auto;
      clip-path: circle();
      object-fit: cover;
      background-color: $round-image-background;

      transition: all;
      transition-duration: .3s;
      transition-timing-function: ease-out;
    }
  }

  .image-round-border {
    clip-path: circle();
    background: $white;
    padding: 0.4rem;
  }

  .image-container {
    position: relative;

    &.modal-enabled {
        &:before {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            content: "";

            opacity: 0.2;

            transition: all;
            transition-duration: .3s;
            transition-timing-function: ease-out;

            background-color: transparent;
          }
      &:hover::before {
        background: $black;
        cursor: pointer;
      }
    }
  }

  .image-caption {
    padding: .8rem 0 .8rem;
    font-family: $font-family-12;
    background-color: transparent;
    font-style: normal;
    color: $gold;
    font-weight: 700;
    font-size: 2.24rem;

    @media screen and (min-width: 768px) and (max-width: 980px) {
        font-size: 2.08rem;
    }
  }

  .modal {
    position: fixed;
    left: 0;
    top: 0;
    height: 0;
    width: 100vw;
    overflow: hidden;

    cursor: zoom-out;
    z-index: 1000;
    background: 0 0;
    transition: background 0.3s ease-out;

    .modal-dialog {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;

      .modal-content {
        height: 0;
        opacity: 0;
        margin: auto;
        position: relative;

        transition: opacity 0.2s ease-out;
        width: fit-content;
        max-width: 80%;

        .modal-image {
          @extend .image-box-shadow;
          height: 0;
          margin: auto;

        }

        .button-close {
          @extend .image-box-shadow;
          position: absolute;
          height: 0;
          border: 0;
          border-radius: 50%;

          background-color: $light-green;
          color: $white;
          width: 4.2rem;
          line-height: 4.2rem;
          font-size: 2.8rem;
          top: -2.2rem;
          right: -2.2rem;
          cursor: zoom-out;
          z-index: 1002;
        }
      }
    }

    //modal revealed with fade-in effect
    &.reveal {
      background: rgba(0, 0, 0, 0.8);

      .modal-dialog {
        .modal-content {
          opacity: 1;
        }
      }
    }
  }

  @for $i from 1 through 10 {
    &.img-#{$i *10} {
      max-width: #{$i *10%};
    }
  }
  &.img-404 {
    display: block;
    margin: 0 auto;
    max-width: 680px;
    min-height: 289px;
    width: 100%;
  }

  //only for images used as page banner -  the mobile aspect ratio changes
  &.banner-img {
    @media (max-width: $sm-max) {
      .image-container {
        height: 22vh;
        img {
          height: 100%;
          object-fit: cover;
        }
      }
    }

  }
  &.left-aligned {
        margin: unset;
  }

}

//IMAGE UTILITIES
.image-box-shadow {
  box-shadow: 0 0 1.6rem #000;
}

//GRID COMPONENT
.grid-component {
  .image-component {
    .image-container {
      margin: 0;
    }
  }
}