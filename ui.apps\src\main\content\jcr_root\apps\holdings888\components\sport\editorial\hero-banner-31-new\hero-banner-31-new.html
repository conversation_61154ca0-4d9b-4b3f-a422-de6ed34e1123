<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='sliderItems'}" />
<sly data-sly-use.imageItems="${'holdings888/utils/multifield.js' @ multifieldName='imageItems'}" />
<sly data-sly-test.hasContent="${listItems}" />
<sly data-sly-use.richTextDesktop="${'holdings888.core.models.RichTextImp' @ text=properties.textDesktop}" />
<sly data-sly-use.richTextTablet="${'holdings888.core.models.RichTextImp' @ text=properties.textTablet}" />
<sly data-sly-use.richTextMobile="${'holdings888.core.models.RichTextImp' @ text=properties.textMobile}" />

<sly data-sly-test="${listItems}">
    <sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html" />
    <sly data-sly-call="${clientlib.all @ categories='holdings888.swiper'}" />
</sly>

<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<sly data-sly-use.processorImageDesktop="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.backgroundImageDesktop}"/>
<sly data-sly-set.backgroundImageDesktopPath="${processorImageDesktop.renditions['webp'].path || processorImageDesktop.renditions['original'].path}"/>
<sly data-sly-use.processorImageMobile="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.backgroundImageMobile}"/>
<sly data-sly-set.backgroundImageMobilePath="${processorImageMobile.renditions['webp'].path || processorImageMobile.renditions['original'].path}"/>

<div
    class="custom-banner"
    role="img"
    aria-label="${properties.imageAlt || 'Image Alternative'}"
    title="${properties.imageTitle}"
    data-mbox-id="${properties.mboxId}"
    data-background-desktop="${backgroundImageDesktopPath @ context='uri'}"
    data-background-mobile="${backgroundImageMobilePath @ context='uri'}">
    <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaimage'}">
        <sly data-sly-use.processoraboveCtaImage="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.aboveCtaImage}"/>
        <sly data-sly-set.aboveCtaImagePath="${processoraboveCtaImage.renditions['webp'].path || processoraboveCtaImage.renditions['original'].path}"/>
        <img
            src="${aboveCtaImagePath}"
            class="above-cta-image"
            alt="${properties.aboveCtaImageAlt || 'Above CTA Image'}"
            title="${properties.aboveCtaImageAlt || 'Above CTA Image'}"
            loading="lazy" />
    </sly>
    <div class="containers lpOfferContainer">
        <div class="lpText">
            <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaRichText'}">
                <div class="offer-main-text-container">
                    <div class="text-above-offer desktop-only">
                        <div class="text">${richTextDesktop.text @ context='unsafe'}</div>
                    </div>
                    <div class="text-above-offer tablet-only">
                        <div class="text">${richTextTablet.text @ context='unsafe'}</div>
                    </div>
                    <div class="text-above-offer mobile-only">
                        <div class="text">${richTextMobile.text @ context='unsafe'}</div>
                    </div>
                </div>
            </sly>

            <div class="cta-button-container desktop-c">
                <div class="cta-comp">
                    <sly data-sly-resource="${'cta' @ resourceType='holdings888/components/sport/editorial/cta'}" />
                </div>
            </div>
        </div>
        <sly data-sly-use.processorImageDesktopReference="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageDesktopReference}"/>
        <sly data-sly-set.imageDesktopReferencePath="${processorImageDesktopReference.renditions['webp'].path || processorImageDesktopReference.renditions['original'].path}"/>
        <sly data-sly-use.processorImageMobileReference="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.imageMobileReference}"/>
        <sly data-sly-set.imageMobileReferencePath="${processorImageMobileReference.renditions['webp'].path || processorImageMobileReference.renditions['original'].path}"/>

        <div class="lpImage">
            <img
                class="lpImageContainer"
                src="${imageDesktopReferencePath}"
                alt="${properties.imageDesktop}"
                title="${properties.imageDesktop}"
                loading="lazy" />
            <img
                class="lpImageContainerMobile"
                src="${imageMobileReferencePath}"
                alt="${properties.imageMobile}"
                title="${properties.imageMobile}"
                loading="lazy" />
            <div class="lpImageLine ${properties.showBg ? '' : 'd-none'}"></div>
        </div>
    </div>
    <div class="cta-button-container mobile-c">
        <div class="cta-comp">
            <sly data-sly-resource="${'cta' @ resourceType='holdings888/components/sport/editorial/cta'}" />
        </div>
    </div>
    <div class="containers lpCarouselContainer ${properties.underCtaOption == 'underCtaimage' ? 'container-under-cta-image' : ''}">
        <div
            class="regulationsRow"
            data-sly-test="${properties.underCtaOption == 'underCtaSlider'}">
            <div
                data-sly-test="${hasContent}"
                class="banner-slider ${wcmmode.edit ? 'edit' : ''}">
                <div class="banner-slider-container">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            <sly data-sly-list.articleSlider="${listItems}">
                                <div class="swiper-slide">
                                    <sly
                                        data-sly-call="${link.simpleImage @
                                            properties=articleSlider.properties,
                                            labelClasses='article-text',
                                            linkUrlName='articleLink',
                                            linkLabelName='articleText',
                                            imagePath=articleSlider.properties.imageReference,
                                            imageAlt=articleSlider.properties.alt,
                                            imageWrapperClasses='slide-img'}" />
                                </div>
                            </sly>
                        </div>
                    </div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
        <div
            class="images-container"
            data-sly-test="${properties.underCtaOption == 'underCtaimage'}">
            <sly data-sly-list.images="${imageItems}">
                <sly data-sly-use.processorUnderCtaImageReference="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=images.properties.underCtaImageReference}"/>
                <sly data-sly-set.underCtaImageReferencePath="${processorUnderCtaImageReference.renditions['webp'].path || processorUnderCtaImageReference.renditions['original'].path}"/>

                <a
                    class="image-link"
                    href="${images.properties.imageLink}"
                    title="${images.properties.linkLabel}"
                    target="${images.properties.target}">
                    <img
                        src="${underCtaImageReferencePath}"
                        class="under-cta-image"
                        alt="${images.underCtaImageAlt || 'Under CTA Image'}"
                        loading="${images.isFetchPriorityHigh}" />
                </a>
            </sly>
        </div>

        <div class="sigTermsRow">
            <div class="disclaimer-richtext">
                <sly data-sly-resource="${'disclaimer-rich-text' @ resourceType='holdings888/components/sport/editorial/rich-text'}" />
            </div>
        </div>
    </div>
</div>
