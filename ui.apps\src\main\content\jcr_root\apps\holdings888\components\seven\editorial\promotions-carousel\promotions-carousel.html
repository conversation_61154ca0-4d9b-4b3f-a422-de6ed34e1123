<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-use.promotionModel="${'holdings888.core.models.CasinoPromoPagesRender'}" />
<sly data-sly-test.hasContent="${promotionModel.isEmpty}" />
<sly data-sly-test.hasContent2="${promotionModel.promotionList}" />
<sly data-sly-test.hasContent3="${properties.category}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent3}" />
<sly data-sly-set.categoryLeft="moveCarouselLeft('${properties.category}')" />
<sly data-sly-set.categoryRight="moveCarouselRight('${properties.category}')" />
<sly data-sly-use.idUtil="${'holdings888.core.models.CasinoIdUtils' @ category=properties.category}" />
<sly data-sly-set.modifiedId="${idUtil.getId}" />
<sly data-sly-include="clientlibs-css.html" />
<sly data-sly-test="${!hasContent && hasContent2 && hasContent3}">
    <div data-mbox-id="${properties.mboxId}">
        <div
            class="promotion-carousel-component ${properties.category}"
            id="${modifiedId}">
            <div class="promotion-carousel-container ${properties.paddingTop} ${properties.paddingBottom}">
                <div class="promotions-carousel-top">
                    <div class="promotions-carousel-title">
                        <h2>${properties.category}</h2>
                    </div>
                    <div
                        class="arrows"
                        id="arrows">
                        <div
                            class="arrow-left"
                            id="arrow-left"
                            onclick="${categoryLeft @ context='unsafe'}">
                            &lt
                        </div>
                        <div
                            class="arrow-right"
                            id="arrow-right"
                            onclick="${categoryRight @ context='unsafe'}">
                            &gt
                        </div>
                    </div>
                </div>
                <div class="swiper">
                    <div
                        class="swiper-wrapper"
                        id="swiper-wrapper-${properties.category}">
                        <sly data-sly-list.promotion="${promotionModel.promotionList}">
                            <sly data-sly-test="${promotion.category == properties.category}">
                                ${promotion.ctaGlowing}
                                <sly data-sly-use.componentTemplate="promotions-teaser-template.html" />
                                <sly
                                    data-sly-call="${componentTemplate.default @ 
                                TopText = promotion.topTitle, 
                                topLinkText=promotion.topLinkText, 
                                topLink=promotion.topLinkUrl.relativePublishLink,
                                imagePath=promotion.imageRef,
                                imageAlt=promotion.imageAlt, 
                                bottomText=promotion.bottomText, 
                                bottomTextSubtitle=promotion.bottomTextSubtitle, 
                                topCtaText=promotion.topCtaLinkText, 
                                topCtaLink=promotion.topCtaLink, 
                                topCtaScript=promotion.topCtaScript,
                                bottomCtaGlowing=promotion.bottomCtaGlowing,
                                bottomCtaText=promotion.bottomCtaLinkText,
                                bottomCtaLink=promotion.bottomCtaLink, 
                                bottomCtaScript=promotion.bottomCtaScript, 
                                link=promotion.link.relativePublishLink, 
                                category=promotion.category,
                                mboxId=promotion.mboxId,
                                bottomLinkText=promotion.bottomLinkText,
                                disclaimerLink=promotion.disclaimerLinkUrl.relativePublishLink,
                                extraText=promotion.extraText,
                                additionalTextOptions=promotion.isBefore,
                                promotion=promotion}" />
                            </sly>
                        </sly>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
        <sly data-sly-include="clientlibs-js.html" />
        <script>
            (function () {
                let $originalCarouselPosition = 0;
                let $maxDist = 0;

                window.moveCarouselLeft = function (category) {
                    let carouselName = "promotion-carousel-component ".concat(category);
                    carouselName.replace(".", " ");
                    const wrapperElements = document.getElementsByClassName(carouselName);
                    if ($originalCarouselPosition < 0) {
                        const offSet = window.matchMedia('screen and (max-width: 1024px)').matches ? 100.1 : 81.1;
                        $originalCarouselPosition += offSet;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    }
                };

                window.moveCarouselRight = function (category) {
                    const wrapperElements = document.getElementsByClassName("promotion-carousel-component " + category);
                    const promotionElements = document
                        .getElementsByClassName("promotion-carousel-component " + category)[0]
                        .getElementsByClassName("promotions-teaser-component").length;
                    $maxDist = (promotionElements - 2) * -40;
                    if ($originalCarouselPosition > $maxDist) {
                        const offSet = window.matchMedia('screen and (max-width: 1024px)').matches ? 100.1 : 81.1;
                        $originalCarouselPosition -= offSet;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    } else {
                        $originalCarouselPosition = 0;
                        let value = "translateX(".concat($originalCarouselPosition).concat("%)");
                        wrapperElements[0].getElementsByClassName("swiper-wrapper")[0].style.setProperty("transform", value);
                    }
                };
            })();
        </script>
    </div>
</sly>
