<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"/>
<sly data-sly-call="${clientlib.all @ categories='holdings888.swiper'}"/>
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-test.path="${properties.promotionLobbyPath ? properties.promotionLobbyPath : currentPage.path}"/>
<sly data-sly-use.checkPromoModel="${'holdings888.core.models.CheckPromoModel' @ path = path, resourceType = resource.resourceType}"/>
<sly data-sly-test.hasAFullRow="${checkPromoModel.promotionList}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasAFullRow}"/>


<div class="promotion-carousel-component" data-sly-test="${hasAFullRow}" data-mbox-id="${properties.mboxId}">
    <div class="promotion-carousel-container js-promotion-carousel-container" value="${properties.delay}">

        <div class="swiper">
            <div class="swiper-wrapper">
                <sly data-sly-list.promotion="${hasAFullRow}">
                    <sly data-sly-use.componentTemplate="holdings888/components/editorial/htl-templates/promotions-image-and-text-overlay-template.html"/>
                    <sly data-sly-call="${componentTemplate.auto @ promotion=promotion, link=promotion.link.relativePublishLink}"/>
                </sly>
            </div>
            <div class="swiper-pagination"></div>
        </div>

    </div>
</div>