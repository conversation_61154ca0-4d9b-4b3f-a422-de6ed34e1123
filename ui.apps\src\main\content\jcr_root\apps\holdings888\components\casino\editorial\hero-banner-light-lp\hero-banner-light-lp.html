<sly data-sly-use.cta="${'holdings888/components/common888/utils/childNode.js' @ nodeName='cta'}" />
<sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=cta.properties.ctaScript}" />
<sly data-sly-use.processorImageDesktop="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.backgroundImageDesktop}"/>
<sly data-sly-set.backgroundImageDesktopPath="${processorImageDesktop.renditions['webp'].path || processorImageDesktop.renditions['original'].path}"/>
<sly data-sly-use.processorImageMobile="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.backgroundImageMobile}"/>
<sly data-sly-set.backgroundImageMobilePath="${processorImageMobile.renditions['webp'].path || processorImageMobile.renditions['original'].path}"/>
<div class="custom-banner ${properties.aboveCtaOption == 'aboveCtaOffer' ? 'cta-offer' : ''} ${properties.activateCover ? 'backgroundCover' : ''} ${properties.activateContain ? 'backgroundContain' : ''}"
     role="img"
     aria-label="${properties.imageAlt || 'Image Alternative'}"
     title="${properties.imageTitle}"
     data-background-desktop="${backgroundImageDesktopPath @ context='uri'}"
     data-background-mobile="${backgroundImageMobilePath @ context='uri'}" data-mbox-id="${properties.mboxId}">
      <sly data-sly-use.cta="${'holdings888/components/common888/utils/childNode.js' @ nodeName='cta'}" />
        <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=cta.properties.ctaScript}" />
            <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaimage'}">
                <sly data-sly-use.processoraboveCtaImage="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.aboveCtaImage}"/>
                <sly data-sly-set.aboveCtaImagePath="${processoraboveCtaImage.renditions['webp'].path || processoraboveCtaImage.renditions['original'].path}"/>
                <div class="above-cta-div ${properties.clickable ? 'clickable' : ''}" onclick="${properties.clickable == 'true' ? scriptProcessor.processedScript : '' @ context='unsafe'}">
                    <img src="${aboveCtaImagePath @ context='uri'}" class="above-cta-image" alt="${properties.aboveCtaImageAlt || 'Above CTA Image'}" title="${properties.aboveCtaImageAlt || 'Above CTA Image'}" loading="lazy"/>
                </div>
            </sly>
   <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaRichText'}">
            <div class="above-cta-rich-text ${properties.clickable ? 'clickable' : ''}" onclick="${properties.clickable == 'true' ? scriptProcessor.processedScript : '' @ context='unsafe'}">
                <sly data-sly-resource="${'above-cta-rich-text' @ resourceType='holdings888/components/casino/editorial/rich-text'}"/>
            </div>
    </sly>
    <sly data-sly-test="${properties.aboveCtaOption == 'aboveCtaOffer'}">
            <div class="offerClickable">
            <div class="offerContainer ${properties.clickable ? 'clickable' : ''}" onclick="${properties.clickable == 'true' ? scriptProcessor.processedScript : '' @ context='unsafe'}">
                <p class="offer-type">${properties.aboveCtaOfferType}</p>
                <div class="offerPrice">
                    <p class="offer-amount">${properties.aboveCtaOfferAmount}</p>
                    <p class="offer-amount-text">${properties.aboveCtaOfferAmountText}</p>
                </div>
                <div class="subtitle ${properties.deactivateCover ? 'deactivateCover' : ''}"><p>${properties.aboveCtaOfferSubtitle}</p></div>
                <p class="offer-text">${properties.aboveCtaOfferText}</p>
                </div>
            </div>
    </sly>
    <div class="cta-button-container ${properties.setMaxWidth ? 'max-width-40' : ''} ${properties.setBackgroundColor ? 'mobile-dark-background' : ''}">
        <div class="cta-comp">
            <sly data-sly-resource="${'cta' @ resourceType='holdings888/components/casino/editorial/cta'}"/>
        </div>
        <sly data-sly-test="${properties.optionSelect == 'additionalImages'}">
            <div class="additional-images ${properties.marginImage ? 'marginImage' : ''}">
                <sly data-sly-test="${properties.image1Path}">
                    <sly data-sly-use.processorImage1="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.image1Path}"/>
                    <sly data-sly-set.image1Path="${processorImage1.renditions['webp'].path || processorImage1.renditions['original'].path}"/>
                    <img src="${image1Path}" alt="${properties.image1Alt || 'Additional Image 1'}" title="${properties.image1Title || 'Additional Image 1'}" data-redirect-url="${properties.image1Link}" class="clickable-image" loading="lazy"/>
                </sly>
            </div>
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'richText'}">
            <div class="cta-rte">
                <sly data-sly-resource="${'rich-text' @ resourceType='holdings888/components/casino/editorial/rich-text'}"/>
            </div>    
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'threeElements'}">
            <div class="rte-threel">
                <div class="three-elements-container">
                    <sly data-sly-resource="${'three-elements-with-lines' @ resourceType='holdings888/components/casino/editorial/three-elements-with-lines'}"/>
                </div>
                <div class="rte-container">
                    <sly data-sly-resource="${'threeElements-rich-text' @ resourceType='holdings888/components/casino/editorial/rich-text'}"/>
                </div>
            </div> 
        </sly>
        <sly data-sly-test="${properties.optionSelect == 'imagesAndRichText'}">
            <div class="and-rich-text">    
                <div class="additional-images">
                    <sly data-sly-test="${properties.image1Path2}">
                    <sly data-sly-use.processorImage12="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.image1Path2}"/>
                    <sly data-sly-set.image1Path2="${processorImage12.renditions['webp'].path || processorImage12.renditions['original'].path}"/>
                        <img src="${image1Path2}" alt="${properties.image1Alt2 || 'Additional Image 1'}" title="${properties.image1Title2 || 'Additional Image 1'}" data-redirect-url="${properties.image1Link2}" class="clickable-image" loading="lazy"/>
                    </sly>
                </div>
                <div class="adim-rte-container">
                    <sly data-sly-resource="${'ImageAndRichText' @ resourceType='holdings888/components/casino/editorial/rich-text'}"/>
                </div>
            </div>
        </sly>
    </div>
</div>
