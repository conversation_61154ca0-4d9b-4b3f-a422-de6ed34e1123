<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2015 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>holdings888</groupId>
        <artifactId>holdings888</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>holdings888.it.tests</artifactId>
    <name>holdings888 - Integration Tests</name>
    <description>Integration Tests for holdings888</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.htmlunit</groupId>
            <artifactId>htmlunit</artifactId>
            <version>2.45.0</version>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>aem-cloud-testing-clients</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>1.7.25</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- Cloud author service (LB) -->
                <it.author.url>http://localhost:4502</it.author.url>
                <it.author.user>admin</it.author.user>
                <it.author.password>admin</it.author.password>

                <!-- cloud publish service (LB) -->
                <it.publish.url>http://localhost:4503</it.publish.url>
                <it.publish.user>admin</it.publish.user>
                <it.publish.password>admin</it.publish.password>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.22.1</version>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>2.22.1</version>
                        <configuration>
                            <testSourceDirectory>${basedir}/src/main/java/</testSourceDirectory>
                            <testClassesDirectory>${project.build.directory}/classes/</testClassesDirectory>
                            <systemPropertyVariables>
                                <!-- Same properties as used when run in a AEM CI/CD pipeline -->
                                <sling.it.configure.default.replication.agents>false</sling.it.configure.default.replication.agents>
                                <com.sun.security.enableAIAcaIssuers>true</com.sun.security.enableAIAcaIssuers>
                                <it.logintokenauth>true</it.logintokenauth>

                                <!-- Sling Testing Tools properties for configuring the test instances -->
                                <sling.it.instances>2</sling.it.instances>

                                <!-- Author service -->
                                <sling.it.instance.url.1>${it.author.url}</sling.it.instance.url.1>
                                <sling.it.instance.runmode.1>author</sling.it.instance.runmode.1>
                                <sling.it.instance.adminUser.1>${it.author.user}</sling.it.instance.adminUser.1>
                                <sling.it.instance.adminPassword.1>${it.author.password}
                                </sling.it.instance.adminPassword.1>

                                <!-- Publish service -->
                                <sling.it.instance.url.2>${it.publish.url}</sling.it.instance.url.2>
                                <sling.it.instance.runmode.2>publish</sling.it.instance.runmode.2>
                                <sling.it.instance.adminUser.2>${it.publish.user}</sling.it.instance.adminUser.2>
                                <sling.it.instance.adminPassword.2>${it.publish.password}
                                </sling.it.instance.adminPassword.2>
                            </systemPropertyVariables>
                        </configuration>
                        <executions>
                            <execution>
                                <id>default-integration-test</id>
                                <goals>
                                    <goal>integration-test</goal>
                                </goals>
                                <configuration>
                                    <includes>
                                        <include>**/*IT.java</include>
                                    </includes>
                                </configuration>
                            </execution>
                            <execution>
                                <id>default-verify</id>
                                <goals>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifestEntries>
                            <Cloud-Manager-TestType>integration-test</Cloud-Manager-TestType>
                        </manifestEntries>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
