function initClaimPage(claimPageData, claimPageContainer) {
  let selectors = {
    mainWrapper: ".mainWrapper",
    upperSentenceText: ".upperSentenceText",
    bannerImg: ".bannerImg",
    lowerSentenceText: ".lowerSentenceText",
    ctaButton: ".cta-button",
  };

  render(claimPageContainer, claimPageData);

  //Main rendering function
  function render(claimPageContainer, claimPageData) {
    let status = claimPageData.status;
    const txtBParam =
      typeof window.sCut !== "undefined" ? sCut.get("txtB") : "default";

    addClaimStatusToHiddenDiv(status, claimPageContainer);

    let mainWrapper = claimPageContainer.querySelector(selectors.mainWrapper);

    let upperSentenceText = mainWrapper.querySelector(
      selectors.upperSentenceText
    );

    let bannerImg = mainWrapper.querySelector(selectors.bannerImg);

    let lowerText = mainWrapper.querySelector(selectors.lowerSentenceText);

		let ctaBtn = mainWrapper.querySelector(selectors.ctaButton);

    renderBackgroundImage(bannerImg, claimPageData, status);

    switch (txtBParam?.toLowerCase()) {
      case "casino":
      case "888casino":
        console.log("casino");
        upperSentenceText.innerHTML = getUpperSentence(
          claimPageData.offerBoxCasino.upperSentence,
          status
        );
        renderOffer(lowerText, claimPageData.offerBoxCasino, status);
        break;
      case "sports":
      case "888sport":
        console.log("sports");
        upperSentenceText.innerHTML = getUpperSentence(
          claimPageData.offerBoxSports.upperSentence,
          status
        );
        renderOffer(lowerText, claimPageData.offerBoxSports, status);
        break;
      default:
        console.log("default");
        upperSentenceText.innerHTML = getUpperSentence(
          claimPageData.offerBoxDefault.upperSentence,
          status
        );
        renderOffer(lowerText, claimPageData.offerBoxDefault, status);
    }

    window.onresize = reRenderImages;

    // Inner Rendering functions
    function reRenderImages() {
      renderBackgroundImage(bannerImg, claimPageData, status);
    }

    function addClaimStatusToHiddenDiv(claimStatus, claimPageContainer) {
      const automationClaim = claimPageContainer.querySelector(
        "#automation_claim_response"
      );
      null !== automationClaim && (automationClaim.innerHTML = claimStatus);
    }

    function renderBackgroundImage(bannerImg, claimPageData, status) {
      const { img, alt } = getBackgroundImage(
        claimPageData.backgroundImage,
        status
      );
      bannerImg.setAttribute("src", img);
      bannerImg.setAttribute("alt", alt);
    }

    function renderOffer(offerContainer, claimData, status) {
      const offerMessage = getOfferContent(claimData);
      const cta = claimData.cta;

      switch (status) {
        case "1":
          if (cta && cta.ctaText !== "") {
            ctaBtn.setAttribute("attr-ctaText", cta.ctaText);
            cta.ctaScript && ctaBtn.setAttribute("onclick", cta.ctaScript);
            cta.CTAariaLabel && ctaBtn.setAttribute("aria-label", cta.CTAariaLabel);
            cta.ctaUrl && ctaBtn.setAttribute("href", cta.ctaUrl);
            ctaBtn.textContent = cta.ctaText;
						ctaBtn.style.display = "block";
          }

          offerContainer.innerHTML = offerMessage.success
            ? offerMessage.success
            : "";
          break;
        case "4":
          offerContainer.innerHTML = offerMessage.used ? offerMessage.used : "";
          break;
        case "2":
          offerContainer.innerHTML = offerMessage.expired
            ? offerMessage.expired
            : "";
          break;
        default:
          offerContainer.innerHTML = offerMessage.error
            ? offerMessage.error
            : "";
      }
    }

    function getBackgroundImage(backgroundImage, status) {
      let successBg = backgroundImage.success;
      let errorBg = backgroundImage.error;
      return status === "1"
        ? {
            img: switchPcMobileImage(successBg.pc, successBg.mobile),
            alt: "Claim Success Image",
          }
        : {
            img: switchPcMobileImage(errorBg.pc, errorBg.mobile),
            alt: "Claim Unsuccessful Image",
          };
    }

    function getUpperSentence(upperSentence, status) {
      let success = upperSentence.success;
      let used = upperSentence.used;
      let expired = upperSentence.expired;
      let error = upperSentence.error;
      let message;
      switch (status) {
        case "1":
          message = success.trim();
          break;
        case "2":
          message = expired.trim();
          break;
        case "4":
          message = used.trim();
          break;
        default:
          message = error.trim();
      }
      return message;
    }

    function switchPcMobileImage(pc, mobile) {
      let isMobile = 768 >= window.screen.width;
      return isMobile ? mobile : pc;
    }

    function getOfferContent(offer) {
      return offer.message || "";
    }
  }
}

function getClaimHtml() {
  return new Promise((resolve, reject) => {
    const urlParams = new URLSearchParams(window.location.search);
    const guid = urlParams.get("guid") || "";

    const validationPath = "/claim/new.htm";
    let validationUrl = claimPageData.validationDomain || "";
    validationUrl = validationUrl.concat(validationPath, "?guid=", guid);

    fetch(validationUrl)
      .then((response) => {
        window.response = response;
        return response.text();
      })
      .then((claimHtml) => {
        let claimStatus = getClaimStatusFromHtmlString(claimHtml);

        if (!claimStatus) {
          resolve("3");
        }

        resolve(claimStatus);

        reject("Error fetching or processing data - claimHtml:");
      })
      .catch((error) => {
        console.error("Error fetching or processing data - call 1:", error);
        resolve("3");
      });
  });
}

function getClaimStatusFromHtmlString(htmlString) {
  let parser = new DOMParser();
  let DOM = parser.parseFromString(htmlString, "text/html");
  let content = DOM.body.querySelector("#GenericBonusClaimControlMainDiv");

  const contentElements = getVisibleAndHiddenElements(content);

  if (!contentElements) {
    return 3;
  }

  const responseCode = contentElements.textContent.trim().match(/\d+/)?.[0];
  console.log(responseCode);

  return responseCode;
}

function getVisibleAndHiddenElements(element) {
  const successArea = element.querySelector(
    "#content_ctl00_GenericBonusClaimControlSuccessAreaDiv"
  );
  const errorArea = element.querySelector(
    "#content_ctl00_GenericBonusClaimControlErrorAreaDiv"
  );

  const isVisible = (el) => el.style.display === "block";

  const allChildrenEl = [
    ...Array.from(successArea.children),
    ...Array.from(errorArea.children),
  ];

  const visibleElement = allChildrenEl.find(isVisible);

  return visibleElement ? visibleElement : null;
}

function wcmmodeRender(claimPageData, claimBannerComponent) {
  try {
    let info = document.createElement("h3");
    info.style.color = "grey";
    info.innerText =
      "This is a WCM Mode with all banner variations. To see it \"like in live page\" add '?livemode' to URL";
    if (claimBannerComponent.parentNode) {
      claimBannerComponent.parentNode.appendChild(info);

      // loop for 4 claimStatuses
      for (let i = 1; i <= 4; i++) {
        let newClaimBannerComponent = claimBannerComponent.cloneNode(true);
        newClaimBannerComponent.id = claimBannerComponent.id + "-" + i;

        let header = document.createElement("h2");
        header.innerText = "Claim Page - status = " + i;
        claimBannerComponent.parentNode.appendChild(
          document.createElement("hr")
        );
        claimBannerComponent.parentNode.appendChild(header);
        claimBannerComponent.parentNode.appendChild(
          document.createElement("hr")
        );
        claimBannerComponent.parentNode.appendChild(newClaimBannerComponent);
      }
    }

    claimBannerComponent.remove();

    setTimeout(() => {
      for (let i = 1; i <= 4; i++) {
        claimPageData.status = i + "";
        let newClaimBannerComponent = document.getElementById(
          "fullContent-" + i
        );
        initClaimPage(claimPageData, newClaimBannerComponent);
      }
    }, 200);
  } catch (error) {
    console.error("Error on wcmmodeRender: ", error);
  }
}

// Entry point
let claimBannerComponent = document.querySelector(".claim-banner-component");

if (claimBannerComponent && claimPageData) {
  setTimeout(() => {
    let isWcmMode = document.querySelector("input#isWCMMode");
    if (
      ((isWcmMode && isWcmMode.value === "true") ||
        document.location.href.includes("wcmmode=disabled")) &&
      !document.location.href.includes("livemode")
    ) {
      wcmmodeRender(claimPageData, claimBannerComponent);
      return;
    }

    getClaimHtml()
      .then((claimStatus) => {
        claimPageData.status = claimStatus;
        // re-assign container to the new one after delay
        let newClaimBannerComponent = document.getElementById("fullContent");
        initClaimPage(claimPageData, newClaimBannerComponent);
      })
      .catch((error) => {
        console.error("Error fetching or processing data:", error);
        claimPageData.status = "3";
        // re-assign container to the new one after delay
        let newClaimBannerComponent = document.getElementById("fullContent");
        initClaimPage(claimPageData, newClaimBannerComponent);
      });
  }, 100);
}
