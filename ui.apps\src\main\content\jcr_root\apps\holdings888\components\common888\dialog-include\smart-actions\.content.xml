<jcr:root
        xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
    <items jcr:primaryType="nt:unstructured">
        <generateScript
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                text="Generate SmartAction Script"
                value="{Boolean}true"
                name="./${{generateScriptName:generateScript}}"
                granite:class="smart-actions-generateScript"
                uncheckedValue="{Boolean}false"
                checked="{Boolean}false" />
        <accordion
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/accordion">
            <items
                    jcr:primaryType="nt:unstructured">
                <column
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Smart Action Attributes"
                        sling:resourceType="granite/ui/components/coral/foundation/container">
                    <items
                            jcr:primaryType="nt:unstructured">
                        <ctaT
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="CTA code"
                                fieldDescription="Select the CTA code from the 'ctacodes' Json Content Fragment"
                                name="./${{generateScriptName:generateScript}}Data/ctaT">
                            <datasource
                                    jcr:primaryType="nt:unstructured"
                                    source="ctacodes"
                                    sourceType="list"
                                    damRoot="/content/dam/${{damRoot:holdings888}}/"
                                    sling:resourceType="/apps/getListFromJson"/>
                        </ctaT>
                        <dl
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                allowNegative="{Boolean}false"
                                min="0"
                                fieldLabel="Download number"
                                name="./${{generateScriptName:generateScript}}Data/DL" />
                        <gameName
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="Game Name"
                                fieldDescription="Select the game name from the 'gamelist' Json Content Fragment"
                                name="./${{generateScriptName:generateScript}}Data/gameName">
                            <datasource
                                    jcr:primaryType="nt:unstructured"
                                    source="gamelist"
                                    sourceType="list"
                                    damRootPath="/content/dam/${{damRoot:holdings888}}/"
                                    sling:resourceType="/apps/getListFromJson"/>
                        </gameName>
                        <promoCode
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Promo Code"
                                name="./${{generateScriptName:generateScript}}Data/promoCode" />
                        <cashierId
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="Cashier Id"
                                emptyOption="{Boolean}true"
                                name="./${{generateScriptName:generateScript}}Data/cashierId">
                            <items
                                    jcr:primaryType="nt:unstructured">
                                <fullCashier
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="Full Cashier"
                                        text="Full Cashier" />
                                <deposit
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="Deposit"
                                        text="Deposit" />
                                <withdraw
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="Withdraw"
                                        text="Withdraw" />
                                <onlyWithdraw
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="only Withdraw"
                                        text="only Withdraw" />
                            </items>
                        </cashierId>
                        <myAccountID
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="myAccountID"
                                name="./${{generateScriptName:generateScript}}Data/myAccountID" />
                        <redirectionUrl
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                fieldLabel="Redirection url"
                                rootPath="/content"
                                name="./${{generateScriptName:generateScript}}Data/redirectionUrl" />
                        <flag
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="Page type"
                                emptyOption="{Boolean}true"
                                name="./${{generateScriptName:generateScript}}Data/flag">
                            <items
                                    jcr:primaryType="nt:unstructured">
                                <innerPage
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="innerPage"
                                        text="Inner (orbit) Page" />
                                <LP
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="lp"
                                        text="Landing Page" />
                            </items>
                        </flag>
                        <richId
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                allowNegative="{Boolean}false"
                                min="0"
                                fieldLabel="Rich Id"
                                name="./${{generateScriptName:generateScript}}Data/richId" />
                        <gameType
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                fieldLabel="Game Type"
                                emptyOption="{Boolean}true"
                                name="./${{generateScriptName:generateScript}}Data/gameType">
                            <items
                                    jcr:primaryType="nt:unstructured">
                                <rel
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="rel"
                                        text="Rel" />
                                <demo
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/form/select/item"
                                        value="demo"
                                        text="Demo" />
                            </items>
                        </gameType>
                        <filterArena
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                fieldLabel="Game filter"
                                name="./${{generateScriptName:generateScript}}Data/filterArena" />
                        <f2pId
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                allowNegative="{Boolean}false"
                                min="0"
                                max="2"
                                fieldLabel="f2pId (0-2)"
                                name="./${{generateScriptName:generateScript}}Data/f2pId"/>
                    </items>
                </column>
            </items>
        </accordion>
    </items>
</jcr:root>