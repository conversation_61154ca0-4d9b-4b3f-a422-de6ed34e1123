.promotions-card-component {
  font-kerning: none;
  @media only screen and (max-width: 768px) {
    img.mainPromoImg {
      min-height: auto !important;
    }
  }

  .promoCr {
    margin-bottom: 20px;
    width: 100%;
    padding: 15px 15px;
    background-color: $dark-grey-3;
    border: 2px solid #3c3c3c;
    border-radius: 7px;
    box-shadow: 0 11px 7px 0 rgba(0, 0, 0, 0.2);
    position: relative;
    transition: 0.2s;
    justify-content: space-between;
    flex-direction: column;
    display: flex;
    overflow: hidden;
    @media only screen and (min-width: 1028px) {
      min-height: 528px;
    }
    @media (max-width: 1028px) {
      height: auto !important;
      min-height: auto!important;
    }
    * {
      font-kerning: none;
    }

    &.promoOpened {
      border: 2px solid var(--category-color, $orange);
      height: auto !important;
      transition: 0.2s;

      .circles {
        opacity: 1;
      }

      .promoCTA .CTAs a.promoRM {
        padding: 1.8rem 0;
        @media only screen and (max-width: 475px) {
          padding: 11px 0;
        }
        span.rmIcon svg {
          -webkit-transform: rotate(0);
          -moz-transform: rotate(0);
          -ms-transform: rotate(0);
          -o-transform: rotate(0);
          transform: rotate(0);
          transition: 0.3s;
        }
      }
    }

    .circles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      opacity: 0;
      list-style: disc;

      li {
        position: absolute;
        display: block;
        list-style: none;
        width: 20px;
        height: 20px;
        background: radial-gradient(ellipse at center, rgb(0 0 0 / 0%) 30%, var(--category-circles-color) 30.5%);
        animation: animate 20s linear infinite;
        bottom: -150px;
        border-radius: 50%;

        &:before {
          content: none;
        }

        &:nth-child(1) {
          left: 25%;
          width: 40px;
          height: 40px;
          animation-delay: 0s;
          animation-duration: 14s;
        }

        &:nth-child(2) {
          left: 10%;
          width: 20px;
          height: 20px;
          animation-delay: 3s;
        }

        &:nth-child(3) {
          left: 70%;
          width: 30px;
          height: 30px;
          animation-delay: 5s;
          animation-duration: 18s;
        }

        &:nth-child(4) {
          left: 55%;
          width: 20px;
          height: 20px;
          animation-delay: 2s;
          animation-duration: 17s;
        }

        &:nth-child(5) {
          left: 65%;
          width: 20px;
          height: 20px;
          animation-delay: 2.5s;
        }

        &:nth-child(6) {
          left: 75%;
          width: 25px;
          height: 25px;
          animation-delay: 4s;
          animation-duration: 16s;
        }

        &:nth-child(7) {
          left: 45%;
          width: 10px;
          height: 10px;
          animation-delay: 3.5s;
          animation-duration: 17s;
        }
      }

      @keyframes animate {
        0% {
          transform: translateY(-50px);
          opacity: 1;
        }
        100% {
          transform: translateY(-800px);
          opacity: 0;
        }
      }
    }

    .promo-special-tag {
      position: absolute;
      padding: 0 35px 0 25px;
      z-index: 1;
      height: 40px;
      top: 5px;
      left: 5px;
      background-color: $dark-grey-3;
      border-radius: 32px;
      @media only screen and (max-width: 768px) {
        border-radius: 0 0 40px 0;
        padding: 0 20px 0 15px;
      }

      span {
        font-family: $font-family-15;
        margin-top: 0;
        display: inline-block;
        color: $white;
        font-size: 17px;
        font-weight: 400;
        height: 40px;
        padding-top: 8px;
        @media only screen and (max-width: 768px) {
          font-size: 13px;
        }
      }
    }

    .promo-main-box {
      font-size: 20px;
      font-weight: 400;
      color: #d0cfcf;

      position: relative;
      overflow: hidden;

      img {
        display: inline-block;
        width: 100%;
        cursor: pointer;
        min-height: 150px;
        height: auto;
        @media only screen and (max-width: 768px) {
          min-height: auto !important;
        }
      }

      p {
        font-size: 20px;
        font-weight: 400;
        color: #d0cfcf;
        margin: 5px 0 10px;
        @media only screen and (max-width: 768px) {
          font-size: 14px;
        }
      }

      h3 {
        text-transform: uppercase;
        font-size: 20px;
        font-weight: 700;
        margin-top: 10px;
        margin-bottom: 0;
        font-family: $font-family-14;
        color: var(--category-color, $orange);
        @media only screen and (max-width: 768px) {
          font-size: 16px;
        }
      }

      .promo-description {
        .newItem {
          position: relative;
          bottom: 0;
          right: 0;
          z-index: 7;
          color: $white;
          padding: 0;
          border-radius: 50px;
          width: 100%;
          display: inline-block;
          text-transform: uppercase;
          font-size: 15px;
          font-family: $font-family-14;
          font-weight: 600;
          margin-bottom: 10px;
          margin-top: 10px;

          &.readed {
            text-transform: none;
            color: $gray-2;

            &:before {
              display: none;
            }
          }

          &:before {
            content: "";
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;

            background-color: var(--category-flash-color-1, #e45b01);
            animation: flash 1s;
            -moz-animation: flash 1s infinite;
            -webkit-animation: flash 1s infinite;
          }

          @-moz-keyframes flash {
            0% {
              background: var(--category-flash-color-1, #e45b01);
            }
            30% {
              background: var(--category-flash-color-2, #181818);
            }
            100% {
              background: var(--category-flash-color-1, #e45b01);
            }
          }

          @-webkit-keyframes flash {
            0% {
              background: var(--category-flash-color-1, #e45b01);
            }
            30% {
              background: var(--category-flash-color-2, #181818);
            }
            100% {
              background: var(--category-flash-color-1, #e45b01);
            }
          }
        }

        .promoVisible {
          display: inline-block;
          min-height: 90px;

          p {
            font-family: $font-family-14;
            font-weight: 600;
          }
        }

        .promoFullText {
          display: none;
          overflow: hidden;
          transition: height 0.5s ease;
          font-family: $font-family-15;
          p, ul li, ol li{
            font-size: 20px;
            line-height: normal !important;
            padding-bottom: 0 !important;
            @media only screen and (max-width: 768px) {
              font-size: 14px;
            }
          }

          strong {
            font-weight: 700;
          }

          ul {
            padding-left: 25px;
            font-size: inherit;
            line-height: normal;
            list-style: disc;
            margin-left: 0;
            margin-bottom: 1.4285rem;

            li {
              margin-bottom: unset;
              font-family: $font-family-15;
              font-weight: 400;

              &:before {
                content: none;
              }
            }
          }
        }
      }
    }

    .promoCTA {
      display: inline-block;
      text-align: center;

      .promoCTAsig {
        span {
          display: inline-block;
          font-family: $font-family-10;
          font-size: 15px;
          color: $gray-2;
          background-color: #121212;
          padding: 10px;
          width: 100%;
          float: none;
          margin-top: 0;
          text-align: left;
          border-radius: 5px;
          position: relative;
          z-index: 2;

          p {
            font-family: inherit;
            font-size: inherit;
            display: inline;
            @media only screen and (max-width: 768px) {
              font-size: 11px !important;
              line-height: 12px !important;
              display: inline-block;
            }
          }

          a {
            color: var(--category-color, $orange);
            cursor: pointer;
            text-decoration: none;
          }
        }
      }

      .CTAs {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        @media screen and (max-width: 400px) {
          display: inline-block;
          text-align: center;
          width: 100%;
        }

        .cta-template {
          margin-top: 15px;
          width: 48%;
          min-width: fit-content;
        }

        .cta-secondary-variant-3 {
          a:focus {
            background-color: $dark-grey-3;
          }
          @media screen and (max-width: 400px) {
            float: left;
            justify-items: flex-start;
            a {
              min-width: -webkit-fill-available;
              &.promoRM span {
                padding-left: 15px;
                padding-right: 35px !important;
              }
            }
            .rmIcon {
              right: 0 !important;
            }
          }
          @media screen and (max-width: 370px) {
            .promoRM span{
              letter-spacing: -0.15px;
            }
          }
        }
        .cta-secondary-variant-1 {
          @media screen and (max-width: 400px) {
            float: right;
            justify-items: flex-end;
            a {
              min-width: -webkit-fill-available;
              .promoPN {
                padding: 0px 15px;
              }
            }
          }
          @media screen and (max-width: 370px) {
            .promoPN {
              letter-spacing: -0.15px;
            }
          }
        }

        a {
          font-size: 20px;
          padding: 1.8rem 0;
          @media only screen and (max-width: 435px) {
            font-size: 14px;
            padding: 11px 0;
            overflow: hidden;
          }

          &.promoRM {
            border: 2px solid var(--category-color, $orange);
            background-color: transparent;
            align-items: center;
            position: relative;

            span {
              background-repeat: no-repeat;
              padding-right: 20px;
              z-index: 1;
              text-transform: initial;
              font-family: $font-family-9;
              font-size: 20px;
              color: var(--category-color, $orange);
              @media only screen and (max-width: 435px) {
                font-size: 14px;
                overflow: hidden;
              }
              &.rmIcon {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 20px;
                @media only screen and (max-width: 475px) {
                  right: 5px;
                }

                svg {
                  fill: var(--category-color, $orange);

                  width: 20px;
                  height: 20px;
                  -webkit-transform: rotate(180deg);
                  -moz-transform: rotate(180deg);
                  -ms-transform: rotate(180deg);
                  -o-transform: rotate(180deg);
                  transform: rotate(180deg);
                  transition: 0.3s;
                }
              }
            }

            &:active,
            &:hover {
              background: var(--category-color, $orange);

              span {
                color: $dark-grey-3;

                &.rmIcon {
                  svg {
                    fill: $dark-grey-3;
                  }
                }
              }
            }

            &a:focus {
              background: unset;
            }
          }

          .promoPN {
            font-family: $font-family-9;
            z-index: 1;
          }
        }
        @media (hover: none) {
          a.promoRM:hover {
            background: transparent;

            span {
              color: var(--category-color, $orange);

              &.rmIcon {
                svg {
                  fill: var(--category-color, $orange);
                }
              }
            }
          }
          a.promoRM:active{
            background: var(--category-color, $orange);

            span {
              color: $dark-grey-3;

              &.rmIcon {
                svg {
                  fill: $dark-grey-3;
                }
              }
            }
          }
        }
      }
    }

    @media only screen and (max-width: 320px) {
      .promoRM span.rmIcon {
        top: 9px;
        right: 9px;
      }

      &.promoOpened .promoRM span.rmIcon {
        top: 8px;
        right: 9px;
      }
    }
  }

  .promoCrFullTerms {
    position: fixed;
    background-color: $dark-grey-3;
    bottom: 0;
    height: 80%;
    max-height: 80%;
    overflow-y: scroll;
    margin-left: 230px;
    width: calc(100% - 230px);
    display: none; //js
    border-top: 1px solid var(--category-color, $orange);
    z-index: 20;
    right: 0;

    transition: height 1s ease;

    .promoCrFullTermsIn {
      padding: 0 20px 0 0;
      color: $white;
      position: relative;
      margin: 0 90px;

      p {
        margin-bottom: 10px;
      }

      h2 {
        margin: 0 0 10px;
        padding: 0;
        font-size: 19px;
        color: $white;
      }

      ul {
        padding-left: 20px;
      }

      .closeTNC {
        position: fixed;
        right: 35px;
        font-size: 20px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-weight: 700;
        color: var(--category-color, $orange);
        cursor: pointer;
        z-index: 10;
        margin-top: 10px;

        svg {
          width: 15px;
          height: 15px;
          fill: $white;
        }
      }

      .promoSigContent {
        display: block;
        margin: 0 auto;
        color: #d0cfcf;
        padding-top: 45px;

        h3 {
          margin: 30px 0 10px;
          color: $white;
          font-size: 16px;
        }

        ul li {
          font-size: 14px;

          &:before {
            color: white;
          }

          a {
            color: #f86100;
          }
        }

        ol {
          padding: 0 10px;

          li {
            ol {
              padding: 5px 10px 0;
            }

            a {
              color: var(--category-color, $orange);
            }
          }
        }

        p span {
          cursor: pointer;
        }

        table {
          tr {
            background: #1e1e1e;

            td,
            .promoSigContent table tr th {
              padding: 0.5625em 0.625em;
              text-align: left;
              display: table-cell;
              border: 1px solid #454545;
              word-break: initial;
            }
          }
        }

        @media only screen and (max-width: 1028px) {
          padding-top: 50px;
        }

        @media only screen and (max-width: 1279px) {
          padding-top: 50px;
        }
      }

      &:before {
        content: "";
        display: inline-block;
        position: fixed;
        width: 100%;
        height: 20px;
        z-index: 2;
        left: 0;
      }

      @media only screen and (max-width: 1279px) {
        padding: 0 20px;
        color: $white;
        position: relative;
        margin: 0;

        &:before {
          display: none;
        }

        .closeTNC {
          right: 20px;
          margin-top: 8px;

          svg {
            width: 15px;
            height: 15px;
            fill: $dark-grey-3;
          }
        }
      }
    }

    .promoSigMobileClose {
      display: none;

      background-color: var(--category-color, $orange);
      text-align: center;
      position: fixed;
      z-index: 9;

      width: calc(100% - 20px);
      height: 35px;
      border-radius: 10px 10px 0 0;

      .promoSigImg {
        svg {
          display: inline-block;
          background: var(--category-color, $orange);
          width: 20px;
          height: 20px;
          opacity: 0.4;
        }
      }

      @media only screen and (max-width: 1279px) {
        display: inline-block;
        text-align: center;
        position: fixed;
        z-index: 9;

        .promoSigImg {
          width: 100px;
          display: block;
          margin: 0 auto;
          padding-top: 10px;

          svg {
            display: inline-block;
            width: 20px;
            height: 20px;
            opacity: 0.4;
          }
        }
      }
    }

    @media only screen and (max-width: 1279px) {
      border: none;
      width: calc(100% - 20px);
      margin: 0 auto;
      left: 0;
      text-align: justify;
      border-radius: 10px;
      right: 0;
    }

    @media only screen and (min-height: 1280px) and (max-width: 1480px) {
      width: calc(100% - 191px);
      margin-left: 191px;
    }
  }
}

.pageCover {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  background-color: #000000c2;
  z-index: 15;
}
