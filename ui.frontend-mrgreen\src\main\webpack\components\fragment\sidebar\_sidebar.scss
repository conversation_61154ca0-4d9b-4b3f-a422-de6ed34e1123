@import "simplebar/dist/simplebar.min.css";

.sidebar-component {
    font-size: 1rem;
    z-index: 1;
    width: 100%;
    position: relative;
    display: inline-grid;

     .simplebar-mask, .simplebar-offset{
        position: initial;
    }
    .simplebar-content-wrapper{
        height: 100% !important;
        overflow: scroll hidden !important;
    }

    @media (min-width: $md) {
        height: auto;
        z-index: 1;
        position: static;
        width: 100%;
        border-left: 3px solid #41B169;
        padding: 10px 0 10px 36px;
        max-width: 24.125rem;
    }

    .sidebar-title {
        display: none;
        @media (min-width: $md) {
            display: block;
            font-size: 31.872px;
            line-height: 38.2464px;
            font-family: $font-family-10;
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-decoration: none;
            color: $white;
            padding: 0 .3125rem .625rem .3125rem;

            &:hover {
                color: $white;
            }
        }
    }
    .sidebar-menu{
        overflow: auto;
        white-space: nowrap;
        @media (max-width: $md) {
          height: 66px;
        }

        &.custom-simplebar {
            .simplebar-track {
                margin: -6px 1rem 0 0;
                background-color: black;

                .simplebar-scrollbar {
                    background-color: #87888a;
                }

                height: 100%;
                border-radius: 0.5rem;

                .simplebar-scrollbar {
                    border-radius: 0.5rem;
                }

                &.simplebar-vertical {
                    display: none;
                }

                &.simplebar-horizontal {
                    height: 0.375rem;

                    .simplebar-scrollbar {
                        top: 0;
                        height: 100%;
                    }
                }
            }
        }

        .sidebar-menu-item {
            border-left: none;
            white-space: nowrap;
            width: auto;
            display: inline-block;
            margin-bottom: 2.5rem;
            min-width: 15rem;
            text-align: center;

            @media (min-width: $md) {
                float: left;
                border: 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.5);
                display: block;
                width: 100%;
                margin: 0;
                white-space: normal;
                padding: 0.5rem;
                text-align: left;
            }

            a {
                display: block;
                text-decoration: none;
                color: $white;
                font-family: $font-family-4;
                font-size: 1.2rem;
                font-weight: 700;
                line-height: 1.8rem;
                border-bottom: .3125rem solid transparent;
                padding: .875rem 2.25rem .6875rem;
                @media (min-width: $md) {
                    border-bottom: none;
                    border-left: .625rem solid transparent;
                    padding: .625rem .3125rem .625rem .625rem;
                    font-size: 1.9rem;
                }

                &:hover {
                    color: $green-3;
                }
                &:active, &.active {
                    border-bottom: 3px solid #41B169;
                    margin: 0 1rem;

                    @media (min-width: $md) {
                        color: $green-3;
                        border-bottom: none;
                        margin: 0;
                    }
                    
                }
            }
            &:first-child {
                @media (min-width: $md) {
                    border-left:0;
                }
            }
            &:last-child {
                @media (min-width: $md) {
                    border-bottom: none;
                }
            }
            &::before {
                content: none;
            }
        }
    }

    &.edit-mode {
        padding: 0 2rem;

        .sidebar-title {
            display: block;
            margin: 0;
            text-decoration: none;
            padding: 0 .3125rem .625rem .3125rem;
            font-size: 31.872px;
            line-height: 38.2464px;
            font-family: $font-family-10;
            font-weight: 600;
            color: $white;
        }

        .sidebar-menu {
            .sidebar-menu-item {
                display: block;
                float: left;
                width: 100%;
                border: 0;
                margin: 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.5);
            }
        }

    }
    @media (max-width: $md-max) {
        &.pt-small,
        &.pt-medium,
        &.pt-large,
        &.pt-xlarge,
        &.pb-small,
        &.pb-medium,
        &.pb-large,
        &.pb-xlarge {
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}

.aem-AuthorLayer-Edit{
    .sidebar-component {
        .simplebar-mask, .simplebar-offset{
            position: absolute;
        }
    }
}


.two-columns-66-33{
    .sidebar{
        @media (max-width: $md-max) {
            margin-top: 0;
            padding-top: 0!important;
        }
    }
}