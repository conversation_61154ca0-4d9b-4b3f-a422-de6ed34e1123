CategoryAnchorInit();
function CategoryAnchorState(categories, el) {
    el.addEventListener("click", function () {
        categories.forEach(function (el) {
            el.classList.remove("active");
        });
        this.classList.add("active");
    });
}

function handleScroll() {
    const categoryAnchorBanner = document.querySelector(".promotions-lobby .static-herobanner");
    const categoryAnchor = document.querySelector(".promotions-category-icons-anchor-cmp");
    const elementToCheck = categoryAnchorBanner || categoryAnchor;

    updateAnchorSpacing();
    if (categoryAnchor) {
        if (window.innerWidth <= 768) {
            if (verifyIfElementInViewport(elementToCheck, 50)) {
                categoryAnchor.classList.remove("fixedCategories");
            } else {
                categoryAnchor.classList.add("fixedCategories");
            }
        } else {
            categoryAnchor.classList.remove("fixedCategories");
        }
    }
}

function CategoryAnchorInit() {
    const categoryAnchorCmp = document.querySelector(".promotions-category-icons-anchor-cmp");
    if (categoryAnchorCmp) {

        updateAnchorSpacing();

        let categories = document.querySelectorAll(".category-element");
        categories.forEach(function (el) {
            CategoryAnchorState(categories, el);
        });
        let scrollTimeout;
        window.addEventListener("scroll", function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(handleScroll, 150);
        });
        let resizeTimeout;
        window.addEventListener("resize", function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleScroll, 150);
        });
    }
}
function updateAnchorSpacing(attempt = 0, maxAttempts = 15) {
    let anchorSpacing = 0;
    const categoryAnchorCmp = document.querySelector(".promotions-category-icons-anchor-cmp");
    const cyHeader = document.querySelector('.cy-header-regulation-data');
    const cyNavbar = document.querySelector('.cy-navbar-container');
    const mainHeader = document.querySelector('.uc-main-header');

    if (mainHeader) {
        anchorSpacing += mainHeader.clientHeight;
    }
    if (cyHeader) {
        anchorSpacing += cyHeader.clientHeight;
    }
    if (cyNavbar) {
        anchorSpacing += cyNavbar.clientHeight;
    }

    if (anchorSpacing > 0 && categoryAnchorCmp) {
        categoryAnchorCmp.style.top = anchorSpacing + "px";
    } else if (attempt < maxAttempts) {
        setTimeout(() => updateAnchorSpacing(attempt + 1, maxAttempts), 200);
    } else {
        console.warn("Unable to calculate anchorSpacing after multiple attempts.");
    }
}

export function verifyIfElementInViewport(element, offset = 0) {
    let elementTop = element.offsetTop;
    let elementLeft = element.offsetLeft;
    let elementWidth = element.offsetWidth;
    let elementHeight = element.offsetHeight;

    let scrollTop = document.documentElement.scrollTop;
    let scrollLeft = document.documentElement.scrollLeft;

    for (let offsetParent = element.offsetParent; offsetParent; ) {
        elementTop += offsetParent.offsetTop;
        elementLeft += offsetParent.offsetLeft;
        offsetParent = offsetParent.offsetParent;
    }

    let isElementInViewport =
        elementTop < scrollTop + window.innerHeight - offset &&
        elementLeft < scrollLeft + window.innerWidth - offset &&
        elementTop + elementHeight > scrollTop + offset &&
        elementLeft + elementWidth > scrollLeft + offset;

    return isElementInViewport;
}
