<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
	xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0"
	xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
	jcr:primaryType="nt:unstructured"
	jcr:title="Offer Sports content"
	sling:resourceType="granite/ui/components/coral/foundation/container">
	<items jcr:primaryType="nt:unstructured">
		<upperSpoSentenceText
			jcr:primaryType="nt:unstructured"
			sling:resourceType="granite/ui/components/coral/foundation/form/fieldset"
			jcr:title="Upper Sentence for Sports Text">
			<items jcr:primaryType="nt:unstructured">
				<successText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="upSpoSuccessText"
						textLabel="Success Text" />
				</successText>
				<errorText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="upSpoErrorText"
						textLabel="Error Text" />
				</errorText>
				<usedText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="upSpoUsedText"
						textLabel="Used Text" />
				</usedText>
				<expiredText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="upSpoExpiredText"
						textLabel="Expired Text" />
				</expiredText>
			</items>
		</upperSpoSentenceText>
		<offerSpoText
			jcr:primaryType="nt:unstructured"
			sling:resourceType="granite/ui/components/coral/foundation/form/fieldset"
			jcr:title="Offer Sports Text">
			<items jcr:primaryType="nt:unstructured">
				<successText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="successSpoText"
						textLabel="Success Text" />
				</successText>
				<usedText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="usedSpoText"
						textLabel="Used Text" />
				</usedText>
				<errorText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="errorSpoText"
						textLabel="Error Text" />
				</errorText>
				<expiredText
					jcr:primaryType="nt:unstructured"
					path="/apps/holdings888/components/casino/dialog-include/rich-text"
					sling:resourceType="acs-commons/granite/ui/components/include">
					<parameters
						jcr:primaryType="nt:unstructured"
						textName="expiredSpoText"
						textLabel="Expired Text" />
				</expiredText>
			</items>
		</offerSpoText>
		<ctaSpo
			jcr:primaryType="nt:unstructured"
			jcr:title="CTA for 888Sports"
			sling:resourceType="granite/ui/components/coral/foundation/container"
			margin="{Boolean}true">
			<items jcr:primaryType="nt:unstructured">
				<headingType
					jcr:primaryType="nt:unstructured"
					sling:resourceType="granite/ui/components/coral/foundation/heading"
					level="{Long}3"
					text="CTA" />
				<label
					jcr:primaryType="nt:unstructured"
					sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
					fieldLabel="CTA Label"
					jcr:description="Insert the label of the CTA"
					name="./CTASpolabel" />
				<ariaLabel
					jcr:primaryType="nt:unstructured"
					sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
					fieldLabel="ARIA Label (Accessibility)"
					fieldDescription="This attribute is used to provide additional information to help clarify or further describe the purpose of a link. Can also be useful to people with disabilities."
					jcr:description="Insert the Aria Label of the CTA (Accessibility)"
					name="./CTASpoariaLabel" />
				<url
					jcr:primaryType="nt:unstructured"
					sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
					fieldLabel="CTA url"
					jcr:description="Insert the url of the CTA"
					rootPath="/content"
					name="./CTASpourl" />
				<ctaSpoScript
					jcr:description="The JS Script that will be execute on click"
					jcr:primaryType="nt:unstructured"
					sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
					fieldLabel="Custom Script"
					name="./ctaSpoScript"
					resize="vertical" />
			</items>
		</ctaSpo>
	</items>
</jcr:root>
