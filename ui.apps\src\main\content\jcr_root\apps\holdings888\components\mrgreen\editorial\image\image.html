<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${properties.fileReference}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.image="holdings888/components/common888/htl-templates/picture-template.html" />

<sly data-sly-test="${!properties.imageSizingOptions || properties.imageSizingOptions == 'PecentageImageSizing'}">
    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        class="image-component img-${properties.imageSize} ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''} ${properties.isLeftAligned ? 'left-aligned' : ''}">
        <div
            class="image-container ${properties.enableModal && !properties.textLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.textLink}" />
            <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />
            <a
                data-sly-test="${properties.textLink}"
                href="${textLink.relativePublishLink}"
                data-sly-attribute.target="${properties.target}"
                data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                data-sly-attribute.rel="${properties.rel}"
                data-sly-attribute.title="${properties.linkTitleAttr}"
                data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                data-sly-unwrap="${!textLink}">
                <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
            </a>
            <sly
                data-sly-test="${!properties.textLink}"
                data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
        </div>
        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>

        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, cssClassNames='modal-image js-modal-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>

<sly data-sly-test="${properties.imageSizingOptions == 'FixedMinMaxWidth'}">
    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        max-width="${properties.maxWidthFixed}"
        min-width="${properties.minWidth}"
        data-sly-attribute.imageSizingOptions="${properties.imageSizingOptions}"
        class="image-component ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''} ${properties.isLeftAligned ? 'left-aligned' : ''}">
        <div
            class="image-container ${properties.enableModal && !properties.textLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.textLink}" />
            <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />
            <a
                data-sly-test="${properties.textLink}"
                href="${textLink.relativePublishLink}"
                data-sly-attribute.target="${properties.target}"
                data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                data-sly-attribute.rel="${properties.rel}"
                data-sly-attribute.title="${properties.linkTitleAttr}"
                data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                data-sly-unwrap="${!textLink}">
                <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
            </a>
            <sly
                data-sly-test="${!properties.textLink}"
                data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
        </div>
        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>

        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, cssClassNames='modal-image js-modal-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>
<sly data-sly-test="${properties.imageSizingOptions == 'FixedMaxWidthAndPercentageWidth'}">
    <div
        data-sly-test="${hasContent}"
        data-mbox-id="${properties.mboxId}"
        max-width="${properties.maxWidth}"
        width="${properties.width}"
        data-sly-attribute.imageSizingOptions="${properties.imageSizingOptions}"
        class="image-component ${properties.paddingTop} ${properties.paddingBottom} ${properties.isBanner ? 'banner-img' : ''} ${properties.roundImageMobileLarge ? 'img-mobile-width-unset' : ''} ${properties.isLeftAligned ? 'left-aligned' : ''}">
        <div
            class="image-container ${properties.enableModal && !properties.textLink ? 'js-modal-open modal-enabled' : ''} ${properties.roundImage} ${properties.roundImage && properties.roundImageBorder ? 'image-round-border' : ''}">
            <sly data-sly-use.textLink="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.textLink}" />
            <sly data-sly-test.enableDownloadHeading="${properties.linkDownload == 'enableDownload'}" />
            <a
                data-sly-test="${properties.textLink}"
                href="${textLink.relativePublishLink}"
                data-sly-attribute.target="${properties.target}"
                data-sly-attribute.referrerpolicy="${properties.referrerpolicy}"
                data-sly-attribute.rel="${properties.rel}"
                data-sly-attribute.title="${properties.linkTitleAttr}"
                data-sly-attribute.download="${enableDownloadHeading ? (properties.linkFilename ? properties.linkFilename : true ) : false }"
                data-sly-unwrap="${!textLink}">
                <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
            </a>
            <sly
                data-sly-test="${!properties.textLink}"
                data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, loading=properties.isFetchPriorityHigh}" />
        </div>
        <div
            data-sly-test="${properties.enableCaption && properties.caption}"
            class="image-caption">
            ${properties.caption}
        </div>

        <sly data-sly-test="${properties.enableModal}">
            <div class="modal js-modal js-modal-close">
                <div class="modal-dialog">
                    <div class="modal-content js-modal-content">
                        <button
                            class="button-close js-modal-button"
                            title="Close (Esc)"
                            aria-label="Close (Esc)">
                            ×
                        </button>
                        <sly data-sly-call="${image.basic @ imagePath=properties.fileReference, alt=properties.alt, cssClassNames='modal-image js-modal-image'}" />
                    </div>
                </div>
            </div>
        </sly>
    </div>
</sly>
