function ImageComponent(element) {
    this.el = element;

    this.selectors = {
        modal: "js-modal",
        modalContent: "js-modal-content",
        modalImage: "js-modal-image",
        modalButton: "js-modal-button",
        modalOpen: "js-modal-open",
        modalClose: "js-modal-close",
    };

    this.$modal = this.el.getElementsByClassName(this.selectors.modal)[0];

    if (this.el.attributes["max-width"] || this.el.attributes["min-width"] || this.el.attributes["width"]) {
        var maxWidthAttr = this.el.attributes["max-width"];
        if (maxWidthAttr) {
            this.el.style.maxWidth = maxWidthAttr.value;
        }
        var minWidthAttr = this.el.attributes["min-width"];
        if (minWidthAttr) {
            this.el.style.minWidth = minWidthAttr.value;
        }
        var widthAttr = this.el.attributes["width"];
        if (widthAttr) {
            this.el.style.width = widthAttr.value;
        }
    } else {
        const imageContainerRound = this.el.querySelector(".image-round");
        if (imageContainerRound && window.screen.width <= 820) {
            this.el.classList.add("img-100");
        }
    }

    if (this.$modal != null) {
        this.$modalOpen = this.el.getElementsByClassName(this.selectors.modalOpen)[0];
        this.$modalContent = this.el.getElementsByClassName(this.selectors.modalContent)[0];
        this.$modalImage = this.el.getElementsByClassName(this.selectors.modalImage)[0];
        this.$modalButton = this.el.getElementsByClassName(this.selectors.modalButton)[0];
        this.$modalClose = this.el.getElementsByClassName(this.selectors.modalClose)[0];

        this.init(this);
    }
}

ImageComponent.prototype.init = function (el) {
    //click to open modal
    this.$modalOpen.addEventListener("click", function () {
        if (!el.$modal.classList.contains("reveal")) {
            el.openModal();
            el.$modal.classList.add("reveal");
        }
    });

    //click to close modal
    this.$modalClose.addEventListener("click", function (e) {
        if (el.$modal.classList.contains("reveal")) {
            el.$modal.classList.remove("reveal");
            setTimeout(function () {
                el.closeModal();
            }, 300);
            e.stopPropagation();
        }
    });
};

ImageComponent.prototype.openModal = function () {
    this.$modal.style.height = "100vh";
    this.$modalContent.style.height = "fit-content";
    this.$modalImage.style.height = "100%";
    this.$modalButton.style.height = "42px";
};

ImageComponent.prototype.closeModal = function () {
    this.$modal.style.height = 0;
    this.$modalContent.style.height = 0;
    this.$modalImage.style.height = 0;
    this.$modalButton.style.height = 0;
};

document.addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll(".image-component").forEach(function ($this) {
        new ImageComponent($this);
    });
});
