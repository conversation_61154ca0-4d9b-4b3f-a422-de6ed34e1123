// Main Font
$font-family: "777-Orbit", verdana, sans-serif;

// Extra font for Compatibility
$font-family-2: "777-Light", verdana, sans-serif;
$font-family-3: "777", verdana, sans-serif;
$font-family-4: "777-Medium", verdana, sans-serif;
$font-family-5: "777-Heavy", verdana, sans-serif;
$font-family-6: "777-DemiBold", verdana, sans-serif;
$font-family-7: "777-Bold", verdana, sans-serif;
$font-family-8: "777-ExtraBold", verdana, sans-serif;
$font-family-9: "777-Ultra", verdana, sans-serif;
$font-family-10: "777-Black", verdana, sans-serif;

// Other Fonts
$font-family-11: "Oleo-Script-Regular", sans-serif;
$font-family-12: "Oleo-Script-Bold", sans-serif;
$font-family-13: "Lobster", sans-serif;
$font-family-14: "Bello", sans-serif;
$font-family-15: "777-Book", sans-serif;
$font-family-16: Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;
$font-family-17: "777", Helvetica Neue, Helvetica, Roboto, Arial, sans-serif;
$font-family-18: "888 Regular", sans-serif;
$font-family-19: "888-Ultra", verdana, sans-serif;
$font-family-20: "888ExtraCondensed", verdana, sans-serif;
$font-family-21: "888-DemiBold", verdana, sans-serif;


$font-size: 10px; //this define value REM: 10px = 1rem
$font-size-18: 13px; //this define value REM: 13px = 1.3rem
$font-size-root: 19px;
$font-size-mobile: 19px;
$line-height: 1.5;
$line-height-2: 1.6;
$line-height-3: 1.2;
$line-height-4: 1.4;
$line-height-normal: 1;
$line-height-double: 2;

//== Color
$white: #ffffff;
$white2:#f5f5f5;
$black: #000000;
$dark-grey: #1c1c1c;
$dark-grey-2: #171717;
$light-grey: #f4f4f4;
$light-green: #7cf700;
$dark-green: #19322f;
$light-blue: #007cf7;
$warm-blue: #00c0ff;
$light-blue-hover: #7fbdfb;
$yellow: #ffbe0b;
$gray: #969696;
$light: #969696;
$light-grey-777: #eadfc3;
$lighter-grey: #b3b3b3;
$gray-border: #e1e1e1;
$gray-border-2: #efefef;
$accordion-container: #dadada;
$green: #7ff800;
$green-2: #7ffc02;
$orange: #fc6200;
$yellow-casino: #ffdb02;
$yellow2-casino: #fde500;
$gray2: #b7b7b7;
$gray3: #cbcccb;
$gray4: #717171;
$gray5: #6f6f6f;
$gray6: #6b6f6e;
$brown: #826458;
$red: #bc3554;

  // Normal mode
$color-background: #fcf6d5;
$color-container-bg: #1a1a1a;
$image-container-bg: url("../resources/images/icons/texture-background-color.png");
$color-container-gradient: #282828;
$color-font: #4c1723;
$color-link: #bc3554;
$color-link-hover: #bc3554;
$color-foreground: #202020;
$color-font-footer: #969696;
$color-font-footer-light: #d2d2d2;
$color-bullet-point: $color-font;
$primary-color: #0ea79b;

//vip navbar color
$vip-gold-border-color: #aa9a4c;
$vip-gold-color: #b19b52;
$vip-navbar-bg: #14171c;

//Vip three stars divider
$vip-three-stars-divider: #a2a5aa;

// Round image background
$round-image-background: #dbdbdb;

//CTA color
$cta-primary-bg: $red;
$cta-primary-bg-hover: $color-background;
$cta-primary-border-color: $red;
$cta-primary-text: $color-background;
$cta-primary-hover-text: $red;

$cta-primary-v2-bg: #fcf6d5;
$cta-primary-v2-bg-hover: #bc355433;
$cta-primary-v2-border-color: $red;
$cta-primary-v2-text: $red;
$cta-primary-v2-hover-text: $red;

$cta-secondary-bg: $light-grey-777;
$cta-secondary-bg-hover: $primary-color;
$cta-secondary-border-color: $primary-color;
$cta-secondary-text: $primary-color;
$cta-secondary-hover-text: $light-grey-777;

$cta-secondary-variant-1-bg: $primary-color;
$cta-secondary-variant-1-bg-hover: #fcf6d5;
$cta-secondary-variant-1-border-color: $primary-color;
$cta-secondary-variant-1-text: #fcf6d5;
$cta-secondary-variant-1-hover-text: $primary-color;

$cta-noglow-bg: $red;
$cta-noglow-bg-hover: $color-background;
$cta-noglow-border-color: $red;
$cta-noglow-text: $color-background;
$cta-noglow-hover-text: $red;  

$cta-glow-bg: $red;
$cta-glow-bg-hover: #fcf6d5;
$cta-glow-border-color: $red;
$cta-glow-text: #fcf6d5;
$cta-glow-hover-text: $red;
$glow-1: $red;
$glow-2: $red;

$cta-animated-bg: #fce403;
$cta-animated-bg-hover: $black;
$cta-animated-border-color: #fce403;
$cta-animated-text: $black;
$cta-animated-hover-text: #fce403;

//Specific colors for Component Image with two cta and two links
$four_cta_blue_bg: rgba(4, 102, 201, 0.909);
$four_cta_yellow_bg: rgba(253, 232, 68, 0.909);
$four_cta_secondary: #0466c9e8;

//Component specific colors
$back-to-top-bg: #909090;

$breadcrumb-color: #4c1723;
$carousel-bullet-bg: #d1d2d4;
$carousel-bullet-border: #b3b3b6;
$image-caption-text: #a3a3a3;
$image-caption-bg: #f7f7f7;
$separator-color: #ccc;
$past-events-color: #949599;
$dark-promotion: #3e3e3e;
$mobile-menu-icon-bg: #f2f2f2;
$social-share-close-btn: #666666;
$social-share-input-bg: #eeeeee;
$social-share-input-placeholder: #aaaaaa;
$social-share-input-required: #0080ff;
$social-share-btn-gradient: #45a2ff;
$social-share-container-border: #1d4fae;
$steps-primary: #bc3554;
$hot-games-primary: #c3e3c7;

//VIP Pages
$vip-bg-color: #181b20;
$vip-bg-image: url("../resources/images/icons/texture.png");

$vip-content-silver-bg: #14171c;
$vip-content-dark: #101318;
$vip-content-glow: #aa9a4c;
$vip-content-glow1: #94979c;
$vip-content-dark-bg: #1a1420;
$vip-content-dark-bg1: #16191e;
$vip-content-dark-rgba: rgba(21, 24, 29, 0.3);
$vip-content-dark-rgba1: rgba(25, 28, 33, 0.1);

$article-slider-border: #353535;

$jackpot-font-color: #fce403;

//Resposible Gaming Elements
$backgroundResposible: rgba(255, 255, 255, 0.3);
$backgroundGap: rgb(20, 23, 28);
$responsibleBreakPoint: 600px;

// Title
$title-color-brown: $brown;
$title-color-primary: $primary-color;
$title-color-secondary: $red;

// Complex Title Component
$complex-title-white-color: $white;
$complex-title-yellow-color: #fce403;
$complex-title-green-color: $light-green;
$complex-title-red-color: #b01619;

//Border Radius
$border-radius: 5rem; //50px
// Font-size

$font-size-1: 3.8rem; //38px
$font-size-2: 3.6rem; //36px
$font-size-3: 1.6rem; //16px
$font-size-4: 2.24rem; //22.4px
$font-size-5: 1.2rem; //12px
$font-size-6: 1.1rem; //11px
$font-size-7: 1.44rem; //14.4px
$font-size-8: 4.2rem; //42px
$font-size-9: 3rem; //30px
$font-size-10: 2.5rem; //25px
$font-size-11: 2.1rem; //21px
$font-size-12: 1.65rem; //16.5px
$font-size-13: 2.24rem; //22.4px
$font-size-14: 1.28rem; //12.8px
$font-size-15: 3.2rem; //32px
$font-size-16: 1.5rem; //15px
$font-size-17: 2.3rem; //23px
$font-size-18: 1rem; //10px
$font-size-19: 1.8rem; //18px
$font-size-20: 1.7rem; //17px

// Font-Weights
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;
$font-weight-bold: 700;
$font-weight-medium-bold: 800;
$font-weight-extra-bold: 900;
$font-weight-black: 900;

// Heading
$font-size-h1: 3.7em; //37px
$font-size-h1-mobile: 3em; //30px;
$line-height-h1: 1.2;
$font-weight-h1: $font-weight-normal;
$font-family-h1: $font-family-6;
$font-color-h1: $primary-color;

$font-size-h2: 2.1em; //21px
$font-size-h2-mobile: 2.1em;
$line-height-h2: 1.2;
$font-weight-h2: $font-weight-semi-bold;
$font-family-h2: $font-family-6;
$font-color-h2: $dark-green;

$font-size-h3: 2.1em; //21px
$font-size-h3-mobile: 2.1em; //21px
$line-height-h3: 1.2;
$font-weight-h3: $font-weight-semi-bold;
$font-family-h3: $font-family-3;
$font-color-h3: $red;

$font-size-h4: 2.24rem; //22.4px
$font-size-h4-mobile: 1.9rem;
$line-height-h4: 1.2;
$font-weight-h4: $font-weight-semi-bold;
$font-family-h4: $font-family-3;
$font-color-h4: $red;

$font-size-h5: 1.92rem; //19,2px
$font-size-h5-mobile: 1.72rem;
$line-height-h5: 1.2;
$font-weight-h5: $font-weight-semi-bold;
$font-family-h5: $font-family-3;
$font-color-h5: $red;

$font-size-h6: 1.6rem; //16px
$font-size-h6-mobile: 1.6rem;
$line-height-h6: 1.2;
$font-weight-h6: $font-weight-semi-bold;
$font-family-h6: $font-family-3;
$font-color-h6: $red;

$font-size-jackpots: 3.84rem; //Jackpots Title
$font-size-jackpots-desktop: 3.36rem;
$font-size-jackpots-tablet: 2.88rem;
$font-size-jackpots-mobile: 1.92rem;
$line-height-jackpots: 1.2;
$font-weight-jackpots: $font-weight-normal;
$font-family-jackpots: $font-family-3;
$font-color-jackpots: $color-font;

//Header
$background-color-header: #000000;
$font-color-header: #ffffff;
$font-size-header-menu: 2.24rem; //22.4px
$font-size-header-menu-item: 1.6rem; //16px

$background-color-header-hover: #ffffff;
$font-color-header-hover: #000000;
$border-color-header: #ffffff;
$border-color-header-hover: #000000;
$border-color-header-grey: $gray;
$border-color-footer: #2c2c2c;
$outer-separator-color: #b6bfc8;
$inner-separator-color: #566573;

//Mobile Icon (Navbar)
$mobile-icon-cmp-height: 6.8rem;

// Accessibility tools
$accessibility-line-height-1: 3.36rem; // 33.6px
$accessibility-line-height-2: 2rem; // 20px
$accessibility-fonts-small: 12px;
$accessibility-fonts-medium: 16px;
$accessibility-fonts-large: 20px;
$zoom-small-h1: 27px;
$zoom-small-h2: 20.4px;
$zoom-medium-h1: 36px;
$zoom-medium-h2: 27.2px;
$zoom-large-h1: 45px;
$zoom-large-h2: 34px;

//Why-Us
$edit-mode-background : rgba(255, 255, 255, 0.75);
$popup-background-color: rgba(0, 0, 0, 0.6);

//Casino Promotion
$casino-promotion-title-back: hsla(0, 0%, 100%, 0) 75%;
$casino-promotion-teaser-border: #848484;
$casino-promotion-top-link-color: #b2b2b2;

//Header Bar
$header-bar-cta-background: rgb(18 18 18 / 50%);

//disclaimer
$disclaimer-grey-bg: #1d1f22;

// Numbered Cards
$numbered-cards-circle-bg: $primary-color;
$numbered-cards-sm-grid: 840px;

//Invite Friend Three Steps
$invite-friend-green: #7bf037;
$invite-friend-background: #232323;
$invite-friend-text: #cccccc;

//Footer
$background-color-footer: $color-background;
$color-font-footer: $gray;
$color-font-footer-light: #d3d3d3;
$font-size-footer: 2rem; //20px
$font-size-footer-license: $font-size-5;
$font-size-internal-footer-title: 2.4rem;
$font-size-footer-paragraph: $font-size-5;
$footer-separator: #232323;
$footer-grid-icon-border: #3f3e3e;
$footer-banner-background: #232323;

// GRID
$grid-width: 100%; // 100%
$grid-divider-color: #ffffffaa;
$gutter-vertical: 2rem; //20px
$gutter-horizontal: 2rem; // 20px

//Simplebar
$simplebar-dark-track-color: #ebebeb;
$simplebar-dark-scrollbar-color: #d9d9d9;
$simplebar-light-track-color: rgba(256, 256, 256, 0.2);
$simplebar-light-scrollbar-color: rgba(256, 256, 256, 0.8);

// Options
//
// Quickly modify global styling by enabling or disabling optional features.
$enable-grid-classes: true;
$enable-negative-margins: false;

// mixins media-breakpoint-up
$xs: 0; // = > is MOBILE FIRST
$sm: 640px; // = > TABLET
$sm-max: calc(640px - 1px); // TABLET: SPECIAL For @media (max-width: $sm-max)
$sm-max-landscape: 844px;
$sm-air: 820px; // = > TABLET AIR
$md: 1025px; // = > DESKTOP: For @media (min-width: $md)
$md-blog: 915px; //Custom media query
$md-max: calc(1025px - 1px); // > DESKTOP: SPECIAL For @media (max-width: $md-max)
$lg: 1200px; // DESKTOP BIG SIZE
$lg-max: calc(1200px - 1px); // > DESKTOP: SPECIAL For @media (max-width: $lg-max)
$height-mobile: 44rem; //440px
$height-tablet: 84rem; //840px
$height-desktop: 85rem; //850px

// Orbit client breakpoints
$xs-grid: 420px; // = > MOBILE
$sm-grid: 768px; // = > TABLET
$md-grid: 1280px; // = > DESKTOP
$lg-grid: 1480px; // = > DESKTOP BIG SIZE

$grid-breakpoints: (
    xs: 0,
    sm: 640px,
    //MOBILE
    md: 1024px,
    //TABLET
    lg: 1200px,
    //DESKTOP
);
// scss-docs-start container-max-widths
$container-max-widths: (
    sm: 576px,
    //MOBILE
    md: 1024px,
    //TABLET
    lg: 1140px,
    //DESKTOP
);

// scss-docs-end grid-breakpoints
@include _assert-ascending($grid-breakpoints, "$grid-breakpoints");
@include _assert-starts-at-zero($grid-breakpoints, "$grid-breakpoints");

$linear-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
$linear-gradient-light-grey: linear-gradient(180deg, rgba(244, 244, 244, 0) 65%, rgba(244, 244, 244, 0.97) 99%, rgba(244, 244, 244, 1) 100%);

$text-shadow: #fff 1px 1px 0, 2px 2px 0 #4c1723, 2px -2px 0 #4c1723, -2px 2px 0 #4c1723, -2px -2px 0 #4c1723, 2px 0px 0 #4c1723, 0px 2px 0 #4c1723, -2px 0px 0 #4c1723, 0px -2px 0 #4c1723, 1px 0px 1px #4c1723, 0px 1px 1px #4c1723, 2px 1px 1px #4c1723, 1px 2px 1px #4c1723, 3px 2px 1px #4c1723, 2px 3px 1px #4c1723, 4px 3px 1px #4c1723, 3px 4px 1px #4c1723, 5px 4px 1px #4c1723, 4px 5px 1px #4c1723, 6px 5px 1px #4c1723, 5px 6px 1px #4c1723, 7px 6px 1px #4c1723, 2px 2px 2px rgba(255, 190, 11, 0);
$text-shadow-2: 1px 2px 3px #51afaf;
$text-shadow-3: 5px 5px 5px #000;;
