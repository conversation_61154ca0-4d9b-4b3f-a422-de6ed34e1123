package holdings888.core.servlets;

import com.drew.lang.annotations.NotNull;
import holdings888.core.utils.PageUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;
import java.util.Enumeration;

import static holdings888.core.servlets.DamUtilsServlet.populateCasinoDropDownListWithAudiences;

/**
 * Java Servlet to populate 'Audience' dropdown in editor dialog Carousel Component
 *
 */
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Get Categories Servlet",
        "sling.servlet.resourceTypes=" + "/apps/CasinoPromoPageAudienceServlet"
})
public class CasinoPromoPageAudienceServlet extends SlingSafeMethodsServlet {
    private final transient Logger logger = LoggerFactory.getLogger(CasinoPromoPageAudienceServlet.class);



    @Override
    protected void doGet(@NotNull SlingHttpServletRequest request,
            @NotNull SlingHttpServletResponse response) {
        logger.info("[888] - [CasinoPromoPageAudienceServlet] - doGet");
        Enumeration<String> values = request.getHeaders("Referer");
        StringBuilder url = new StringBuilder();
        if (values != null) {
            while (values.hasMoreElements()) {
                String firstElement = values.nextElement();
                if (firstElement.contains("experience-fragments")) {
                    ResourceResolver resourceResolver = request.getResourceResolver();
                    url.append(PageUtils.findPromotionPagePathForXF(firstElement, resourceResolver));
                    url.append("/jcr:content/audiences");
                }
            }
        }
        populateCasinoDropDownListWithAudiences(request, url.toString());
    }
}

