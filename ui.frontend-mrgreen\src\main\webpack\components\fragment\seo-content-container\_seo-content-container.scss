.seo-text-content-component {
    background-color: $color-background-seo;
    padding: 2.4rem 0;

    @media screen and (max-width: 765px) {
        padding: 3.2rem 1.3rem;
    }

    .grid-component {
        .column-container {
            .two-columns-50 .column {
                padding: 0;
            }
            .two-columns-50 .column:first-child {
                padding: 0;
                padding-right: 40px;
            }
            .column {
                padding: 0 4vw;
            }
            &.one-column {
                padding-left: 2vw;
                padding-right: 2vw;
            }
            &.one-column > .column:has(.two-columns-50) {
                padding: 0;

                .grid {
                    padding-top: 0;
                }
            }
        }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: white !important;
    }

    .rich-text {
        .rich-text-component {
            p,
            span,
            ul li, ol li {
                color: white;
            }
            ul li:before {
                color: white;
                display: none;
            }
            ul li p.black-bullet::before {
                color: $green;
            }

            p {
                font-size: 1.3rem;
                color: white;
            }
        }
    }
}
