package holdings888.core.utils;

import com.adobe.cq.dam.cfm.ContentElement;
import com.adobe.cq.dam.cfm.ContentFragment;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.resource.ResourceResolver;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static holdings888.core.utils.Constants.*;


public class PlaceholdersUtils {
    public static final  String COMMENT = "<!-- -->";
    public static final String PLACEHOLDER = "[PLACEHOLDER]";

    public static String clearFromComments(String text) {
        if (Objects.nonNull(text)) {
            text = text.replace(COMMENT, "");
        }
        return text;
    }

    public static Matcher getMatcher(@NonNull String text) {
        Pattern pattern = Pattern.compile("\\$\\$(\\S+)\\$\\$");
        return pattern.matcher(text);
    }

    // method for clearing text from the first paragraph to stay in the same line
    public static String clearFromFirstParagraph(String value) {
        Pattern pattern = Pattern.compile("^<p>(.*?)</p>(.*)", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(value);
        if (matcher.find()) {
            return matcher.group(1) + matcher.group(2);
        }
        return value;

    }

    // method for finding matches for {{placeholder}} in text using regex and replacing them to values from CF
    public static String processText(String text, ResourceResolver resourceResolver, String path) {
        if (Objects.nonNull(text)) {
            text = PlaceholdersUtils.clearFromComments(text);
            Matcher matcher = PlaceholdersUtils.getMatcher(text);
            while (matcher.find()) {
                String placeholderName = matcher.group(1);
                String placeholder = getPlaceholderValue(placeholderName, resourceResolver, path);
                text = text.replace(matcher.group(), placeholder);
            }
        }
        return text;
    }

    private static String getPlaceholderValue(String placeholderName, ResourceResolver resourceResolver, String path) {
        if (path.contains(EXPERIENCE_FRAGMENTS)) {
            path = path.replace(EXPERIENCE_FRAGMENTS, "");
        }
        String upToLangPath = LinkUtils.truncateUpToLang(path);
        String damLocaleRootPath = upToLangPath.replace(CONTENT, DAM_ROOT_PATH);
        ContentFragment placeholdersCF = resourceResolver.resolve(damLocaleRootPath + PLACEHOLDERS_FOLDER + "/" + placeholderName).adaptTo(ContentFragment.class);
        ContentElement cfEl = java.util.Optional.ofNullable(placeholdersCF).map(cf -> cf.getElement("value")).orElse(null);
        String value = PLACEHOLDER;
        if (Objects.nonNull(cfEl)) {
            value = java.util.Optional.ofNullable(cfEl.getContent()).orElse(PLACEHOLDER);
            if (StringUtils.isNotEmpty(value)) {
                value = PlaceholdersUtils.clearFromFirstParagraph(value);
            }
        }
        return value;
    }

}
