
package holdings888.core.servlets;

import holdings888.core.utils.PageUtils;
import io.wcm.testing.mock.aem.junit5.AemContext;
import io.wcm.testing.mock.aem.junit5.AemContextExtension;
import org.apache.http.HttpStatus;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.testing.mock.sling.servlet.MockSlingHttpServletRequest;
import org.apache.sling.testing.mock.sling.servlet.MockSlingHttpServletResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith({AemContextExtension.class, MockitoExtension.class})
class CasinoPromoPageCategoryServletTest {


    private CasinoPromoPageCategoryServlet promoServlet = new CasinoPromoPageCategoryServlet();

    @Test
    void doGetTest(AemContext context) {
        context.load().json("/mockup/promotion-carousel-xf/promotion_carousel_XF.json", "/content/888casino/ro/ro/promotion");
        context.currentResource("/content/888casino/ro/ro/promotion");

        Map<String, Object> map = new HashMap<>();
        map.put("item", "/content/888casino/ro/ro/promotion/ro-welcome-bonus");
        context.request().setParameterMap(map);


        MockSlingHttpServletRequest request = context.request();
        MockSlingHttpServletResponse response = context.response();
        promoServlet.doGet(request, response);
        assertNotNull(response);
        assertEquals(HttpStatus.SC_OK, response.getStatus());

    }

    @Test
    public void doGetMockTest(AemContext context) {
        MockSlingHttpServletRequest request = mock(MockSlingHttpServletRequest.class);
        Enumeration<String> values = mock(Enumeration.class);
        when(request.getHeaders("Referer")).thenReturn(values);
        when(values.hasMoreElements()).thenReturn(true, false);
        when(values.nextElement()).thenReturn("http://aem/experience-fragments.html/content/experience-fragments/888casino/ro/ro/");

        ResourceResolver resourceResolver = context.resourceResolver();
        PageUtils.findPromotionPagePathForXF("http://aem/experience-fragments.html/content/experience-fragments/888casino/ro/ro/", resourceResolver);
        PageUtils.findPromotionPagePathForXF("http://aem/experience-fragments.html/content/experience-fragments/888casino/it/it/", resourceResolver);

        MockSlingHttpServletResponse response = context.response();
        promoServlet.doGet(request, response);

        assertNotNull(response);
        assertEquals(HttpStatus.SC_OK, response.getStatus());
    }

    @Test
    public void doGetMockNotXFTest(AemContext context) {
        MockSlingHttpServletRequest request = mock(MockSlingHttpServletRequest.class);
        Enumeration<String> values = mock(Enumeration.class);
        when(request.getHeaders("Referer")).thenReturn(values);
        when(values.hasMoreElements()).thenReturn(true, false);
        when(values.nextElement()).thenReturn("http://content/casino/test");

        ResourceResolver resourceResolver = context.resourceResolver();
        PageUtils.findPromotionPagePathForXF("http://content/casino/test", resourceResolver);

        MockSlingHttpServletResponse response = context.response();
        promoServlet.doGet(request, response);

        assertNotNull(response);
        assertEquals(HttpStatus.SC_OK, response.getStatus());
    }

}
