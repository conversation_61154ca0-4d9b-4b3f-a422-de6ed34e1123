import Swiper, { Navigation } from "swiper";

(function () {
    function TopGamesSlider($cmp) {
        const selectors = {
            swiper: ".swiper-top-games",
            swiperWrap: ".swiper-wrapper",
            slide: ".swiper-slide",
            additionalGames: ".additional-games",
        };

        const $swiper = $cmp.querySelector(selectors.swiper);
        const $swiperWrap = $swiper.querySelector(selectors.swiperWrap);
        const additionalGamesCount = $cmp.querySelectorAll(selectors.additionalGames).length;

        function getAuthorSpecifiedGameIds() {
            const gameConfigElements = $cmp.querySelectorAll(".game-configuration");
            return new Set(Array.from(gameConfigElements).map((el) => el.getAttribute("data-game-id")));
        }

        function fetchGamesData() {
            const authorSpecifiedGameIds = getAuthorSpecifiedGameIds();
            const currConfig = document.querySelector(".top-games-slider-container");
            const currency = currConfig.getAttribute("data-currency") || "USD";
            const brand = currConfig.getAttribute("data-brand") || 0;
            const apiUrl = `https://www.smart-feeds.com/casinojackpot.xml?brand=${brand}&cur=${currency}`;
            return fetch(apiUrl)
                .then((response) => response.text())
                .then((data) => {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(data, "text/xml");
                    const jackpotElements = xmlDoc.getElementsByTagName("jackpot");

                    const jackpotsArray = Array.from(jackpotElements);
                    const games = [];

                    jackpotsArray.forEach((jackpot) => {
                        const jackpotId = jackpot.getAttribute("id");
                        const gameNames = jackpot.getAttribute("name").split(", ");
                        const amount = jackpot.getAttribute("amount");

                        gameNames.forEach((gameName, index) => {
                            const gameId = `${jackpotId}-${index + 1}`;
                            if (authorSpecifiedGameIds.has(gameId)) {
                                games.push({
                                    id: gameId,
                                    name: gameName,
                                    amount: formatAmount(amount, currency),
                                });
                            }
                        });
                    });

                    return games.length > 0 ? games : Promise.reject("No matching games found.");
                })
                .catch((error) => {
                    console.error("Error fetching or processing game data:", error);
                    return [];
                });
        }

        function formatAmount(amount, currency) {
            amount = parseFloat(amount.replace(/[^\d.-]/g, ""));

            const options = { minimumFractionDigits: 2, maximumFractionDigits: 2 };
            const locale = currency === "EUR" || currency === "RON" ? "de-DE" : "en-US";

            let formattedAmount = amount.toLocaleString(locale, options);

            switch (currency) {
                case "USD":
                    formattedAmount = "$" + formattedAmount;
                    break;
                case "GBP":
                    formattedAmount = "£" + formattedAmount;
                    break;
                case "EUR":
                    formattedAmount = formattedAmount + "€";
                    break;
                case "RON":
                    formattedAmount += " RON";
                    break;
            }

            return formattedAmount;
        }

        function createGameElement(game) {
            const existingElement = $swiperWrap.querySelector(`.swiper-slide[data-game-id="${game.id}"]`);
            if (existingElement) {
                return existingElement;
            }

            const container = document.createElement("div");
            container.classList.add("swiper-slide");
            container.setAttribute("data-game-id", game.id);

            const gameConfig = document.querySelector(`.game-configuration[data-game-id="${game.id}"]`);
            const dialogName = gameConfig.getAttribute("data-game-name");
            if (gameConfig) {
                const icon = gameConfig.getAttribute("data-icon");
                const iconAlt = gameConfig.getAttribute("data-icon-alt");
                let destinationUrl = gameConfig.getAttribute("data-destination-url");

                container.style.backgroundImage = `url(${icon})`;
                container.setAttribute("title", iconAlt);
                destinationUrl = decodeURIComponent(destinationUrl);
                if (destinationUrl.includes("javascript:")) {
                    container.setAttribute("onclick", destinationUrl);
                } else {
                    container.setAttribute("onclick", `window.location.href='${destinationUrl}'`);
                }
            }

            const gameName = dialogName && dialogName.trim() !== "" ? dialogName : game.name;
            const gameInfo = document.createElement("div");
            gameInfo.classList.add("game-info");
            let gameInfoHTML = `<span class="game-name">${gameName}</span>`;
            if (game.amount && game.amount !== "$0.0") {
                gameInfoHTML += `<span class="game-jackpot">${game.amount}</span>`;
            }

            gameInfo.innerHTML = gameInfoHTML;
            container.appendChild(gameInfo);

            return container;
        }

        function populateAPIGames(games) {
            games.forEach((game) => {
                const gameElement = createGameElement(game);
                $swiperWrap.appendChild(gameElement);
            });
        }

        function initSwiper() {
            const swiper = new Swiper(selectors.swiper, {
                modules: [Navigation],
                centerInsufficientSlides: true,
                navigation: {
                    nextEl: ".top-games-butt-prev",
                    prevEl: ".top-games-butt-next",
                },
                loop: $swiperWrap.querySelectorAll(selectors.slide).length > 6,
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 20,
                    },
                    1300: {
                        slidesPerView: 4,
                        spaceBetween: 20,
                    },
                    1440: {
                        slidesPerView: 5,
                        spaceBetween: 20,
                    },
                    1800: {
                        slidesPerView: 7,
                        spaceBetween: 20,
                    },
                },
            });

            const initialSlideIndex = additionalGamesCount % swiper.slides.length;
            swiper.slideTo(initialSlideIndex, 0);
            setupCTAButtonListener(swiper);
        }

        function setupCTAButtonListener(swiper) {
            let ctaButtons = $cmp.querySelectorAll(".cta-component");
            ctaButtons.forEach((button) => {
                button.addEventListener("click", (e) => {
                    swiper.slides.forEach((slide) => {
                        slide.classList.remove("glow-animation");
                    });
                    if (button.classList.contains("roll")) {
                        e.preventDefault();
                    } else {
                        button.classList.add("roll");
                        animateSwiper(swiper, button);
                    }
                });
            });
        }

        function animateSwiper(swiper, button) {
            const totalSteps = Math.floor(Math.random() * 21) + 370;
            let currentStep = 0;
            function animate() {
                if (currentStep < totalSteps) {
                    let progress = currentStep / totalSteps;
                    let speedAdjustment = Math.sin(progress * Math.PI);

                    swiper.params.speed = 50 + 300 * (1 - speedAdjustment);

                    swiper.slideNext();
                    currentStep++;
                    requestAnimationFrame(animate);
                } else {
                    const activeSlide = swiper.slides[swiper.activeIndex];
                    swiper.slides.forEach((e) => {
                        if (activeSlide.getAttribute("data-swiper-slide-index") === e.getAttribute("data-swiper-slide-index")) {
                            e.classList.add("glow-animation");
                        }
                    });
                }

                swiper.slides.forEach(slide => {
                    if (slide.classList.contains("glow-animation")) {
                        button.classList.remove("roll");
                    }
                });
            }

            requestAnimationFrame(animate);
        }

        fetchGamesData()
            .then((games) => {
                if (games && games.length > 0) {
                    populateAPIGames(games);
                } else {
                    console.log("No games data available");
                }
                initSwiper();
            })
            .catch((error) => {
                console.error("Error initializing the top games carousel:", error);
            });
    }

    function setupAdditionalGames() {
        document.querySelectorAll(".swiper-slide.additional-games").forEach((game) => {
            const redirectionUrl = game.getAttribute("data-redirection");
            const iconUrl = game.getAttribute("data-additional-icon");

            if (iconUrl) {
                game.style.backgroundImage = `url(${iconUrl})`;
            }

            if (redirectionUrl) {
                const decodedUrl = decodeURIComponent(redirectionUrl);
                game.setAttribute("onclick", decodedUrl);
                game.style.cursor = "pointer";
            }
        });
    }

    document.querySelectorAll(".promotions-top-games-carousel").forEach(function ($cmp) {
        TopGamesSlider($cmp);
        setupAdditionalGames();
    });
})();
