package holdings888.core.utils;

import lombok.Getter;

public class Constants {

    public static final String UTF_8 = "UTF-8";
    public static final String JSON = "json";
    public static final String XML = "xml";
    public static final String HTML = ".html";
    public static final String HTL = ".htl";
    public static final String GIF = ".gif";
    public static final String AMP = ".amp";
    public static final String COM = "com";
    public static final String EN = "en";
    public static final String HTTP = "http://";
    public static final String HTTPS = "https://";
    public static final String WWW = "www";
    public static final String SLASH = "/";
    public static final String DOT = ".";
    public static final String COMMA = ",";
    public static final String HASH = "#";
    public static final String QUESTION_MARK = "?";
    public static final String AMPERSAND = "&";
    public static final String EQUAL = "=";
    public static final String DASH = "-";
    public static final String PLACEHOLDER_START = "{{";
    public static final String PLACEHOLDER_END = "}}";
    public static final String UNDERSCORE = "_";
    public static final String REQUEST_URL_PLACEHOLDER = "{{RequestUrl,/}}";
    public static final String AMP_PARAM = QUESTION_MARK + "amp";
    public static final String MRGREEN = "mrgreen";
    protected static final String[] ADDSCUT_LINKS_LIST = { "http://www.888.com", "http://www.888casino.com",
            "http://www.888sport.com" };
    public static final String PUBLISH = "publish";
    // REGEX
    public static final String QUESTION_MARK_REGEX = "\\?";

    public static final String CONTENT = "/content";
    public static final String CONTENT_ROOT_PATH = CONTENT + SLASH;
    public static final String APPLICATION_JSON = "application/json";
    public static final String FLAG = "flag";
    public static final String DAM_ROOT_PATH = "/content/dam/holdings888";
    public static final String EXPERIENCE_FRAGMENTS = "/experience-fragments";
    public static final String CONTENT_EXPERIENCE_FRAGMENTS_ROOT_PATH = CONTENT + EXPERIENCE_FRAGMENTS + SLASH;
    public static final String FLAGS_PATH = DAM_ROOT_PATH + "/flags";
    public static final String SOCIAL_ICONS = DAM_ROOT_PATH + "/social-icons";
    public static final String BLOG_AUTHORS_ROOT = "/content/888poker/es/es/blog/authors";
    public static final String BLOG_CATEGORIES_ROOT = "/content/888poker/es/es/blog";
    public static final String P_LIMIT = "p.limit";
    public static final String IMAGE_ALT = "imageAlt";
    public static final String TEXT = "text";
    public static final String HOMEPAGE_TEMPLATE = "/conf/888poker/settings/wcm/templates/poker--homepage";
    public static final String TEMPLATES_PATH = "/conf/888poker/settings/wcm/templates";


    // Localization
    public static final String LOCALE = "en_US"; // table format on localization
    public static final String DATE_DEFAULT_ADDITIONAL_TEXT = "of";
    public static final String LOCALE_ERROR = "Error on localization setup"; // table error message on localization
    public static final String HOMEPAGE = "homepage";

    // Author
    public static final String AUTHOR_NAME = "authorName";
    public static final String AUTHOR_IMG_LINK = "authorAvatar";
    public static final String AUTHOR_BIO_DESCRIPTION = "authorBio";
    public static final String AUTHOR_FOLLOW = "followText";
    public static final String AUTHOR_FB_LINK = "facebook";
    public static final String AUTHOR_TW_LINK = "twitter";
    public static final String AUTHOR_LN_LINK = "linkedin";
    public static final String ALL_ARTICLES_FROM = "allArticleFrom";
    public static final String ALL_ARTICLES_FROM_AUTOR_NAME = "All articles from {}";

    // Article
    public static final String ARTICLE_TITLE = "articleTitle";
    public static final String ARTICLE_AUTHOR = "articleAuthor";
    public static final String ARTICLE_COVER = "articleCover";
    public static final String ARTICLES_TAGS = "cq:tags";
    public static final int WORD_PER_MIN = 225;
    public static final String SPACE_TEXT = " ";
    public static final String TEXT_CONTAINER = "root/container/grid/column-1-wrapper/blog_large_small_font/article-body js-article-body";

    public static final String SHARE_PROPERTY_PARAMETERS = "socialShareInfo";
    public static final String SHARE_PROPERTY_INHERITANCE_DL = "shareInheritanceOptions";
    public static final String SHARE_INHERIT = "inherit";
    public static final int SHARE_ROOT_DEPTH = 2;
    public static final String ARTICLE_ANCHOR_TITLE = "anchorTitle";

    // Blog Category - Tag

    public static final int NUM_FEATURED_ARTICLES = 3;
    public static final String DATE_FORMAT = "dd MMM yyyy";
    public static final String BLOG_TAG_TEMPLATE = "Poker Blog | Tag Page";
    public static final String TAG_ROOT = "tags";

    // Page Depths
    public static final int HOMEPAGE_DEPTH = 3;


    // Placeholders
    public static final String PLACEHOLDERS_FOLDER = "/content-fragments/placeholders";

    // 888 Brands
    public static final String SPORT = "888sport";


    public static final String LANG_EN = "?lang=en";

    // CONSTRUCTOR
    private Constants() {
        // Do nothing
    }

    // ERROR CODE
    public static final int SUCCESS = 200; // doGet
    public static final int CREATE = 201; // doPost
    public static final int DELETE = 410; // doDelete
    public static final int SERVER_ERROR = 500; // Internal Server Error

    //NO AMP SERVLET
    public static final String PATH_UPDATE_AMPMODE = "/bin/updateampmode";
    public static final String REGION_PATH_PREFIX = "/content/888poker/";
    public static final String PROPERTY_TARGET_SEGMENTATION = "targetSegmentation";
    public static final String PROPERTY_AMP_MODE = "ampMode";
    public static final String VALUE_NO_AMP = "noAmp";

    @Getter
    public enum Regions {
        COM("com"),
        ES("es"),
        IT("it"),
        SE("se"),
        RO("ro"),
        CA("ca"),
        DK("dk");

        private final String region;

        Regions(String region) {
            this.region = region;
        }

    }
}
