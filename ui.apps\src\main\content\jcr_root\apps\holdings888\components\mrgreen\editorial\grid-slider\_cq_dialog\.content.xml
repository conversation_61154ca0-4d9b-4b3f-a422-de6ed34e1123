<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
        xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
        xmlns:jcr="http://www.jcp.org/jcr/1.0"
        xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
        xmlns:cq="http://www.day.com/jcr/cq/1.0"
        jcr:primaryType="nt:unstructured"
        jcr:title="Three Images Taboola - Configuration"
        sling:resourceType="cq/gui/components/authoring/dialog"
        extraClientlibs="[holdings888.components.author.editor,rte.dialog.rich-text]">

        <content jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/container">
                <items jcr:primaryType="nt:unstructured">
                        <tabs jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                                maximized="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                        <main jcr:primaryType="nt:unstructured"
                                                jcr:title="Main"
                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                margin="{Boolean}true">
                                                <items jcr:primaryType="nt:unstructured">
                                                        <columns jcr:primaryType="nt:unstructured"
                                                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                                                margin="{Boolean}true">
                                                                <items
                                                                        jcr:primaryType="nt:unstructured">
                                                                        <column
                                                                                jcr:primaryType="nt:unstructured"
                                                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                                <items
                                                                                        jcr:primaryType="nt:unstructured">
                                                                                        <columnsNumber
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                granite:class="cq-dialog-dropdown-showhide"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                                                fieldLabel="Columns number"
                                                                                                required="{Boolean}true"
                                                                                                name="./columns">
                                                                                                <items
                                                                                                        jcr:primaryType="nt:unstructured">
                                                                                                        <two
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                selected="{Boolean}true"
                                                                                                                text="Two"
                                                                                                                value="two" />
                                                                                                        <twoZigzag
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                text="Two Zigzag Section"
                                                                                                                value="twoZigzag" />
                                                                                                        <three
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                text="Three"
                                                                                                                value="three" />
                                                                                                </items>
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        cq-dialog-dropdown-showhide-target=".mediaSelect-showhide-target" />
                                                                                        </columnsNumber>
                                                                                        <withBackground
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                                                                text="Add gradiant Background"
                                                                                                name="./withBackground"
                                                                                                checked="{Boolean}false"
                                                                                                uncheckedValue="{Boolean}false"
                                                                                                value="{Boolean}true"
                                                                                                granite:class="mediaSelect-showhide-target">
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        showhidetargetvalue="three" />
                                                                                        </withBackground>
                                                                                          <withBackgroundTwo
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                                                                text="Add gradiant Background"
                                                                                                name="./withBackgroundTwo"
                                                                                                checked="{Boolean}false"
                                                                                                uncheckedValue="{Boolean}false"
                                                                                                value="{Boolean}true"
                                                                                                granite:class="mediaSelect-showhide-target">
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        showhidetargetvalue="two" />
                                                                                        </withBackgroundTwo>
                                                                                        <column
                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                sling:resourceType="granite/ui/components/coral/foundation/container"
                                                                                                granite:class="mediaSelect-showhide-target">
                                                                                                <items
                                                                                                        jcr:primaryType="nt:unstructured">
                                                                                                        <zigzagElements
                                                                                                                jcr:primaryType="nt:unstructured"
                                                                                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                                                                                composite="{Boolean}true"
                                                                                                                validation="minmax-multifield"
                                                                                                                jcr:title="Row Items">
                                                                                                                <granite:data
                                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                                        max-items="6"
                                                                                                                        name="Rows" />
                                                                                                                <field
                                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                                        sling:resourceType="granite/ui/components/coral/foundation/container"
                                                                                                                        required="{Boolean}false"
                                                                                                                        name="./rows">
                                                                                                                        <items
                                                                                                                                jcr:primaryType="nt:unstructured">
                                                                                                                                <column
                                                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                                                                                                                        <items
                                                                                                                                                jcr:primaryType="nt:unstructured">
                                                                                                                                                <textPlaceholder
                                                                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                                                                                                        fieldLabel="Section id"
                                                                                                                                                        name="./id"
                                                                                                                                                        granite:class="cq-dialog-text-placeholder" />
                                                                                                                                        </items>
                                                                                                                                </column>
                                                                                                                        </items>
                                                                                                                </field>
                                                                                                        </zigzagElements>
                                                                                                </items>
                                                                                                <granite:data
                                                                                                        jcr:primaryType="nt:unstructured"
                                                                                                        showhidetargetvalue="twoZigzag" />
                                                                                        </column>
                                                                                </items>
                                                                        </column>
                                                                </items>
                                                        </columns>
                                                </items>
                                        </main>

                                        <targetConfig jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="holdings888/components/dialog-include/target-config" />
                                </items>
                        </tabs>
                </items>
        </content>
</jcr:root>