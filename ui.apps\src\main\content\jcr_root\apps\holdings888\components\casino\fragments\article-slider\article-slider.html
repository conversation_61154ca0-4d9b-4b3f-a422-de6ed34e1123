<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"/>
<sly data-sly-call="${clientlib.css @ categories='holdings888.swiper'}"/>
<sly data-sly-use.articleSliderModel="${'holdings888.core.models.ArticleSliderModel'}"/>
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-use.articles="${'holdings888/utils/multifield.js' @ multifieldName='articles'}"/>
<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"/>
<sly data-sly-test.hasContent="${properties.boxTitle}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<div id="${properties.id}" class="article-slider-component-xf" data-mbox-id="${properties.mboxId}">
    <div class="${properties.type}">
        <div data-sly-test="${properties.boxTitle}" data-sly-element="${properties.seoTag}" class="boxTitle h3 ${properties.textAlignment}">
            ${properties.boxTitle}
        </div>

        <div class="article-slider-container-xf">
            <div class="swiper-xf">
                <div class="swiper-wrapper wrapper-xf">
                    <sly data-sly-list.articleSlider="${articles}">
                        <div class="swiper-slide">
                            <sly data-sly-test="${articleSlider.properties.imageReference}" data-sly-call="${link.imageWithText @
                                properties=articleSlider.properties,
                                labelClasses='slide-text-xf',
                                linkUrlName='articleLink',
                                linkLabelName='articleText',
                                imagePath=articleSlider.properties.imageReference,
                                imageAlt=articleSlider.properties.articleAltImage,
                                imageWrapperClasses='slide-img-xf'
                                }"/>

                                <img class="slide-text-xf"
                                data-sly-test="${!articleSlider.properties.imageReference}" data-sly-element="img" 
                                src="${articleSlider.properties.articleImageUrl}" alt="External Image" class="slide-img-xf-external"/>
                        </div>
                    </sly>
                </div>
            </div>
            <div class="swiper-button-prev-xf"></div>
            <div class="swiper-button-next-xf"></div>
        </div>
    </div>
</div>

<!-- Add Back To Top ClientLib -->
<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"
     data-sly-call="${clientlib.all @ categories='holdings888.casino-article-slider'}" />

<sly data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"/>
<sly data-sly-call="${clientlibs.fe @ locations='casino-article-slider'}"/>
<!-- End Clientlib -->
