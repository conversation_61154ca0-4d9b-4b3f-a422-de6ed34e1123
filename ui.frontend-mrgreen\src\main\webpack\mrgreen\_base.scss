//== Base
// example
// basic reset
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    font-size: $font-size; //this define value REM: 10px = 1rem
    scroll-behavior: smooth;
    @media screen and (prefers-reduced-motion: reduce) {
        scroll-behavior: auto;
    }
}

.root {
    margin: 0;
    font-family: $font-family;
    font-size: $font-size;
    line-height: $line-height;
    color: $color-font;
    &.container {
        background-color: $color-background;
    }
}

.root {
    .xf-content-height {
        margin: 0;
    }
    a {
        color: $color-link;
        text-decoration: underline;

        &:hover {
            color: $color-link-hover;
        }
    }
    button,
    input,
    optgroup,
    select,
    textarea {
        font: inherit;
    }
    ol,
    ul {
        margin-bottom: 0;
        font-family: inherit;
        font-size: 14px;
        line-height: $line-height-custom;
        list-style-position: outside;
    }
    ol {
        margin-left: 3.2rem;
        margin-bottom: 2rem;
    }

    ul {
        list-style: none;
        margin-left: 2.2rem;
        margin-bottom: 0;

        li {
            position: relative;
            line-height: 30px;

            &::before {
                content: "•";
                position: absolute;
                left: -20px;
                margin-right: 10px;
                color: #202020;
                line-height: 1;
                font-size: 25px;
                display: flex;
                align-items: center;
            }

            &:has(.black-bullet) {
                &::before {
                    color: $black;
                }
            }
        }
    }
}
