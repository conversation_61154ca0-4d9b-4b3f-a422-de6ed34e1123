<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-test.hasContent="${properties.columns}" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />
<sly data-sly-use.clientlib="core/wcm/components/commons/v1/templates/clientlib.html"></sly>

<div
    class="grid-slider-component"
    data-sly-test="${hasContent}"
    data-mbox-id="${properties.mboxId}">
    <sly data-sly-call="${clientlib.css @ categories='holdings888.swiper'}"></sly>
    <div class="swiper">
        <div class="arrows">
            <div class="swiper-button-prev">
                <svg
                    width="2.8em"
                    height="2.8em"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    class="sc-fzoant iQBAoJ"
                    style="display: block">
                    <g>
                        <path
                            fill="currentColor"
                            d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                    </g>
                </svg>
            </div>
            <div class="swiper-button-next">
                <svg
                    width="2.8em"
                    height="2.8em"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    class="sc-fzoant iQBAoJ"
                    style="display: block">
                    <g>
                        <path
                            fill="currentColor"
                            d="M14.18,20.36a2,2,0,0,1-1.41-.58L6.4,13.41a2,2,0,0,1,0-2.82l6.37-6.37A2,2,0,0,1,15.6,7.05l-4.95,5L15.6,17a2,2,0,0,1-1.42,3.41Z"></path>
                    </g>
                </svg>
            </div>
        </div>
        <div class="swiper-wrapper ${properties.columns}">
            <sly data-sly-test="${properties.columns == 'twoZigzag'}">
                <sly data-sly-use.rows="${'holdings888/utils/multifield.js' @ multifieldName='rows'}" />
                <sly data-sly-list="${rows}">
                    <sly data-sly-set.zigzagImage="${['./rows/item', '/zigzag-image'] @ join=itemList.index}" />
                    <sly data-sly-set.zigzagRichText="${['./rows/item', '/zigzag-rich-text'] @  join=itemList.index}" />
                    <sly data-sly-set.zigzagTwoButtons="${['./rows/item' , '/zigzag-two-buttons'] @  join=itemList.index}" />

                    <div
                        class="swiper-slide zigzag-slide${wcmmode.edit ? ' edit-mode' : ''}"
                        id="${item.properties.id}">
                        <div class="grid zigzag-grid">
                            <div class="grid-component">
                                <div class="column-container">
                                    <div class="column">
                                        <div class="zigzag-grid__first">
                                            <sly data-sly-resource="${ zigzagImage @ resourceType='holdings888/components/editorial/image'}"></sly>
                                        </div>
                                        <div class="zigzag-grid__second">
                                            <sly data-sly-resource="${zigzagRichText @ resourceType='holdings888/components/editorial/rich-text', decorationTagName='div'}"></sly>
                                            <sly
                                                data-sly-resource="${zigzagTwoButtons @ resourceType='holdings888/components/mrgreen/editorial/two-buttons', decorationTagName='div'}"></sly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sly>
            </sly>
            <sly data-sly-test="${properties.columns == 'two'}">
                <div class="swiper-slide${properties.withBackgroundTwo ? ' with-background' : ''}">
                    <!-- <svg
                        data-sly-test="${properties.withBackgroundTwo}"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="544"
                        height="544"
                        viewBox="0 0 544 544">
                        <defs>
                            <radialGradient
                                id="radial-gradient"
                                cx="0.5"
                                cy="0.5"
                                r="0.5"
                                gradientUnits="objectBoundingBox">
                                <stop
                                    offset="0"
                                    stop-color="#366147" />
                                <stop
                                    offset="0.251"
                                    stop-color="#366147"
                                    stop-opacity="0.792" />
                                <stop
                                    offset="1"
                                    stop-color="#366147"
                                    stop-opacity="0" />
                            </radialGradient>
                        </defs>
                        <circle
                            id="Ellipse_1"
                            data-name="Ellipse 1"
                            cx="272"
                            cy="272"
                            r="272"
                            fill="url(#radial-gradient)" />
                    </svg> -->
                    <div>
                        <sly data-sly-resource="${'image1' @ resourceType='holdings888/components/editorial/image'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'rich-text1' @ resourceType='holdings888/components/editorial/rich-text'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'cta1' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"></sly>
                    </div>
                    <div
                        data-sly-resource="${ 'column-1-wrapper' @ resourceType='wcm/foundation/components/parsys',decorationTagName = 'div'}"
                        data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>
                <div class="swiper-slide${properties.withBackgroundTwo ? ' with-background' : ''}">
                    <!-- <svg
                        data-sly-test="${properties.withBackgroundTwo}"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="544"
                        height="544"
                        viewBox="0 0 544 544">
                        <defs>
                            <radialGradient
                                id="radial-gradient"
                                cx="0.5"
                                cy="0.5"
                                r="0.5"
                                gradientUnits="objectBoundingBox">
                                <stop
                                    offset="0"
                                    stop-color="#366147" />
                                <stop
                                    offset="0.251"
                                    stop-color="#366147"
                                    stop-opacity="0.792" />
                                <stop
                                    offset="1"
                                    stop-color="#366147"
                                    stop-opacity="0" />
                            </radialGradient>
                        </defs>
                        <circle
                            id="Ellipse_1"
                            data-name="Ellipse 1"
                            cx="272"
                            cy="272"
                            r="272"
                            fill="url(#radial-gradient)" />
                    </svg> -->
                    <div>
                        <sly data-sly-resource="${'image2' @ resourceType='holdings888/components/editorial/image'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'rich-text2' @ resourceType='holdings888/components/editorial/rich-text'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'cta2' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"></sly>
                    </div>
                    <div
                        data-sly-resource="${ 'column-2-wrapper' @ resourceType='wcm/foundation/components/parsys',decorationTagName = 'div'}"
                        data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>
            </sly>
            <sly data-sly-test="${properties.columns == 'three'}">
                <div class="swiper-slide${properties.withBackground ? ' with-background' : ''}">
                    <!-- <svg
                        data-sly-test="${properties.withBackground}"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="544"
                        height="544"
                        viewBox="0 0 544 544">
                        <defs>
                            <radialGradient
                                id="radial-gradient"
                                cx="0.5"
                                cy="0.5"
                                r="0.5"
                                gradientUnits="objectBoundingBox">
                                <stop
                                    offset="0"
                                    stop-color="#366147" />
                                <stop
                                    offset="0.251"
                                    stop-color="#366147"
                                    stop-opacity="0.792" />
                                <stop
                                    offset="1"
                                    stop-color="#366147"
                                    stop-opacity="0" />
                            </radialGradient>
                        </defs>
                        <circle
                            id="Ellipse_1"
                            data-name="Ellipse 1"
                            cx="272"
                            cy="272"
                            r="272"
                            fill="url(#radial-gradient)" />
                    </svg> -->
                    <div>
                        <sly data-sly-resource="${'image1' @ resourceType='holdings888/components/editorial/image'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'rich-text1' @ resourceType='holdings888/components/editorial/rich-text'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'cta1' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"></sly>
                    </div>
                    <div
                        data-sly-resource="${ 'column-1-wrapper' @ resourceType='wcm/foundation/components/parsys',decorationTagName = 'div'}"
                        data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>
                <div class="swiper-slide${properties.withBackground ? ' with-background' : ''}">
                    <!-- <svg
                        data-sly-test="${properties.withBackground}"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="544"
                        height="544"
                        viewBox="0 0 544 544">
                        <defs>
                            <radialGradient
                                id="radial-gradient"
                                cx="0.5"
                                cy="0.5"
                                r="0.5"
                                gradientUnits="objectBoundingBox">
                                <stop
                                    offset="0"
                                    stop-color="#366147" />
                                <stop
                                    offset="0.251"
                                    stop-color="#366147"
                                    stop-opacity="0.792" />
                                <stop
                                    offset="1"
                                    stop-color="#366147"
                                    stop-opacity="0" />
                            </radialGradient>
                        </defs>
                        <circle
                            id="Ellipse_1"
                            data-name="Ellipse 1"
                            cx="272"
                            cy="272"
                            r="272"
                            fill="url(#radial-gradient)" />
                    </svg> -->
                    <div>
                        <sly data-sly-resource="${'image2' @ resourceType='holdings888/components/editorial/image'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'rich-text2' @ resourceType='holdings888/components/editorial/rich-text'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'cta2' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"></sly>
                    </div>
                    <div
                        data-sly-resource="${ 'column-2-wrapper' @ resourceType='wcm/foundation/components/parsys',decorationTagName = 'div'}"
                        data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>
                <div class="swiper-slide${properties.withBackground ? ' with-background' : ''}">
                    <!-- <svg
                        data-sly-test="${properties.withBackground}"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="544"
                        height="544"
                        viewBox="0 0 544 544">
                        <defs>
                            <radialGradient
                                id="radial-gradient"
                                cx="0.5"
                                cy="0.5"
                                r="0.5"
                                gradientUnits="objectBoundingBox">
                                <stop
                                    offset="0"
                                    stop-color="#366147" />
                                <stop
                                    offset="0.251"
                                    stop-color="#366147"
                                    stop-opacity="0.792" />
                                <stop
                                    offset="1"
                                    stop-color="#366147"
                                    stop-opacity="0" />
                            </radialGradient>
                        </defs>
                        <circle
                            id="Ellipse_1"
                            data-name="Ellipse 1"
                            cx="272"
                            cy="272"
                            r="272"
                            fill="url(#radial-gradient)" />
                    </svg> -->
                    <div>
                        <sly data-sly-resource="${'image3' @ resourceType='holdings888/components/editorial/image'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'rich-text3' @ resourceType='holdings888/components/editorial/rich-text'}"></sly>
                    </div>
                    <div>
                        <sly data-sly-resource="${'cta3' @ resourceType='holdings888/components/mrgreen/editorial/cta'}"></sly>
                    </div>
                    <div
                        data-sly-resource="${ 'column-3-wrapper' @ resourceType='wcm/foundation/components/parsys',decorationTagName = 'div'}"
                        data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>
            </sly>
        </div>
        <div class="swiper-pagination"></div>
    </div>
</div>
