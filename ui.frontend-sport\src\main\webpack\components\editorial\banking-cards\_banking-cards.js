function setupLinkListeners(depositList, withdrawalList, pidParam) {
    const depositLinkMenu = document.getElementById('deposit');
    const withdrawalLinkMenu = document.getElementById('withdrawal');

    if (pidParam && pidParam === 'withdrawal') {
        if (withdrawalLinkMenu && depositLinkMenu && depositList && withdrawalList) {
            depositLinkMenu.classList.remove('activeCat');
            withdrawalLinkMenu.classList.add('activeCat');
            depositList.classList.add('banking-list-disabled');
            withdrawalList.classList.remove('banking-list-disabled');
        }
    }

    withdrawalLinkMenu.addEventListener('click', function () {
        withdrawalLinkMenu.classList.add('activeCat');
        depositLinkMenu.classList.remove('activeCat');
        depositList.classList.add('banking-list-disabled');
        withdrawalList.classList.remove('banking-list-disabled');
    });

    depositLinkMenu.addEventListener('click', function () {
        depositLinkMenu.classList.add('activeCat');
        withdrawalLinkMenu.classList.remove('activeCat');
        withdrawalList.classList.add('banking-list-disabled');
        depositList.classList.remove('banking-list-disabled');
    });
}

function activeCard(bankingCards) {
    bankingCards.forEach(function (card) {
        const readme = card.querySelector('.banking-article__readmore');
        const readMore = readme.querySelector('.readMore');
        const readLess = readme.querySelector('.readLess');

        readme.addEventListener('click', function () {
            if (!isActive(card)) {
                closeOpenCards(bankingCards);
                card.classList.add('active-card');
                readLess.classList.remove('d-none');
                readMore.classList.add('d-none');
                // Scroll to the top of the card when opening, plus 10px offset
                setTimeout(function () {
                    const cardRect = card.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const targetY = Math.max(cardRect.top + scrollTop - 10, 0);
                    window.scrollTo({ top: targetY, behavior: 'smooth' });
                }, 40);
            } else {
                closeOpenCards(bankingCards);
            }
        });
    });
}
function closeOpenCards(bankingCards) {
    for (const card of bankingCards) {
        const readMore = card.querySelector('.readMore');
        const readLess = card.querySelector('.readLess');

        if (isActive(card)) {
            card.classList.remove('active-card');
            readMore.classList.remove('d-none');
            readLess.classList.add('d-none');
            // Scroll to the top of the card when closing, plus 10px offset
            setTimeout(function () {
                const cardRect = card.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = Math.max(cardRect.top + scrollTop - 10, 0);
                window.scrollTo({ top: targetY, behavior: 'smooth' });
            }, 40);
            break;
        }
    }
}

function changeBg(bgCustomElements) {
    bgCustomElements.forEach(function (el) {
        const bgImage = el.getAttribute('data-bg');
        const bgPath = encodeURI(bgImage);
        el.style.backgroundImage = `url(${window.location.origin}${bgPath})`;
    });
}
function isActive(div) {
    return div.classList.contains('active-card');
}
function bankingCardInit() {
    const bankingCardsCmp = document.querySelector('.banking-cards-component');
    const urlParams = new URLSearchParams(window.location.search);
    let pidParam = urlParams.get('pid');

    if (pidParam == null) pidParam = window.pid;

    if (bankingCardsCmp) {
        const depositList = document.querySelector('.deposit-list-js');
        const withdrawalList = document.querySelector('.withdrawal-list-js');
        const bankingCards = document.querySelectorAll('.banking-article');
        const bgCustomElements = bankingCardsCmp.querySelectorAll('.banking-article__header[data-bg]');

        activeCard(bankingCards);
        setupLinkListeners(depositList, withdrawalList, pidParam);

        if (bgCustomElements) {
            changeBg(bgCustomElements);
        }
    }
}

bankingCardInit();
