package holdings888.core.servlets;

import com.drew.lang.annotations.NotNull;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import holdings888.core.service.AdobeTargetApiService;
import holdings888.core.service.SQLRulesParameterService;
import holdings888.core.utils.HttpResponse;
import holdings888.core.utils.HttpsClient;
import holdings888.core.utils.PrivateKeyCertificate;
import holdings888.core.utils.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.ServletResolverConstants;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;

import javax.security.auth.login.LoginException;
import javax.servlet.Servlet;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static holdings888.core.utils.Constants.APPLICATION_JSON;

/**
 * Java Servlet to populate 'MboxIdList'
 */
@Slf4j
@Component(service = Servlet.class, property = {Constants.SERVICE_DESCRIPTION + "= Get MboxId List Servlet", ServletResolverConstants.SLING_SERVLET_PATHS + "=" + "/apps/getMboxId", ServletResolverConstants.SLING_SERVLET_EXTENSIONS + "=" + "json"})
public class MboxIdServlet extends SlingSafeMethodsServlet {
    protected static final String LOCATIONS = "locations";
    protected static final String OPTIONS = "options";
    protected static final String EXPERIENCES = "experiences";
    protected static final String OPTION_LOCATIONS = "optionLocations";
    protected static final String NAME = "name";
    protected static final String OFFER_ID = "offerId";
    protected static final String OPTION_LOCAL_ID = "optionLocalId";
    protected static final String LOCATION_LOCAL_ID = "locationLocalId";
    protected static final String ID = "id";
    protected static final String FORCE_UPDATE = "forceUpdate";

    @Reference
    transient AdobeTargetApiService adobeTargetApiService;
    @Reference
    transient SQLRulesParameterService sqlRulesParameterService;
    @Reference
    transient PrivateKeyCertificate privateKeyCertificate;

    @SuppressWarnings("java:S2095") // "Resources should be closed" warning for ExecutorService (closed in finally block)
    @Override
    protected void doGet(@NotNull SlingHttpServletRequest request, @NotNull SlingHttpServletResponse response) throws IOException {
        log.debug("[888] - [MboxIdServlet] - doGet");

        String forceUpdate = request.getParameter(FORCE_UPDATE);
        if (StringUtils.isNotBlank(forceUpdate)) {
            ResourceResolver resourceResolver = request.getResourceResolver();
            JsonArray jsonArray = retrieveMboxIdFromSqlDB(resourceResolver, response);
            response.getWriter().write(jsonArray.toString());
        } else {
            final String accessToken = adobeTargetApiService.getAccessToken();
            JsonArray audiencesArray = adobeTargetApiService.retrieveActivities(accessToken);
            List<String> activityIdList = new ArrayList<>();
            getActivityIdList(audiencesArray, activityIdList);
            final String idOfferTrue = adobeTargetApiService.getOfferTrue(accessToken);

            // Create a thread-safe ConcurrentSkipListSet to store the "name" values
            ConcurrentSkipListSet<String> namesSet = new ConcurrentSkipListSet<>();

            long startTime = System.nanoTime(); // Start the stopwatch

            // Create a thread pool
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            try {
                for (String idActivity : activityIdList) {
                    executorService.submit(() -> processActivity(idActivity, accessToken, namesSet, idOfferTrue));
                }
            } finally {
                // Shutdown the executor service and wait for tasks to complete
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    log.error("[888] - [MboxIdServlet] - Error waiting for executor service to shutdown: {}", e.getMessage());
                }
            }


            Stopwatch.durationLog(startTime, "[888] - [MboxIdServlet] - create namesSet - Duration: {} ms", log, Stopwatch.LogLevel.INFO);

            response.getWriter().write(String.valueOf(namesSet));
        }
        response.setContentType(APPLICATION_JSON);
    }

    private void processActivity(String idActivity, String accessToken, ConcurrentSkipListSet<String> namesSet, String idOfferTrue) {
        try {
            String activityJson = adobeTargetApiService.retrieveActivity(accessToken, idActivity);
            JsonObject activity = JsonParser.parseString(activityJson).getAsJsonObject();
            JsonArray options = activity.has(OPTIONS) ? activity.getAsJsonArray(OPTIONS) : new JsonArray();

            options.forEach(optionEl -> {
                JsonObject option = optionEl.getAsJsonObject();
                String offerId = option.get(OFFER_ID).getAsString();
                if (StringUtils.isNotBlank(offerId) && offerId.equals(idOfferTrue)) {
                    processOption(activity, option, namesSet);
                }
            });
        } catch (Exception e) {
            log.error("[888] - [MboxIdServlet] - Error processing activityId {}: {}", idActivity, e.getMessage());
        }
    }

    private void processOption(JsonObject activity, JsonObject option, ConcurrentSkipListSet<String> namesSet) {
        String optionLocalId = option.get(OPTION_LOCAL_ID).getAsString();
        JsonArray experiences = activity.getAsJsonArray(EXPERIENCES);

        experiences.forEach(experienceEl -> {
            JsonObject experience = experienceEl.getAsJsonObject();
            JsonArray optionLocations = experience.getAsJsonArray(OPTION_LOCATIONS);

            optionLocations.forEach(optionLocationEl -> {
                JsonObject optionLocation = optionLocationEl.getAsJsonObject();
                String experienceOptionLocalId = optionLocation.get(OPTION_LOCAL_ID).getAsString();

                if (StringUtils.isNotBlank(experienceOptionLocalId) && experienceOptionLocalId.equals(optionLocalId)) {
                    addMboxNames(activity, optionLocation, namesSet);
                }
            });
        });
    }

    private void addMboxNames(JsonObject activity, JsonObject optionLocation, ConcurrentSkipListSet<String> namesSet) {
        String locationLocalId = optionLocation.get(LOCATION_LOCAL_ID).getAsString();
        JsonObject locations = activity.getAsJsonObject(LOCATIONS);
        JsonArray mboxes = locations.getAsJsonArray("mboxes");

        mboxes.forEach(mboxEl -> {
            JsonObject mbox = mboxEl.getAsJsonObject();
            String mboxLocationLocalId = mbox.get(LOCATION_LOCAL_ID).getAsString();

            if (StringUtils.isNotBlank(mboxLocationLocalId) && mboxLocationLocalId.equals(locationLocalId)) {
                String mboxName = mbox.get(NAME).getAsString();
                namesSet.add("\"" + mboxName + "\"");
            }
        });
    }

    private void getActivityIdList(JsonArray audiencesArray, List<String> activityIdList) {
        audiencesArray.forEach(audience -> {
            JsonObject audienceObj = audience.getAsJsonObject();
            String id = audienceObj.get("id").getAsString();
            String type = audienceObj.get("type").getAsString();
            String state = audienceObj.get("state").getAsString();
            if ("xt".equals(type) && "approved".equals(state)) {
                activityIdList.add(id);
            }
        });
    }


    private JsonArray retrieveMboxIdFromSqlDB(ResourceResolver resourceResolver, SlingHttpServletResponse response) {
        long startTime = System.nanoTime(); // Start the stopwatch

        try {
            KeyStore keyStore = privateKeyCertificate.getKeyStore(resourceResolver);

            URIBuilder baseURL = new URIBuilder(sqlRulesParameterService.getMboxIdUrl());
            URI destinationURL = baseURL.addParameter(FORCE_UPDATE, "true").build();

            HttpsClient _client = new HttpsClient();
            HttpResponse res = _client.getForMboxIdSqlDb(destinationURL.toString(), true, keyStore);
            String responseBody = res.getContent();

            Stopwatch.durationLog(startTime, "[888] - [MboxIdServlet] - retrieveMboxIdFromSqlDB - Duration: {} ms", log, Stopwatch.LogLevel.INFO);

            Gson gson = new Gson();
            return gson.fromJson(responseBody, JsonArray.class);

        } catch (ExceptionInInitializerError | URISyntaxException | UnrecoverableKeyException | KeyManagementException |
                 KeyStoreException | NoSuchAlgorithmException | IOException | LoginException |
                 org.apache.sling.api.resource.LoginException e) {
            log.error("[888] - MboxIdServlet - error in get request: {}", e.getMessage());
            response.setStatus(500);
            return new JsonArray();
        }
    }

}
