package holdings888.core.servlets;

import com.drew.lang.annotations.NotNull;
import holdings888.core.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;

import static holdings888.core.servlets.DamUtilsServlet.populateCasinoDropDownListWithCategories;
import static com.day.cq.wcm.api.constants.NameConstants.NN_CONTENT;
import static holdings888.core.utils.Constants.SLASH;

import java.util.Enumeration;

/**
 * Java Servlet to populate 'Category' dropdown in editor dialog Carousel Component
 *
 */
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Get Categories Servlet",
        "sling.servlet.resourceTypes=" + "/apps/CasinoPromoPageCategoryServlet"
})
public class CasinoPromoPageCategoryServlet extends SlingSafeMethodsServlet {
    private final transient Logger logger = LoggerFactory.getLogger(CasinoPromoPageCategoryServlet.class);

    @Override
    protected void doGet(@NotNull SlingHttpServletRequest request,
            @NotNull SlingHttpServletResponse response) {
        logger.info("[888] - [CasinoPromoPageCategoryServlet] - doGet");
        String url = StringUtils.EMPTY;
        StringBuilder urlXF= new StringBuilder();
        Enumeration<String> values = request.getHeaders("Referer");
        if (values != null) {
            while (values.hasMoreElements()) {
                String firstElement = values.nextElement();
                if (firstElement.contains("experience-fragments")) {
                    logger.info("[888] - [CasinoPromoPageCategoryServlet] - doGet experience-fragments");
                    ResourceResolver resourceResolver = request.getResourceResolver();
                    urlXF.append(PageUtils.findPromotionPagePathForXF(firstElement, resourceResolver));
                    urlXF.append("/jcr:content/categories");
                    url = urlXF.toString();
                } else {
                    logger.info("[888] - [CasinoPromoPageCategoryServlet] - doGet page");
                    url = firstElement;
                    url = url.split("/content")[1];
                    url = "/content".concat(url);
                    url = url.split(".html")[0];
                    url = url.concat(SLASH + NN_CONTENT + SLASH + "categories");
                }

            }
        }

        populateCasinoDropDownListWithCategories(request, url);
       

    }
}

