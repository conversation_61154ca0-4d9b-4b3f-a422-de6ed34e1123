<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-test.hasContent="${properties.card2ImageReference}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<div class="event-cards-container">
    <div class="event-card event-card--card1 ${properties.hideCard1 ? 'hidden-card' : ''}">
        <sly data-sly-use.processorCard1Image ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.card1ImageReference}"/>
        <sly data-sly-set.card1ImagePath ="${processorCard1Image.renditions['webp'].path || processorCard1Image.renditions['original'].path }"/>
        <img class="event-card__image" src="${card1ImagePath}" alt="${properties.card1AltImage}" title="${properties.card1AltImage}" loading="lazy">
        <h3 class="event-card__title">${properties.h3card1}</h3>
        <sly data-sly-resource="${'holdings888/components/casino/editorial/cta' @ resourceType='holdings888/components/casino/editorial/cta'}"/>
        <div class="details--card1">
            <sly data-sly-use.card1Text="${'holdings888.core.models.RichTextImp' @ text=properties.card1Text}"/>
            ${card1Text.text @ context='html'}
        </div>
    </div>
    
    <div class="event-card event-card--card2 ${properties.hideCard2 ? 'hidden-card' : ''}" data-sly-test="${hasContent}">
        <sly data-sly-use.processorCard2Image ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.card2ImageReference}"/>
        <sly data-sly-set.card2ImagePath ="${processorCard2Image.renditions['webp'].path || processorCard2Image.renditions['original'].path }"/>
        <img class="event-card__image" src="${card2ImagePath}" alt="${properties.card2AltImage}" title="${properties.card2AltImage}" loading="lazy">
        <h3 class="event-card__title">${properties.h3card2}</h3>
        <sly data-sly-resource="${'holdings888/components/casino/editorial/cta-2' @ resourceType='holdings888/components/casino/editorial/cta'}"/>
        <div class="details--card2">
            <sly data-sly-use.card2Text="${'holdings888.core.models.RichTextImp' @ text=properties.card2Text}"/>
            ${card2Text.text @ context='html'}
        </div>
    </div>
</div>
