<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"          jcr:primaryType="nt:unstructured"
          jcr:title="Divider"
          sling:resourceType="cq/gui/components/authoring/dialog">
    <content
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <divider
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                    fieldLabel="Divider"
                    name="./divider">
                <items jcr:primaryType="nt:unstructured">
                    <greenishLine
                            jcr:primaryType="nt:unstructured"
                            text="Greenish Line"
                            selected="true"
                            value="green-line"/>
                    <greyLine
                            jcr:primaryType="nt:unstructured"
                            text="Grey Line Divider"
                            value="grey-line"/>
                    <greyLineThic
                            jcr:primaryType="nt:unstructured"
                            text="Thic Grey Line Divider"
                            value="thic-grey-line"/>
                    <mrgreenLine
                            jcr:primaryType="nt:unstructured"
                            text="Mr Green Divider"
                            value="mr-green-line"/>
                    <mrgreenDoubleLine
                            jcr:primaryType="nt:unstructured"
                            text="Mr Green Double Divider"
                            value="mr-green-double-line"/>
                </items>
            </divider>
            <spacing
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                    fieldLabel="Top/Bottom Margin"
                    name="./spacing">
                <items jcr:primaryType="nt:unstructured">
                    <default
                            jcr:primaryType="nt:unstructured"
                            text="Default 25px"
                            selected="true"
                            value="default"/>
                    <medium
                            jcr:primaryType="nt:unstructured"
                            text="Medium 10px"
                            value="medium"/>
                    <small
                            jcr:primaryType="nt:unstructured"
                            text="Small 5px"
                            value="small"/>
                </items>
            </spacing>
        </items>
    </content>
</jcr:root>
