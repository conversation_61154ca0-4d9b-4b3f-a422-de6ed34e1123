  .hero-banner-light-lp {
    &:has(.backgroundDark) {
      background-color: $hero-banner-background-color-mobile;
    }

    .custom-banner {
      background-size: contain;
      background-repeat: no-repeat;
      bottom: 0;
      left: 0;
      right: 0;
      top: 0;
      padding-top: calc(9 / 16 * 75% + 5vw);
      position: relative;

      &.backgroundCover{
        background-size: cover;

        &.backgroundContain{
          @media screen and (max-width: 910px){
            background-size: contain;
          }
        }
      }

      &.backgroundDark {
        background-color: $hero-banner-background-color-mobile;
      }
  
      .above-cta-image {
        display: block;
        width: 31%;
        left: 15%;
        top: 11%;
        position: absolute;
      }
  
      .above-cta-rich-text {
        width: 30%;
        position: absolute;
        bottom: 39%;
        left: 15%;
      }

      .offerContainer {
        width: 35%;
        position: absolute;
        top: 6vw;
        left: 15%;
        padding: 0px 25px;
        border-left: 5px solid $color-link;

        @media (max-width: $md) and (orientation: portrait) {
          top: 24vw;
          left: 0;
          border-left: 13px solid #fa6200;
          padding: 0px 15px;
        }

        @media (max-width: 840px) and (orientation: portrait) {
          top: 36vw;
        }

        @media (orientation: landscape) and (max-width: 550px) {
          left: 15%;
          top: 5vw;
        }

        @media (max-width: 544px) and (orientation: portrait) {
          left: 0;
          width: 70%;
          border-left: 13px solid $color-link;
          padding: 0px 15px;
          top: 32vw;
        }

        @media (max-width: 380px) {
          top: 37vw;
        }

        .offer-type {
          color: white;
          font-weight: 700;
          letter-spacing: 1.3px;
          font-size: 1.3vw;
          font-family: $font-family-21;
          margin: 0;
          margin-top: 5px;

          @media (max-width: $md) and (orientation: portrait) {
            margin-top: 0;
            font-size: 1.6rem;
          }

          @media (max-width: 820px) and (orientation: portrait) {
            margin-top: 15px;
          }

          @media (max-width: 670px) and (orientation: landscape) {
            margin-top: 0;
          }

          @media (orientation: portrait) and (max-width: 544px) {
            font-size: 1.6rem;
            margin: 0;
          }
        }

        .offerPrice {
          display: flex;
          gap: 8px;
          align-items: flex-end;

          @media (max-width: 840px) and (orientation: portrait) {
            gap: 0;
          }

          .offer-amount {
            font-size: 5vw;
            margin: 0;
            font-weight: bold;
            line-height: 1;
            color: $orange-1;
            letter-spacing: normal;
            font-family: $font-family-8;

            @media (orientation: portrait) and (max-width: $md) {
              font-size: 10vw;
              letter-spacing: 1.2px;
            }

            @media (orientation: portrait) and (max-width: 544px) {
              font-size: 10vw;
              letter-spacing: 1.2px;
            }
          }

          .offer-amount-text {
            font-weight: 800;
            text-transform: uppercase;
            font-size: 2.3vw;
            line-height: 1;
            translate: 0px -8px;
            letter-spacing: -1px;
            color: $white-smoke;
            margin: 0;
            font-family: $font-family-8;

            @media (orientation: portrait) and (max-width: $md) {
              font-size: 4vw;
              translate: 0px -2px;
            }

            @media (orientation: portrait) and (max-width: 544px) {
              font-size: 4vw;
              translate: 0px -2px;
            }
          }
        }

        .subtitle {
          padding: 5px 10px;
          background-color: $subtitle-background-color;
          display: inline-block;

            @media only screen and (max-width: 600px) and (orientation: portrait) {
              width: 65%;
            }

            @media (max-width: 544px) and (orientation: portrait) {
              width: 105px;
            }

            &.deactivateCover{
              background-color: transparent;
            }

            p {
              font-size: 2.2vw;
              color: white;
              font-weight: 800;
              letter-spacing: 1px;
              margin: 0;
              line-height: 1;
              font-family: $font-family-8;

              @media (max-width: $md) and (orientation: portrait) {
                font-size: 1.8rem;
              }

              @media (max-width: 544px) and (orientation: portrait) {
                font-size: 1.8rem;
              }
            }
        }

        .offer-text {
          color: white;
          font-size: 1.28vw;
          letter-spacing: 1px;
          font-weight: 800;
          margin: 9px 0px;
          font-family: $font-family-21;
          padding: 0px 1.5px;
          margin-top: 5px;

          @media (max-width: $md) and (orientation: portrait) {
            font-size: 1.6rem;
            margin: 9px 0px;
          }

          @media (max-width: 670px) and (orientation: landscape) {
            margin: 0;
          }

          @media (max-width: 544px) and (orientation: portrait) {
            width: 100%;
            font-size: 1.6rem;
            margin: 9px 0px;
          }

          @media (max-width: 430px) {
            width: 70%;
          }
        }
      }
  
      .cta-button-container {
        bottom: 30%;
        left: 15%;
        height: 4.5vw;
        position: absolute;

        &.max-width-40 {
          @media screen and (orientation:landscape) {
            max-width: 40%;
          }
        }

        &.mobile-dark-background {
          @media screen and (orientation:portrait) and (max-width: 566px) {
            background-color: $hero-banner-background-color-mobile;
          }
        }
  
        .cta-comp {
          display: flex;
          .cta-component {
            display: flex;
            justify-content: start;
            align-items: center;
  
            .cta-wide-size {
              height: auto;
              width: 100%;
              a {
                font-size: 1.6vw;
                align-items: center;
                min-width: 28vw;
                padding: 1.8rem 0;
                border: 0.2vw solid #fce403;

                @media(max-width: 1440px) {
                  padding: 0.9rem 0;
                }
              }
            }
          }
        }

        .three-elements-container {
          .cmp-three-elm {
            .cmp-three-elm__step {
              .cmp-three-elm__step-content {
                .cmp-three-elm__step-title {
                  p {
                      font-size: 1.3vw;
                      line-height: 1;
                  }
                }
              }
            }
          }

          &.three-elements-vertical {

            .cmp-three-elm {
              flex-direction: column;

              @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
                flex-direction: row;
              }

              @media (max-width: 544px) {
                flex-direction: row;
              }

              &:first-child {
                @media (max-width: 544px) {
                  border-left: 3px solid #fa6200;
                }
              }

              .cmp-three-elm__step {
                @media (max-width: 544px) {
                  border-left: 3px solid #fa6200;
                }

              }
            }
          }
        }
  
        .additional-images {
          display: flex;
          justify-content: center;
          margin-top: 1rem;

          @media(max-width: $sm) {
            margin-top: 0;
          }

          img {
            object-fit: contain;
            max-height: 6vw;
            width: 28vw;
            padding: 3% 3% 0 3%;
            &:hover {
              cursor: pointer;
            }
          }
        }
  
        .cta-rte {
          text-align: center;
          padding-top: 1rem;
          width: 28vw;
  
          .text p {
            font-size: 0.7vw;
          }
        }
  
        .rte-threel {
          display: flex;
          flex-direction: column-reverse;
  
          .three-elements-container {
            padding-top: 1rem;
            width: 140%;
            max-width: 70vw;
          }
  
          .rte-container {
            padding-top: 2rem;
            width: 28vw;

            @media (max-width: 1480px) {
              padding-top: 1rem;
            }

            @media (max-width: 544px) {
              width: 92%;
            }
  
            .text p,
            .text p a,
            .text p a b {
              font-family: $font-family-27;
              font-size: 0.65vw;
              line-height: 1.2;

              @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
                font-size: 2.1vw;
              }

              @media (max-width: 560px) and (orientation: landscape) {
                font-size: 0.65vw;
              }

              @media (max-width: 544px) and (orientation: portrait) {
                font-size: 1.1rem;
              }
            }
          }
        }
  
        .and-rich-text {
          display: flex;
          flex-direction: row;
          gap: 10vw;
  
          .adim-rte-container {
            padding-right: 3rem;
  
            .rich-text-component {
              .text {
                width: 76%;
                p {
                  font-size: 0.9vw;
                  line-height: 1.1vw;
                  font-family: $font-family-9;
                }
              }
            }
          }
        }
      }

      &.cta-offer {
        .cta-button-container {
          height: fit-content;
          bottom: 0%;
          @media screen and (min-width:911px) and (max-width: 1080px) {
            bottom: 5%;
            @media screen and (orientation:landscape) {
              bottom: 10%;
            }
          }
          @media screen and (orientation:landscape) {
            @media screen and (min-width: 1300px) {
              bottom: 20%;
            }

            @media screen and (max-width: 940px) {
              bottom: 22%;
            }

            bottom: 7%;
          }
          @media screen and (min-width: 640px) and (max-width:768px) and (max-height:740px) and (orientation:landscape) {
            bottom: 12%;
          }
          @media screen and (min-width: 570px) and (max-width:640px) and (max-height:740px) and (orientation:landscape) {
            bottom: -30%;
          }

          &:has(.rte-threel) {
            bottom: 30%;

            @media (max-width: 1200px) {
              bottom: 25%;
            }

            @media (max-width: $md) {
              bottom: 16%;
            }

            @media (max-width: $md) and (min-height: $md) and (orientation: portrait) {
              bottom: -8%;
            }

            @media (max-width: $md) and (orientation: landscape) {
              bottom: 12%;
            }

            @media (max-width: $sm-air) {
              bottom: 8%;
            }

            @media (max-width: $sm-air) and (min-height: $sm-air) and (orientation: portrait) {
              bottom: -12%;
            }

            @media (max-width: 780px) and (min-height: 780px) and (orientation: portrait) {
              bottom: -8%;
            }

            @media (max-width: 560px) and (orientation: landscape) {
              bottom: 2%;
            }

            @media (max-width: 390px) {
              bottom: 3%;
            }

            @media (max-width: 330px) {
              bottom: 0;
            }
          }

          &:has(.three-elements-vertical) {
            bottom: 10%;

            @media (max-width: 1380px) and (orientation: landscape) {
              bottom: 4%;
            }

            @media (max-width: $md) {
              bottom: -4%;
            }

            @media (max-width: $md) and (min-height: $md) and (orientation: portrait) {
              bottom: -14%;
            }

            @media (max-width: 860px) and (orientation: landscape) {
              bottom: -10%;
            }

            @media (max-width: 820px) {
              bottom: -14%;
              z-index: 99;
            }


            @media (max-width: 820px) and (orientation: portrait) {
              bottom: -8%;
            }

            @media (max-width: 820px) and (min-height: 1020px) and (orientation: portrait) {
              bottom: -18%;
            }

            @media (max-width: 780px) and (min-height: 1020px) and (orientation: portrait) {
              bottom: -20%;
            }

            @media (max-width: 670px) and (orientation: landscape) {
              bottom: -10%;
            }

            @media (max-width: 570px) and (orientation: landscape) {
              bottom: 10%;
            }

            @media (max-width: 544px) and (orientation: portrait) {
              bottom: 4%;
            }

            @media (max-width: 390px) and (orientation: portrait) {
              bottom: -4%;
            }

            .rte-container {
              @media (max-width: $md) {
                padding-top: 1rem;
              }
            }

            .cta-comp .cta-component .cta-template.cta-wide-size {
              @media (max-width: $md) and (min-height: $md) and (orientation: portrait) {
                padding: 1rem 3rem;
              }

              @media (max-width: 820px) and (min-height: 1020px) and (orientation: portrait) {
                padding: 1rem 0;
              }

              @media (max-width: 544px) and (orientation: portrait) {
                padding: 1rem 0;
              }
            }

            .three-elements-container {
              @media (max-width: $md) {
                z-index: 1;
              }

              @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
                order: 1;
                padding: 3% 0;
              }

              @media (max-width: 544px) and (orientation: portrait) {
                order: 1;
                padding: 3% 0;
              }
            }

            .cmp-three-elm__step {
              border-width: 5px;
              max-width: 60%;
              width: 45%;

              @media screen and (orientation:landscape) and (max-width: 670px) and (max-height: 400px) {
                width: 100%;
              }

              .cmp-three-elm__step-no {
                font-family: $font-family-28 !important;
              }

              .cmp-three-elm__step-title p {
                font-family: $font-family-26;
              }
            }
          }
  
          .rte-threel .rte-container {
            width: 28vw;

            @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
              width: 95%;
            }

            @media (max-width: 560px) and (orientation: landscape) {
              width: 28vw;
            }

            @media (max-width: 544px) and (orientation: portrait) {
              width: 100%;
            }
  
            .text p,
            .text p a,
            .text p a b {
              font-family: $font-family-27;
              font-size: 0.65vw;
              line-height: 1.2;

              @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
                font-size: 2.1vw;
              }

              @media (max-width: 560px) and (orientation: landscape) {
                font-size: 0.65vw;
              }

              @media (max-width: 544px) and (orientation: portrait) {
                font-size: 1.1rem;
              }
            }
          }
        }
        height: auto;

        @media (max-width: 544px) and (orientation: portrait) {
          height: 150vw;
        }

      }

      @media(max-width: 1100px) and (orientation: portrait) {
        background-color: $hero-banner-background-color-mobile;
      }

      @media (max-width: 605px) {
        background-color: $hero-banner-background-color-mobile;
      }
    }
  
    @media (max-width: 1080px) and (orientation: portrait) {
      .custom-banner {
        padding-top: calc(11 / 9 * 95%);
        .above-cta-image {
          display: none;
        }
        .above-cta-rich-text {
          bottom: 16%;
          left: 5%;
          width: 45%;
        }
        .above-cta-rich-text,
        .and-rich-text .adim-rte-container {
          width: 80%;
        }
        .cta-button-container {
          bottom: 0;
          left: 0;
          height: 7.5vw;
          position: absolute;
          width: 100%;
          display: flex;
          flex-direction: column;
          .cta-comp {
            display: block;
            .cta-component {
              .cta-wide-size {
                width: 100%;
                padding: 1rem 1rem;
                a {
                  aspect-ratio: 60/9;
                  font-size: 4vw;
                }
              }
            }
          }
          .three-elements-container {
            width: 90%;
            margin: auto;
            .cmp-three-elm {
              .cmp-three-elm__step {
                .cmp-three-elm__step-no {
                  font-size: 8vw;
                }
                .cmp-three-elm__step-content {
                  .cmp-three-elm__step-title p {
                    font-size: 2.4vw;
                    line-height: 2vw;
                  }
                  .cmp-three-elm__step-text p {
                    font-size: 1.8vw;
                  }
                }
              }
            }

            &.three-elements-vertical {
              .cmp-three-elm {
                .cmp-three-elm__step {
                  .cmp-three-elm__step-no {
                    font-size: 3rem;
                  }

                  .cmp-three-elm__step-content {
                    .cmp-three-elm__step-title p {
                      font-size: 1.1rem;
                      line-height: 1.2;
                    }
                    .cmp-three-elm__step-text p {
                      font-size: 1rem;
                    }
                  }
                }
              }
            }
          }
          .cta-rte {
            padding-top: 1rem;
            text-align: center;
            width: 100%;
  
            .text p {
              font-size: 1.7vw;
              padding: 0.5rem 2rem;
            }
          }
          .additional-images img {
            max-height: 15vw;
            width: 75vw;
            padding: 0;
          }
          .rte-threel {
            align-items: center;
            .rte-container {
              padding-top: 1vw;
              .text p,
              .text p a,
              .text p a b {
                font-size: 1.7vw;

                @media (max-width: 544px) {
                  font-size: 1.1rem;
                }
              }
            }
          }
          .and-rich-text {
            flex-direction: column;
            gap: 0vw;
            .adim-rte-container {
              display: flex;
              padding: 0rem 2rem;
              margin: auto;
              width: 100%;

              .rich-text-component {
                .text {
                  width: 78%;
                  p {
                    font-family: $font-family-9;
                    font-size: 0.9vw !important;
                    line-height: 1.1vw !important;
                  }
                } 
              }
              
            }
            .rich-text-component .text p {
              font-size: 1.5vw !important;
              line-height: 1.8vw !important;
            }
          }
        }
      }
      height: 165vw;
    }

    @media (max-width: 1080px) and (orientation: portrait) {
      .custom-banner {
        .cta-button-container {
          .cta-comp {
            .cta-component {
              .cta-wide-size {
                padding: 1rem 3rem;
                a {
                  aspect-ratio: 75/9;
                  font-size: 4vw;
                }
              }
            }
          }

          .and-rich-text {
            .adim-rte-container {
              .rich-text-component {
                .text {
                  width: 100%;
                  p {
                    font-size: 2.1vw !important;
                    line-height: 2.3vw !important;
                  }
                } 
              }
            }
          }
        }
      }
    }

    @media (max-width: $md) and (min-height: 1020px) and (orientation: portrait) {
      .custom-banner {
        .cta-button-container {

          .three-elements-container {
            &.three-elements-vertical {
              .cmp-three-elm {
                .cmp-three-elm__step {
                  .cmp-three-elm__step-no {
                    font-size: 7.6vw;
                  }
  
                  .cmp-three-elm__step-content {
                    .cmp-three-elm__step-title p {
                      font-size: 2.3vw;
                      line-height: 1.2;
                    }
                    .cmp-three-elm__step-text p {
                      font-size: 2.3vw;
                    }
                  }
                }
              }
            }
          }
        }
      }
      height: 140vw;
    }

    @media (max-width: $sm-air) and (orientation: portrait) {
      .custom-banner {
        .cta-button-container {
          .cta-comp {
            .cta-component {
              .cta-wide-size {
                padding: 1rem 3rem;
                a {
                  aspect-ratio: 65/9;
                  font-size: 4vw;
                }
              }
            }
          }

          &:has(.three-elements-container) {
            .cta-comp {
              .cta-component {
                .cta-wide-size {
                  padding: 1rem 0;
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 544px) and (orientation: portrait) {
      .custom-banner {
        .cta-button-container {
          .three-elements-container {

            .cmp-three-elm {
              .cmp-three-elm__step {
                .cmp-three-elm__step-no {
                  font-size: 3rem;
                }
                .cmp-three-elm__step-content {
                  .cmp-three-elm__step-title p {
                    font-size: 1.1rem;
                    line-height: 1.2;
                  }
                  .cmp-three-elm__step-text p {
                    font-size: 1rem;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 576px) and (orientation: portrait) {
      .custom-banner {
        .cta-button-container {
          .cta-comp {
            .cta-component {
              .cta-wide-size {
                padding: 1rem 1rem;

                a {
                  aspect-ratio: 60/9;
                }
              }
            }
          }

          &:has(.three-elements-container) {
            .cta-comp {
              .cta-component {
                .cta-wide-size {
                  padding: 1rem 1rem;
  
                  a {
                    aspect-ratio: 60/9;
                  }
                }
              }
            }
          }
        }
      }

      .custom-banner.cta-offer {

        &:has(.three-elements-container) {
          .cta-comp {
            .cta-component {
              .cta-wide-size {
                padding: 1rem 0;

                a {
                  aspect-ratio: 60/9;
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: $sm-air) {
      .custom-banner {
        .cta-button-container {
          .and-rich-text {
            .adim-rte-container {
              .rich-text-component {
                .text {
                  width: 80%;
                } 
              }
            }
          }
        }
      }
    }

    @media (max-width: $sm-air) and (orientation:portrait) {
      .custom-banner {
        .cta-button-container {
          .and-rich-text {
            .adim-rte-container {
              .rich-text-component {
                .text {
                  width: 100%;
                } 
              }
            }
          }
        }
      }
    }

    @media screen and (orientation:portrait) and (max-width: $sm) {
      .custom-banner {
        .cta-button-container {
          .and-rich-text {
            .adim-rte-container {
              .rich-text-component {
                .text {
                  width: 100%;
                  p {
                    font-family: $font-family-9;
                    font-size: 1.1rem !important;
                    line-height: 3.8vw !important;
                  }
                } 
              } 
            }
          }
        }
      }
    }

    @media screen and (orientation:portrait) and (max-width: 400px) {
      height: 170vw;
    }

    @media screen and (orientation:portrait) and (max-width: 340px) {
      height: 175vw;
    }


    @media screen and (orientation:landscape) and (min-width: 500px) and (max-width: 1080px) and (hover: none) {
      height: auto;
      .custom-banner {
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        padding-top: calc(9 / 16 * 75% + 5vw);
        position: relative;
    
        .above-cta-image {
          display: block;
          width: 31%;
          left: 15%;
          top: 12%;
          position: absolute;
        }
    
        .above-cta-rich-text {
          width: 30%;
          position: absolute;
          bottom: 39%;
          left: 15%;
        }
    
        .cta-button-container {
          bottom: 27%;
          left: 15%;
          height: 4.5vw;
          position: absolute;
          width: auto;

          .cta-comp {
            display: flex;
            .cta-component {
              display: flex;
              justify-content: start;
              align-items: center;
    
              .cta-wide-size {
                height: auto;
                width: 100%;
                padding: 0;
                a {
                  font-size: 1.6vw;
                  align-items: center;
                  min-width: 28vw;
                }
              }
            }
          }
          .three-elements-container {
            width: 100%;
            margin: 0;
            .cmp-three-elm {
              .cmp-three-elm__step {
                .cmp-three-elm__step-no {
                  font-size: 5vw;
                }
                .cmp-three-elm__step-content {
                  .cmp-three-elm__step-title p {
                    font-size: 2vw;
                    line-height: 2vw;
                  }
                  .cmp-three-elm__step-text p {
                    font-size: 1.5vw;
                  }
                }
              }
            }
          }
          .three-elements-vertical {
            .cmp-three-elm {
              .cmp-three-elm__step {
                .cmp-three-elm__step-no {
                  font-size: 3.6vw;
                }

                .cmp-three-elm__step-content {
                  .cmp-three-elm__step-title p {
                    font-size: 1.3vw;
                    line-height: 1.3vw;
                  }
                  .cmp-three-elm__step-text p {
                    font-size: 1vw;
                  }
                }

              }

            }
          }
          .additional-images {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            img {
              object-fit: contain;
              max-height: 5vw;
              width: 28vw;
              padding: 0;
            }
          }
    
          .cta-rte {
            text-align: center;
            padding-top: 1rem;
            width: 28vw;
    
            .text p {
              font-size: 0.7vw;
            }
          }
    
          .rte-threel {
            display: flex;
            flex-direction: column-reverse;
            align-items: start;
    
            .three-elements-container {
              padding-top: 1rem;
              width: 140%;
              max-width: 78vw;
            }
    
            .rte-container {
              padding-top: 2rem;
              width: 90%;
    
              .text p {
                font-size: 0.7vw;
              }
            }
          }
    
          .and-rich-text {
            display: flex;
            flex-direction: row;
            gap: 10vw;
    
            .adim-rte-container {
              padding: 0rem 3rem 0rem 0rem;
              max-width: 47vw;
              .rich-text-component {
                .text {
                  p {
                    font-size: 1vw;
                    line-height: 1.5vw;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media screen and (orientation:landscape) and (min-width: 500px) and (max-width: 1080px) and (max-height: 400px) {
      height: auto;
      .custom-banner {
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        padding-top: calc(9 / 16 * 75% + 5vw);
        position: relative;

        .above-cta-image {
          display: block;
          width: 31%;
          left: 15%;
          top: 12%;
          position: absolute;
        }

        .above-cta-rich-text {
          width: 30%;
          position: absolute;
          bottom: 39%;
          left: 15%;
        }

        .cta-button-container {
          bottom: 27%;
          left: 15%;
          height: 4.5vw;
          position: absolute;
          width: auto;

          .cta-comp {
            display: flex;
            .cta-component {
              display: flex;
              justify-content: start;
              align-items: center;

              .cta-wide-size {
                height: auto;
                width: 100%;
                padding: 0;
                a {
                  font-size: 1.6vw;
                  align-items: center;
                  min-width: 28vw;
                }
              }
            }
          }
          .three-elements-container {
            width: 100%;
            margin: 0;
            .cmp-three-elm {
              .cmp-three-elm__step {
                .cmp-three-elm__step-no {
                  font-size: 5vw;
                }
                .cmp-three-elm__step-content {
                  .cmp-three-elm__step-title p {
                    font-size: 2vw;
                    line-height: 2vw;
                  }
                  .cmp-three-elm__step-text p {
                    font-size: 1.5vw;
                  }
                }
              }
            }
          }
          .additional-images {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            img {
              object-fit: contain;
              max-height: 5vw;
              width: 28vw;
              padding: 0;
            }
          }

          .cta-rte {
            text-align: center;
            padding-top: 1rem;
            width: 28vw;

            .text p {
              font-size: 0.7vw;
            }
          }

          .rte-threel {
            display: flex;
            flex-direction: column-reverse;
            align-items: start;

            .three-elements-container {
              padding-top: 1rem;
              width: 140%;
              max-width: 78vw;
            }

            .rte-container {
              padding-top: 2rem;
              width: 90%;

              .text p {
                font-size: 0.7vw;
              }
            }
          }

          .and-rich-text {
            display: flex;
            flex-direction: row;
            gap: 10vw;

            .adim-rte-container {
              padding: 0rem 3rem 0rem 0rem;
              max-width: 47vw;
              .rich-text-component {
                .text {
                  p {
                    font-size: 1vw;
                    line-height: 1.5vw;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media screen and (orientation:landscape) and (max-width: 670px) and (max-height: 400px) {
      .custom-banner {
        .cta-button-container {
          .three-elements-container {

            &.three-elements-vertical {
              padding-top: 0;

              .cmp-three-elm {
                .cmp-three-elm__step {
                  .cmp-three-elm__step-no {
                    font-size: 3rem;
                  }
                  .cmp-three-elm__step-content {
                    .cmp-three-elm__step-title p {
                      font-size: 1.1rem;
                      line-height: 1.2;
                    }
                    .cmp-three-elm__step-text p {
                      font-size: 1rem;
                    }
                  }
                }
              }

            }
          }
        }
      }
    }



    @media (max-width: 40rem) {
      .custom-banner .above-cta-rich-text {
        width: 80%;
      }
    }

    @media (max-width: 544px) and (orientation: portrait) {
          height: 167vw;
    }

    @media (max-width: 390px) and (orientation: portrait) {
      height: 167vw;
    }
  }

@media (max-width: 910px) and (orientation: portrait) {
  .custom-banner {
    .offerContainer {
        top: 32vw;
        bottom: unset;
        left: 0;
        width: 100%;
        border-left: 2vw solid $color-link;
        padding: 0.2rem 1.5rem;

        .offer-type {
          font-size: 4vw;
        }

        .offerPrice {
          .offer-amount {
            font-size: 14vw;
            letter-spacing: 1.2px;
          }

          .offer-amount-text {
            font-size: 25px;
          }
        }

        .subtitle {
          &.deactivateCover{
            background-color: transparent;
            p {
              color: $white !important;
            }
          }

          p {
            font-size: 25px;
            color: #000;
          }
        }
      }

    &.cta-offer {
      .cta-button-container {
        height: fit-content;
        padding: 0 3vw;

          .three-elements-container {
              width: 100%;
              margin: unset;
              max-width: unset;
          }
          .rte-threel {
            align-items: flex-start;
            .rte-container {
              width: 95%;

              .text p {
                font-size: 2.1vw;
              }
            }
          }
      }
      
      height: auto;
    }
  }
}

@media (max-width: 499px) {
  .custom-banner {
    .offerContainer {
        .offer-type {
          margin-top: 0;
        }
        .subtitle {
          width: 60%;
          p {
            font-size: 4.5vw;
            @media (orientation: portrait) {
              font-size: 6vw;
            }
          }
        }
        .offer-text {
          margin-bottom: 0;
        }
      }
  }
}
