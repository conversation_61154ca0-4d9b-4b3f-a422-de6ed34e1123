.content-banner-homepage {
    display: flex;
    align-items: center;
    width: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 45% 0;
    min-height: 22.3rem;
    position: relative;
    line-height: 1.3;

    &.image-pc-orbit {
        min-height: 26.236rem;
    }

    @media only screen and (max-width: 767px) {
        min-height: 14.2rem;
        p{
            line-height: 0 !important;
        }
    }

    .image-pc {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 45% 0;
        display: none;
        @media (min-width: $md) {
            display: flex;
        }
    }
    .image-tablet {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 45% 0;
        display: none;
        @media (min-width: $sm) {
            display: flex;
        }
        @media (min-width: $md) {
            display: none;
        }
    }
    .image-mobile {
        z-index: 0;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 100% 0;
        display: flex;
        @media (min-width: $sm) {
            display: none;
        }
    }
    .content-banner-homepage-wrapper {
        z-index: 1;
        display: flex;
        flex-direction: column;
        max-width: 60%;
        margin-left: 2em;
        width: 100%;
        opacity: 1;
        @media only screen and (max-width: 1600px) {
            margin-left: 1.5em;
        }
        .text-container {
            top: 0;
            position: absolute;
            @media (min-width: $sm) {
                top: unset;
                position: relative;
            }

            .title {
                font-size: 3em;
                font-weight: 800;
                font-family: $font-family-6;
                color: $yellow;
                text-shadow: $text-shadow;
            }

            .rich-text {
                flex-direction: column;
                color: $color-font;
                text-shadow: none;
                line-height: 2.73rem;
                font-family: $font-family-6;

                span {
                    &.text-shadow {
                        text-shadow: $text-shadow;
                    }
                }
            }
            .rich-text {
                p {
                    line-height: normal;
                    font-weight: $font-weight-normal;
                    font-family: $font-family;
                }

                span {
                    &.text-shadow {
                        text-shadow: $text-shadow;
                    }

                    &.m-text-rich-text {
                        font-size: 25px;
                        font-weight: $font-weight-normal;

                        @media (max-width: $lg-grid) {
                            font-size: 21px;
                        }

                        @media (max-width: calc($xs-grid - 1px)) {
                            font-size: 13px;
                        }
                    }

                    &.l-text-rich-text {
                        font-size: 33px;
                        font-weight: $font-weight-normal;

                        @media (max-width: $lg-grid) {
                            font-size: 28px;
                        }

                        @media (max-width: $sm) {
                            font-size: 25px;
                        }

                        @media (max-width: calc($xs-grid - 1px)) {
                            font-size: 18px;
                        }
                    }
                    &.xl-text-rich-text {
                        font-size: 38px;
                        font-weight: $font-weight-extra-bold;

                        @media (max-width: $lg-grid) {
                            font-size: 34px;
                        }

                        @media (max-width: calc($xs-grid - 1px)) {
                            font-size: 30px;
                        }
                    }
                    &.xxl-text-rich-text {
                        font-size: 45px;
                        font-weight: $font-weight-extra-bold;

                        @media (max-width: $lg-grid) {
                            font-size: 40px;
                        }

                        @media (max-width: calc($xs-grid - 1px)) {
                            font-size: 35px;
                        }
                    }
                }

                &.display-mobile {
                    margin-top: 15px;
                    display: none;
                    width: 100%;
                    height: 100%;

                    @media (max-width: calc($sm-grid - 1px)) {
                        display: block;
                    }

                    @media (max-width: 375px) {
                        p {
                            line-height: 0.8;
                        }
                        
                    }
                }

                &.display-tablet {
                    display: none;

                    @media (min-width: $sm-grid) and (max-width: calc($md-grid - 1px)) {
                        display: block;
                        width: 100%;
                        height: 100%;
                    }

                    @media (max-width: $xs-grid) {
                        margin-top: 8px;
                    }
                }

                &.display-pc {
                    margin-top: 25px;
                    display: none;
                    width: 100%;
                    height: 100%;

                    @media (min-width: $md-grid) {
                        display: block;
                    }
                }

                &.display-pc-orbit {
                    margin-top: 10px;
                }

            }
        }
        .cta-component {
            a {
                @media (min-width: $md-grid) {
                    line-height: 2.2em;

                    padding: 0 2em;

                    font-size: 1.2em;

                    min-width: 120px;
                }
            }
        }
        .cta-template a{
            @media screen and (max-width: 768px) {
                min-width: 0 !important;
                padding: 0.7rem 2.1rem !important;
            }
        }
    }
    .display-pc {
        display: none;
        @media (min-width: $md) {
            display: flex;
        }
    }
    .display-tablet {
        display: none;
        @media (min-width: $sm) {
            display: flex;
        }
        @media (min-width: $md) {
            display: none;
        }
    }
    .display-mobile {
        display: flex;
        @media (min-width: $sm) {
            display: none;
        }
    }
}