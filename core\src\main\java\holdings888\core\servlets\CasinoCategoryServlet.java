package holdings888.core.servlets;

import com.day.cq.wcm.api.Page;

import com.drew.lang.annotations.NotNull;
import holdings888.core.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;

import static holdings888.core.servlets.DamUtilsServlet.populateCasinoDropDownListWithCategories;


/**
 * Java Servlet to populate 'categories' dropdown in editor dialog of Promotion Page
 * Categories Page Properties
 */
@Component(service = Servlet.class, property = {
        Constants.SERVICE_DESCRIPTION + "= Get Categories Servlet",
        "sling.servlet.resourceTypes=" + "/apps/getCasinoCategories"
})
public class CasinoCategoryServlet extends SlingSafeMethodsServlet {
    private final transient Logger logger = LoggerFactory.getLogger(CasinoCategoryServlet.class);

    @Override
    protected void doGet(@NotNull SlingHttpServletRequest request,
            @NotNull SlingHttpServletResponse response) {
        logger.info("[888] - [CasinoCategoryServlet] - doGet");
        ResourceResolver resourceResolver = request.getResourceResolver();
        String url = request.getParameter("item");
        String lobbyPath = PageUtils.findPromotionPagePath(url, resourceResolver);
        Resource lobbyResource = resourceResolver.getResource(lobbyPath);
        if (lobbyResource != null) {
            Page page = lobbyResource.adaptTo(Page.class);
            Resource contentResource = page.getContentResource();
            Resource categoriesResource = contentResource.getChild("categories");
            if(categoriesResource != null) {
                String path = categoriesResource.getPath();
                populateCasinoDropDownListWithCategories(request, path);
            }
        }
    }
}
