.rich-text-component {
    word-break: break-word;
    font-weight: 400;
    .no-underline {
        color: inherit !important;
        display: inline-block;
    }
    p {
        padding-bottom: 10px;
        margin-bottom: 0;
    }
    select {
        color: $white;
        background: transparent;
    }
    .text {
        h6{
            margin-bottom: 21.855px;
            margin-top: 21.855px;
        }
    }

    // TABLE STYLE INSIDE RICH TEXT
    .text{
        .Table {
            overflow-x: auto;
        }
        .TableList,
        .table-wrapper,
        .TableFixed {
            text-indent: initial;
            border-collapse: collapse;
            border-spacing: 0;
            word-break: normal;
            font-size: smaller;
            overflow-x: auto;
            width: auto;
            border: 0;

            tbody {
                background-color: transparent;
                border: none;
                tr:nth-of-type(odd) {
                    background: $color-divider;
                }

                tr th {
                    padding: 10px 20px 10px 12px;
                    text-align: left;
                }

                tr td {
                    padding: 10px 20px 10px 12px;
                    text-align: left;
                    border: 1px solid transparent;
                }
            }

            &.green-table {
                tbody {
                    tr:nth-of-type(odd) {
                        background: $green-8;
                    }

                    tr th,
                    tr td {
                        color: $white;
                    }
                }
            }
        }

        .TableFixed {
            border-collapse: separate;
            border-spacing: 2px;
            table-layout:fixed;

            tbody {
                tr:nth-of-type(odd) {
                    background: transparent;
                }

                tr:first-child {
                    background-color: $light-grey-2;
                } 

                tr th,
                tr td {
                    font-size: 12px;
                    text-align: center;
                    border: solid 1px $white;
                    padding: 0;

                    p {
                        font-size: 12px;
                        padding: 10px 0 0;
                    }
                }
            }
        }

        @media only screen and (max-width: $sm-air) {
            .TableList,
            .table-wrapper {
                width: 100%;
            }
        }
        @media only screen and (min-width: $sm) {
            .TableList,
            .table-wrapper * {
                font-size: 1.6rem;
            }
        }
        @media (min-width: $sm) and (max-width: $sm-max-landscape) and (orientation: landscape) {
            .TableList,
            .table-wrapper {
                width: 100%;
            }
            .TableList,
            .table-wrapper * {
                word-break: break-word;
            }
        }
    }


    .modal-enabled:hover::before {
        border-radius: 19.2rem;
    }

    .text ul li ul {
        display: block;
    }
}
