$(function () {
    const exportToTargetScheduledAnchorButton = document.querySelector("#exportToTargetScheduled");

    if (exportToTargetScheduledAnchorButton) {
        const urlParams = new URLSearchParams(window.location.search);
        const pagePath = urlParams.get('item');
        const validatorSelector = '[data-foundation-validation~="dates.notchanged"]';
        let validator = getValidator()

        exportToTargetScheduledAnchorButton.on("click", function (e) {
            const startDatePicker = document.querySelector("#startDateExportToTarget");
            const endDatePicker = document.querySelector("#endDateExportToTarget");
            const startDate = startDatePicker?.value;
            const endDate = endDatePicker?.value;
            let oldStartDate = getJCRDate(startDatePicker);
            let oldEndDate = getJCRDate(endDatePicker);
            const servletUrl = '/apps/startWorkflow.json';

            if ((startDate != null && startDate.trim() !== '') && (endDate != null && endDate.trim() !== '')
                && (pagePath != null && pagePath.trim() !== '')) {

                let invalidTabs = getInvalidTabs();

                if (invalidTabs.length === 0) {
                    submitPagePropertiesAndExport();
                } else {
                    showAlertMessage("error", "Check warnings in " + invalidTabs + " tab(s)")
                }

            } else if (startDate == null || startDate.trim() === '') {
                showAlertMessage("error", "ERROR - Start Date is required");
            } else if (endDate == null || endDate.trim() === '') {
                showAlertMessage("error", "ERROR - End Date is required");
            }

            function submitPagePropertiesAndExport() {
                exportToTargetScheduledAnchorButton.disabled = true;

                let $pageForm = $('form#cq-sites-properties-form');
                $.post( $pageForm.attr("action"), $pageForm.serialize(), function(data) {
                    let status = $(data).find('#Status')?.text();
                    if (status && status === '200') {
                        console.debug("Page properties saved. Start export to Target")
                        exportToTarget();
                    } else {
                        showAlertMessage("error", "ERROR - Unable to save page properties");
                        exportToTargetScheduledAnchorButton.disabled = false;
                    }
                }, 'html' )
            }

            function exportToTarget() {
                const requestData = {
                    startDate: startDate,
                    endDate: endDate,
                    oldStartDate: oldStartDate,
                    oldEndDate: oldEndDate,
                    pagePath: pagePath
                };

                $.ajax({
                    url: servletUrl,
                    type: "POST",
                    data: JSON.stringify(requestData),
                    dataType: "json",
                    contentType: "application/json",
                    success: function (response, message) {
                        if (response.error) {
                            showAlertMessage("error", "Server ERROR - " + response.error);
                            exportToTargetScheduledAnchorButton.disabled = false;
                            validateDates();
                        } else {
                            let startDateWorkflowId = response.startDateWorkflowId;
                            showAlertMessage("success", response.message + " <br> " +
                                (startDateWorkflowId ? "Start date workflow ID: " + startDateWorkflowId +
                                    " scheduled on " + response.wfStartDate + "<br>" : "")
                                + "End date workflow ID: " + response.endDateWorkflowId
                                + " scheduled on " + response.wfEndDate);
                            exportToTargetScheduledAnchorButton.disabled = false;
                            validateDates();
                        }
                    },
                    error: function (data, message) {
                        showAlertMessage("error", "Server ERROR - " + message + " - " + data.status);
                        exportToTargetScheduledAnchorButton.disabled = false;
                        validateDates();
                    }
                });
            }

            function showAlertMessage(variant, message) {
                const exportToTargetWrapper = document.querySelector("#exportToTargetWrapper");
                let alert = exportToTargetWrapper.querySelector("coral-alert");
                const info = alert ? alert : new Coral.Alert();
                info.variant = variant;
                info.content.innerHTML = message;
                exportToTargetWrapper.appendChild(info);
            }

            function validateDates() {
                if (validator) {
                    document.querySelectorAll(validatorSelector).forEach(function (el) {
                        let result = validator.validate(el);
                        if (result) {
                            console.log("Validation error: " + result);
                        } else {
                            console.log("Validation success for element: " + el);
                            if (el.invalid) {
                                el.invalid = false;
                                let parentElement = el.parentElement;
                                parentElement.querySelector("[icon=alert]").remove();
                                parentElement.querySelector(".coral-Form-errorlabel").remove();
                                let panelId = parentElement.closest("coral-panel").id;
                                let promoTab = document.querySelector(`coral-tab[aria-controls=${panelId}]`);
                                if (promoTab) {
                                    promoTab.invalid = false
                                    promoTab.removeAttribute("aria-invalid");
                                }
                            }
                        }
                    });
                } else {
                    console.warn("Validator not found");
                }
            }

            function getInvalidTabs() {
                return $("coral-tab.is-invalid:not(.is-selected)").find("coral-tab-label")
                    .map(function() {
                    return $(this).text();
                }).get()
            }
        });

        function getValidator() {
            let registry = $(window).adaptTo("foundation-registry");
            return registry.get("foundation.validation.validator").find(v => validatorSelector === v.selector)
        }


        function getJCRDate(field) {
            return field.querySelector("[name=jcrValue]")?.value;
        }

    }
});

// MULTIFIELD
// Store all items per select name globally
const allItems = {};

// Keep track if listeners are attached
const attachedSelects = new WeakSet();

// Function to initialize all selects currently on the page
function initializeAllSelects() {
    const allCoralSelects = document.querySelectorAll('coral-select[name^="./audiences/item"]');

    allCoralSelects.forEach(select => {
        const selectName = select.name;
        // Initialize allItems
        if (!allItems[selectName]) {
            const items = select.querySelectorAll('coral-select-item');
            allItems[selectName] = Array.from(items).map(item => item.cloneNode(true));
        }

        // Attach change listener only once
        if (!attachedSelects.has(select)) {
            select.addEventListener('change', debouncedUpdate);
            attachedSelects.add(select);
        }
    });
}

// Function to update all selects by filtering based on selected values
function updateSelectedValuesAndFilterItems() {
    const allCoralSelects = document.querySelectorAll('coral-select[name^="./audiences/item"]');

    // Gather selected values from all selects
    const selectedValues = new Set();
    allCoralSelects.forEach(select => {
        const selected = select.selectedItem?.value;
        if (selected) {
            selectedValues.add(selected);
        }
    });

    allCoralSelects.forEach(select => {
        const selectedValueForThisSelect = select.selectedItem?.value;
        const selectName = select.name;

        // Clear all current items in the select
        Array.from(select.querySelectorAll('coral-select-item')).forEach(item => {
            item.remove();
        });

        // Re-add all original items and then filter them
        if (allItems[selectName]) {
            const itemsToAdd = [];

            const defaultEmptyOption = allItems[selectName].find(item => item.getAttribute('value') === "");
            if (defaultEmptyOption) {
                itemsToAdd.push(defaultEmptyOption.cloneNode(true));
            }

            allItems[selectName].forEach(originalItem => {
                const value = originalItem.getAttribute('value');

                // Only add items that are not selected in other selects,
                // or if it's the currently selected item for this specific select.
                // We no longer skip the empty value here as it's handled above.
                if (value !== "" && (!selectedValues.has(value) || value === selectedValueForThisSelect)) {
                    itemsToAdd.push(originalItem.cloneNode(true));
                }
            });

            // Sort items alphabetically before appending
            const sortedItems = itemsToAdd.slice(1).sort((a, b) => {
                const textA = a.textContent.toUpperCase();
                const textB = b.textContent.toUpperCase();
                return (textA < textB) ? -1 : (textA > textB) ? 1 : 0;
            });

            // Prepend the default empty option back to the sorted list
            const finalItems = defaultEmptyOption ? [defaultEmptyOption.cloneNode(true), ...sortedItems] : sortedItems;

            finalItems.forEach(item => select.appendChild(item));
        }

        // Ensure the previously selected value for this select is still selected if it exists
        if (selectedValueForThisSelect) {
            select.value = selectedValueForThisSelect;
        } else {
            // If no value was selected, ensure the placeholder is selected
            select.value = "";
        }
    });
}


// Debounced update
let debounceTimer;
const debouncedUpdate = () => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(updateSelectedValuesAndFilterItems, 200);
};

// Handle multifield item removal
function handleMultifieldItemRemoval() {
    document.addEventListener('click', event => {
        const removeButton = event.target.closest('button[coral-multifield-remove]');
        if (!removeButton) return;

        const multifieldItem = removeButton.closest('coral-multifield-item');
        if (!multifieldItem) return;

        // Get the coral-select within the multifield item being removed
        const selectElementInRemovedItem = multifieldItem.querySelector('coral-select[name^="./audiences/item"]');

        if (selectElementInRemovedItem) {
            const removedValue = selectElementInRemovedItem.selectedItem?.value;

            // If a value was selected in the removed multifield item,
            // ensure it's re-added to the option of available options for other selects.
            if (removedValue) {
                // The `updateSelectedValuesAndFilterItems` function already handles re-adding
                // options that are no longer selected. So, we just need to trigger it.
                debouncedUpdate();
            }
        }
    });
}

// Mutation observer to handle dynamic DOM changes
function initializeObserver() {
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE && node.matches('coral-multifield-item')) {

                    initializeAllSelects();
                    debouncedUpdate();
                }
            });
        });
    });

    observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true
    });
}

// Initialization logic
function init() {
    initializeAllSelects();
    updateSelectedValuesAndFilterItems();
    initializeObserver();
    handleMultifieldItemRemoval();
}

// Run on load or when DOM is ready
if (document.readyState === 'complete') {
    init();
} else {
    window.addEventListener('load', init);
    document.addEventListener('DOMContentLoaded', init);
}
