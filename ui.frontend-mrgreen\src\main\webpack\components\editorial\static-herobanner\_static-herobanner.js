function StaticHeroBanner($cmp) {
    const selectors = {
        mediaContainer  : '.herobanner-media',
        videoContainer  : '.herobanner-video',
        videoElement    : '.herobanner-video video',
        imageContainer  : '.herobanner-image',
        imageElement    : '.herobanner-image img'
    };

    let $mediaWrapper = $cmp.querySelector(selectors.mediaContainer),
        mediaType = $mediaWrapper.dataset.type;

    if (mediaType === 'video') {
        let $videoContainer = $mediaWrapper.querySelector(selectors.videoContainer),
            $videoElement = $mediaWrapper.querySelector(selectors.videoElement);

        //init video desktop or mobile
        initVideoElement($videoContainer, $videoElement);

        //add event listener on resize only for desktop (not devices)
        if (!ConstantsMrGreen.isDevice) {
            let windowWidth = window.innerWidth;
            window.addEventListener('resize', () => {
                if ((windowWidth <= ConstantsMrGreen.breakpoints.mobile) &&
                    (window.innerWidth > ConstantsMrGreen.breakpoints.mobile)) {
                    windowWidth = window.innerWidth;
                    showDesktopVideo($videoElement, $videoContainer);
                } else if ((windowWidth > ConstantsMrGreen.breakpoints.mobile) &&
                    (window.innerWidth <= ConstantsMrGreen.breakpoints.mobile)) {
                    windowWidth = window.innerWidth;
                    showMobileVideo($videoElement, $videoContainer);
                }
            });
        }

    } else if (mediaType === 'image') {
        let $imageContainer = $mediaWrapper.querySelector(selectors.imageContainer),
            $imageElement = $mediaWrapper.querySelector(selectors.imageElement);
        initImageElement($imageContainer, $imageElement);
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                let windowWidth = window.innerWidth;
                if (windowWidth <= ConstantsMrGreen.breakpoints.mobile) {
                    showMobileImage($imageElement, $imageContainer);
                } else if (windowWidth > ConstantsMrGreen.breakpoints.mobile) {
                    showDesktopImage($imageElement, $imageContainer);
                }
            }, 200); // Debounce delay in milliseconds
        });
    }

    function initVideoElement($videoContainer, $videoElement) {
        if (ConstantsMrGreen.isDevice) { //mobile/mobile
            showMobileVideo($videoElement, $videoContainer);
        } else { //not device
            if (window.innerWidth > ConstantsMrGreen.breakpoints.mobile) {
                showDesktopVideo($videoElement, $videoContainer);
            } else {
                showMobileVideo($videoElement, $videoContainer);
            }
        }
    }

    function showDesktopVideo($videoElement, $videoContainer) {
        $videoElement.height = $videoContainer.dataset.dimensionDesktopHeight;
        $videoElement.width = $videoContainer.dataset.dimensionDesktopWidth;
        $videoElement.poster = $videoContainer.dataset.posterDesktop;
        $videoElement.src = $videoContainer.dataset.videoDesktop;
        $videoElement.setAttribute("aria-describedby", $videoContainer.dataset.videoAltDesktop);
    }

    function showMobileVideo($videoElement, $videoContainer) {
        $videoElement.height = $videoContainer.dataset.dimensionMobileHeight;
        $videoElement.width = $videoContainer.dataset.dimensionMobileWidth;
        $videoElement.poster = $videoContainer.dataset.posterMobile;
        $videoElement.src = $videoContainer.dataset.videoMobile;
        $videoElement.setAttribute("aria-describedby", $videoContainer.dataset.videoAltDesktop);
    }

    function initImageElement($imageContainer, $imageElement) {
        if (ConstantsMrGreen.isDevice && window.innerWidth <= ConstantsMrGreen.breakpoints.mobile) { //mobile/mobile
            showMobileImage($imageElement, $imageContainer);
        } else { //not device
            if (window.innerWidth > ConstantsMrGreen.breakpoints.mobile) {
                showDesktopImage($imageElement, $imageContainer);
            } else {
                showMobileImage($imageElement, $imageContainer);
            }
        }
    }

    function showDesktopImage($imageElement, $imageContainer) {
        if($imageContainer.dataset.imageDesktop){
            $imageElement.src = $imageContainer.dataset.imageDesktop;
            $imageElement.alt = $imageContainer.dataset.imageAltDesktop;
            $imageElement.title = $imageContainer.dataset.imageAltDesktop;
        }
    }

    function showMobileImage($imageElement, $imageContainer) {
        if($imageContainer.dataset.imageMobile){
            $imageElement.src = $imageContainer.dataset.imageMobile;
            $imageElement.alt = $imageContainer.dataset.imageAltDesktop;
            $imageElement.title = $imageContainer.dataset.imageAltDesktop;
        }
    }
}

document.querySelectorAll('.static-herobanner-component').forEach(($cmp) => {
    StaticHeroBanner($cmp);
});
