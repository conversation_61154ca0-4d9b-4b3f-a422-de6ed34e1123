<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-use.listItems="${'holdings888/utils/multifield.js' @ multifieldName='listItems'}"/>
<sly data-sly-test.hasContent="${listItems}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<div data-sly-test="${properties.title}" class="container-background-color">
    <div class="container-wrapper main-container">
        <div class="cmp-container">
            <div class="title-component">
                <h1 data-sly-element="${properties.title}"
                    class ="${properties.headingStyle} ${properties.textAlignment} ${properties.fontColor} ${properties.fontFamily} ${properties.fontWeight}">
                    ${properties.title}
                </h1>
            </div>
        </div>
    </div>
</div>

<sly data-sly-test="${hasContent}">
    <div class="promotions-category-icons-anchor-cmp" data-mbox-id="${properties.mboxId}">
        <div class="categories-container" data-sly-list="${listItems}">
            <div class="category-element" data-mbox-id="${item.properties.singleMboxId}">
                <a href="#${item.properties.anchor}" class="category-element__innerLink" tabindex="0" >
                    <img src="${item.properties.iconPath}" class="anchor-icon" aria-hidden="true" alt="anchor icon" title="${item.properties.title}" loading="lazy" />
                    <span class="anchor-title">${item.properties.title}</span>
                </a>
            </div>
        </div>
    </div>
</sly>

<script>
    function removeEmptyIcons() {
        try {
            const icons = document.querySelectorAll(".category-element")
            // we slice to not involve the top games
            const iconsWithTeasers = Array.from(icons).slice(0,-1).filter(el => el.firstChild.tagName !== "DIV")
            const teaserComponents = {}

            iconsWithTeasers.forEach(node => {
                const elHash = node.firstElementChild?.hash;
                if (!elHash) return;

                // we decode for the characters like in DE
                const decodedHash = "#" + decodeURIComponent(elHash.substring(1));
                const hashedEl = document.querySelector(decodedHash)
                teaserComponents[decodedHash] = {
                    node,
                    teasersLength: hashedEl?.querySelectorAll(".swiper .promotions-teaser-component").length // get the number of available teasers
                };
            })

            for (const el in teaserComponents) {
                if (!teaserComponents[el].teasersLength) {
                    teaserComponents[el].node.style.display = "none"; 
                } else {
                    teaserComponents[el].node.style.display = "block"; 
                }
            }
			console.log(teaserComponents)

        } catch (error) {
            console.error(error);
        }

    }
   
		// Wait until carousel initializes specific DOM elements
		function waitForCarouselAndRun(callback, retries = 5, interval = 300) {
			const check = () => {
				const isReady = document.querySelector(".swiper .promotions-teaser-component");
				if (isReady) {
				    console.log(retries);
					callback();
				} else if (retries > 0) {
					setTimeout(() => waitForCarouselAndRun(callback, retries - 1, interval), interval);
				} else {
					console.warn("Carousel content not found — function not run.");
				}
			};
			check();
		}
		
		waitForCarouselAndRun(removeEmptyIcons);

</script>

