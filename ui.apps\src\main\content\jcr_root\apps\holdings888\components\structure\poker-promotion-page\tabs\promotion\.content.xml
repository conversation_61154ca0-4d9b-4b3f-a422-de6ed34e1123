<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    xmlns:granite="http://www.adobe.com/jcr/granite/1.0"
    xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0"
    xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Promotion Card Settings"
    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
    <items jcr:primaryType="nt:unstructured">
        <column
            jcr:primaryType="nt:unstructured"
            sling:resourceType="granite/ui/components/coral/foundation/container">
            <items jcr:primaryType="nt:unstructured">
                <promotion
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Promotion Data"
                    sling:resourceType="granite/ui/components/coral/foundation/form/fieldset">
                    <items jcr:primaryType="nt:unstructured">
                        <title
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="/libs/granite/ui/components/coral/foundation/form/textfield"
                            fieldLabel="Title"
                            name="./title"
                            required="{Boolean}true" />
                        <text
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="/libs/granite/ui/components/coral/foundation/form/textfield"
                            fieldLabel="Text"
                            name="./text"
                            required="{Boolean}true" />
                        <image
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                            fieldLabel="Image"
                            name="./image"
                            rootPath="/content/dam/holdings888"
                            required="{Boolean}true" />
                        <imageAlt
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                            fieldLabel="Image Alt Text"
                            name="./imageAlt"
                            required="{Boolean}true" />
                        <showOn
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                            fieldLabel="Select the component of the Lobby page where the promotion will be displayed."
                            emptyText="Select"
                            name="./showOn">
                            <items jcr:primaryType="nt:unstructured">
                                <nowhere
                                    jcr:primaryType="nt:unstructured"
                                    text="Nowhere"
                                    value="nowhere">
                                    <granite:data
                                        jcr:primaryType="nt:unstructured"
                                        allowBulkEdit="{Boolean}true" />
                                </nowhere>
                                <carousel
                                    jcr:primaryType="nt:unstructured"
                                    text="Promotion Carousel"
                                    value="promotion-carousel">
                                    <granite:data
                                        jcr:primaryType="nt:unstructured"
                                        allowBulkEdit="{Boolean}true" />
                                </carousel>
                                <grid
                                    jcr:primaryType="nt:unstructured"
                                    text="Promotion Grid"
                                    value="promotion-grid">
                                    <granite:data
                                        jcr:primaryType="nt:unstructured"
                                        allowBulkEdit="{Boolean}true" />
                                </grid>
                            </items>
                        </showOn>
                        <showAlsoOnHomepage
                            jcr:primaryType="nt:unstructured"
                            granite:class="grid-showhide-target"
                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                            name="./showAlsoOnHomepage"
                            text="Also display the promotion on the homepage."
                            value="{Boolean}true"
                            uncheckedValue="{Boolean}false">
                            <granite:data
                                jcr:primaryType="nt:unstructured"
                                allowBulkEdit="{Boolean}true" />
                        </showAlsoOnHomepage>
                        <promotionDialog
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/include"
                            path="holdings888/components/structure/page/tabs/promotion" />
                        <textEditor
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/form/select"
                            granite:class="cq-dialog-dropdown-showhide"
                            fieldLabel="Add TnC"
                            name="./textEditor">
                            <granite:data
                                jcr:primaryType="nt:unstructured"
                                cq-dialog-dropdown-showhide-target=".editor-options-showhide-target" />
                            <items
                                jcr:primaryType="nt:unstructured">
                                <richText
                                    jcr:primaryType="nt:unstructured"
                                    text="No"
                                    selected="{Boolean}true"
                                    value="" />
                                <freeHtml
                                    jcr:primaryType="nt:unstructured"
                                    text="Yes"
                                    value="tncDetails" />
                            </items>
                        </textEditor>

                        <tncDetails
                            granite:class="editor-options-showhide-target"
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="granite/ui/components/coral/foundation/container">
                            <items
                                jcr:primaryType="nt:unstructured">
                                <!-- RichText for PC -->
                                <tncText
                                    jcr:primaryType="nt:unstructured"
                                    path="/apps/holdings888/components/casino/dialog-include/rich-text"
                                    sling:resourceType="acs-commons/granite/ui/components/include">
                                    <parameters
                                        jcr:primaryType="nt:unstructured"
                                        textName="tncText"
                                        textLabel="TnC Text For PC"
                                        isRequired="{Boolean}false" />
                                </tncText>
                                <!-- Inner Link Label and Link for Mobile -->
                                <innerLinkLabel
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                    fieldDescription="TnC Link Label for Small Screens"
                                    fieldLabel="TnC Link Label for Small Screens"
                                    name="./innerLinkLabel"
                                    renderReadOnly="{Boolean}true">
                                    <granite:data
                                        jcr:primaryType="nt:unstructured"
                                        cq-msm-lockable="innerLinkLabel" />
                                </innerLinkLabel>
                                <innerLinkHref
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                    fieldDescription="Inner Link Href."
                                    fieldLabel="TnC Link Href for Small Screens"
                                    name="./innerLinkHref"
                                    renderReadOnly="{Boolean}false"
                                    nodeTypes="cq:Page"
                                    rootPath="/content/888poker">
                                    <granite:data
                                        jcr:primaryType="nt:unstructured"
                                        allowBulkEdit="{Boolean}true"
                                        cq-msm-lockable="innerLinkHref" />
                                </innerLinkHref>
                            </items>
                            <granite:data
                                jcr:primaryType="nt:unstructured"
                                showhidetargetvalue="tncDetails" />
                        </tncDetails>
                    </items>
                </promotion>
            </items>
        </column>
    </items>
</jcr:root>