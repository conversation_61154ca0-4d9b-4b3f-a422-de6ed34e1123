package holdings888.core.bean;

import lombok.Data;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;

import javax.inject.Inject;

@Data
@Model(adaptables = Resource.class, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class PromotionItem {

    @Inject
    private String editorialCardType;

    @Inject
    private String mboxId;

    @Inject
    private String image;

    @Inject
    private String imageAlt;

    @Inject
    private String title;

    @Inject
    private String text;

    @Inject
    private String url;

    @Inject
    private String tncText;

    @Inject
    private String innerLinkLabel;

    @Inject
    private String innerLinkHref;

    @Inject
    private String textEditor;

    private Link link;
}
