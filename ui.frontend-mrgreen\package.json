{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Generates an AEM Frontend project with Webpack", "repository": {"type": "git", "url": ""}, "private": true, "main": "src/main/webpack/mrgreen/main.ts", "license": "SEE LICENSE IN LICENSE.txt", "scripts": {"aemfed-Auth": "aemfed -t \"*********************************\" -w \"../ui.apps/src/main/content/jcr_root/\"", "sync": "aemsync -d -p ../ui.apps/src/main/content/jcr_root/apps/holdings888/clientlibs -w ./dist", "lint-fix": "eslint --ext .ts,.tsx,.js,.jsx src --fix", "build": "webpack --config ./webpack.theme.prod.js", "buildDev": "webpack --config ./webpack.theme.dev.js --watch", "startDev": "webpack-dev-server --config ./webpack.theme.dev.js --env writeToDisk"}, "devDependencies": {"@adobe/aem-site-theme-builder": "5.3.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.3.3", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@typescript-eslint/eslint-plugin": "^5.7.0", "@typescript-eslint/parser": "^5.7.0", "acorn": "^6.1.0", "aem-clientlib-generator": "^1.4.3", "aemsync": "^4.0.1", "autoprefixer": "^10.4.19", "browserslist": "latest", "chokidar-cli": "^3.0.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^10.1.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "cssnano": "^5.0.12", "eslint": "^8.4.1", "eslint-webpack-plugin": "^3.1.1", "glob-import-loader": "^1.2.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.4.5", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "postcss-loader": "^8.1.1", "sass": "^1.45.0", "sass-loader": "^12.4.0", "simplebar": "^5.3.9", "source-map-loader": "^0.2.4", "style-loader": "^0.14.1", "swiper": "^8.4.5", "terser-webpack-plugin": "^5.2.5", "ts-loader": "^9.2.6", "tsconfig-paths-webpack-plugin": "^3.2.0", "typescript": "^3.3.3333", "webpack": "^5.91.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.6.0", "webpack-merge": "^5.8.0"}, "browserslist": ["last 2 version", "> 1%"], "overrides": {"@adobe/aem-site-theme-builder": {"browser-sync": "latest"}}}