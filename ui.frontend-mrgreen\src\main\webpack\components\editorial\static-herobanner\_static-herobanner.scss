.static-herobanner-component {
    box-sizing: inherit;
    margin: 0;
    position: relative;
    contain: content;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;

    &.img-padding {
        padding: 6px 6px 0;
    }

    .herobanner-media {
        width: 100%;

        @media (min-width: $sm-air) {
            &.increase-image-height {
                .herobanner-image img {
                    height: 100%;
                    width: 100%;
                    min-height: 34rem;
                    object-fit: cover;
                    margin-bottom: 30px;
                }
            }
        }
        &.increase-image-height {
            .herobanner-image img {
                margin-bottom: 20px;
            }
        }
        @media (min-width: 1280px) and (max-width: 1895px) {
            &.increase-image-height-orbit {
                .herobanner-image img {
                    height: 100%;
                    width: 100%;
                    min-height: 43.53rem;
                    object-fit: cover;
                }
            }
        }

        @media (min-width: $sm-air) {
            &.increase-image-height-200 {
                .herobanner-image img {
                    height: 100%;
                    width: 100%;
                    min-height: 20rem;
                    object-fit: cover;
                }
            }
        }

        @media (min-width: 1280px) and (max-width: 1945px) {
            &.increase-image-height-200-orbit {
                .herobanner-image img {
                    height: 100%;
                    width: 100%;
                    min-height: 23.53rem;
                    object-fit: cover;
                }
            }
        }

        .herobanner-image {
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                background-repeat: no-repeat;
                object-position: top;
                min-height: 100px;
                @media only screen and (min-width: 768px) {
                    min-height: 200px;
                }
            }

            &.img-round-corners {
                img {
                    border-radius: 8px;
                }
            }
        }
    }

    @media only screen and (max-width: 500px) {
        .herobanner-image img {
            min-height: 10rem;
        }
    }
    @media only screen and (max-width: 768px) {
        .herobanner-image img {
            min-height: 10rem;
        }
    }

    .herobanner-video {
        height: 100%;

        video {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }

        &.same-img-desk-mob {
            .herobanner-image img {
                height: 370px;
                width: 100%;
                object-fit: cover;
            }
            @media (max-width: $md-grid) {
                .herobanner-image img {
                    height: 320px;
                    margin-top: 5.5rem;
                }
            }
            @media (max-width: 490px) {
                .herobanner-image img {
                    height: 250px;
                    margin-top: 0;
                }
            }
        }
    }
}

.static-herobanner + .cta {
    .cta-template a {
        margin-bottom: 10px;
    }
}

.static-herobanner + .container .complex-title,
.static-herobanner + .complex-title {
    p {
        @media (max-width: $sm-grid) {
            font-size: 2.3rem !important;
        }
    }
}
