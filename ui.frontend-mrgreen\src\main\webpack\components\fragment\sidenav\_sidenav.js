// Function to handle sidenav toggle
function Sidenav() {
    console.log('Sidenav initialized');
    const menuBtn = document.querySelector('.sidenav-image');
    const sidenav = document.querySelector('.sidenav-wrapper');
    if (!menuBtn || !sidenav) return;

    menuBtn.addEventListener('click', function () {
        sidenav.classList.toggle('sidenav-wrapper--open');
    });
}

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.sidenav-wrapper').forEach(() => {
        Sidenav();
    });
});

document.addEventListener('DOMContentLoaded', () => {
    // Add event listener to the close button
    const closeBtn = document.querySelector('.closebtn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeNav);
    }
});
function closeNav() {
    var sidenav = document.querySelector('.sidenav-wrapper');
    if (sidenav) {
        sidenav.classList.remove('sidenav-wrapper--open');
    }
}
